import { AgentState, MCPMessage } from '../../src/core/types.js';

/**
 * OpenRouter API integration for AG3NTIC Framework tests
 * 
 * Provides integration with OpenRouter's API using the Kimi-K2 model
 * for realistic LLM interactions during testing.
 */

export interface OpenRouterConfig {
  apiKey: string;
  model: string;
  baseUrl?: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  timeout?: number;
}

export interface OpenRouterMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface OpenRouterResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * OpenRouter client for Kimi-K2 model integration
 */
export class OpenRouterClient {
  private readonly config: Required<OpenRouterConfig>;

  constructor(config: OpenRouterConfig) {
    this.config = {
      baseUrl: 'https://openrouter.ai/api/v1',
      maxTokens: 2048,
      temperature: 0.7,
      topP: 0.9,
      timeout: 30000,
      ...config,
    };
  }

  /**
   * Generate completion using Kimi-K2 model
   */
  async generateCompletion(
    messages: OpenRouterMessage[],
    options: Partial<OpenRouterConfig> = {}
  ): Promise<string> {
    const requestConfig = { ...this.config, ...options };
    
    const requestBody = {
      model: requestConfig.model,
      messages,
      max_tokens: requestConfig.maxTokens,
      temperature: requestConfig.temperature,
      top_p: requestConfig.topP,
      stream: false,
    };

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), requestConfig.timeout);

      const response = await fetch(`${requestConfig.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${requestConfig.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://ag3ntic-framework.dev',
          'X-Title': 'AG3NTIC Framework Tests',
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
      }

      const data: OpenRouterResponse = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error('No completion choices returned from OpenRouter');
      }

      return data.choices[0].message.content;

    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error(`OpenRouter request timeout after ${requestConfig.timeout}ms`);
        }
        throw error;
      }
      throw new Error('Unknown error occurred during OpenRouter request');
    }
  }

  /**
   * Convert AG3NTIC messages to OpenRouter format
   */
  convertMessages(messages: MCPMessage[]): OpenRouterMessage[] {
    return messages.map(msg => ({
      role: msg.role as 'system' | 'user' | 'assistant',
      content: msg.content,
    }));
  }

  /**
   * Generate agent response based on state and instructions
   */
  async generateAgentResponse(
    state: AgentState,
    instructions: string,
    context?: string
  ): Promise<string> {
    const messages: OpenRouterMessage[] = [
      {
        role: 'system',
        content: `${instructions}${context ? `\n\nAdditional Context: ${context}` : ''}`
      },
      ...this.convertMessages(state.messages)
    ];

    return this.generateCompletion(messages);
  }

  /**
   * Generate tool usage decision
   */
  async shouldUseTool(
    state: AgentState,
    availableTools: string[],
    instructions: string
  ): Promise<{ shouldUse: boolean; toolName?: string; parameters?: any }> {
    const toolList = availableTools.map(tool => `- ${tool}`).join('\n');
    
    const messages: OpenRouterMessage[] = [
      {
        role: 'system',
        content: `${instructions}

Available tools:
${toolList}

Analyze the conversation and determine if you need to use a tool. Respond with JSON format:
{"shouldUse": true/false, "toolName": "tool_name", "parameters": {...}}

If no tool is needed, respond with: {"shouldUse": false}`
      },
      ...this.convertMessages(state.messages)
    ];

    try {
      const response = await this.generateCompletion(messages, { temperature: 0.3 });
      
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      return { shouldUse: false };
    } catch (error) {
      console.warn('Failed to parse tool usage decision:', error);
      return { shouldUse: false };
    }
  }

  /**
   * Generate evaluation and critique
   */
  async generateEvaluation(
    input: string,
    output: string,
    criteria: string[]
  ): Promise<{
    score: number;
    feedback: string;
    metrics: Record<string, number>;
  }> {
    const criteriaList = criteria.map(c => `- ${c}`).join('\n');
    
    const messages: OpenRouterMessage[] = [
      {
        role: 'system',
        content: `You are an expert evaluator. Assess the quality of the response based on these criteria:

${criteriaList}

Provide your evaluation in JSON format:
{
  "score": 0.0-1.0,
  "feedback": "detailed feedback",
  "metrics": {
    "accuracy": 0.0-1.0,
    "relevance": 0.0-1.0,
    "completeness": 0.0-1.0,
    "clarity": 0.0-1.0
  }
}`
      },
      {
        role: 'user',
        content: `Input: ${input}\n\nOutput: ${output}\n\nPlease evaluate this response.`
      }
    ];

    try {
      const response = await this.generateCompletion(messages, { temperature: 0.2 });
      
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // Fallback evaluation
      return {
        score: 0.7,
        feedback: 'Evaluation completed',
        metrics: {
          accuracy: 0.7,
          relevance: 0.7,
          completeness: 0.7,
          clarity: 0.7
        }
      };
    } catch (error) {
      console.warn('Failed to parse evaluation response:', error);
      return {
        score: 0.5,
        feedback: 'Evaluation failed',
        metrics: {
          accuracy: 0.5,
          relevance: 0.5,
          completeness: 0.5,
          clarity: 0.5
        }
      };
    }
  }
}

/**
 * Create OpenRouter client with Kimi-K2 configuration
 */
export function createOpenRouterClient(apiKey: string): OpenRouterClient {
  return new OpenRouterClient({
    apiKey,
    model: 'moonshotai/kimi-k2',
    maxTokens: 2048,
    temperature: 0.7,
  });
}

/**
 * Test configuration for OpenRouter integration
 */
export const openRouterTestConfig = {
  apiKey: '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',
  model: 'moonshotai/kimi-k2',
  maxTokens: 2048,
  temperature: 0.7,
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
};

// NO MOCK RESPONSES - REAL API ONLY
