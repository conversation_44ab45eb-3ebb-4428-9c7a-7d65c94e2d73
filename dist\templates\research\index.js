"use strict";
// src/templates/research/index.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.createResearchAgent = createResearchAgent;
exports.createSimpleResearcher = createSimpleResearcher;
exports.createDeepResearcher = createDeepResearcher;
const core_1 = require("../../core");
const lib_1 = require("../../lib");
/**
 * Create a Research Agent - specialized for autonomous web research
 *
 * This agent can autonomously browse the web, scrape information,
 * and compile comprehensive research briefs on specific topics.
 *
 * @param options Configuration options
 * @returns Configured Graph ready for execution
 */
function createResearchAgent(options = {}) {
    const { tools = createDefaultResearchTools(), researchNode, maxIterations = 10, researchDepth = 3, enableParallelResearch = false } = options;
    const systemMessage = options.systemMessage || `You are an expert research assistant specializing in comprehensive information gathering and analysis.

Your research process:
1. Analyze the research topic and formulate targeted search queries
2. Search for relevant information from multiple sources
3. Evaluate source credibility and relevance
4. Extract key insights and supporting evidence
5. Identify research gaps and areas needing further investigation
6. Synthesize findings into a comprehensive research brief

Always prioritize accuracy, cite sources, and maintain objectivity in your research.`;
    console.log(`Research agent initialized with depth ${researchDepth}, max ${maxIterations} iterations, parallel: ${enableParallelResearch}`);
    console.log(`System message: ${systemMessage.substring(0, 50)}...`);
    // Create tool execution node
    const toolNode = (0, lib_1.createToolNode)(tools);
    // Create default research node
    const defaultResearchNode = async (state) => {
        const lastMessage = (0, lib_1.getLastMessage)(state);
        // Initialize research
        if (lastMessage?.role === 'user' && !state.researchStatus) {
            const topic = lastMessage.content || state.topic || '';
            const assistantMessage = {
                role: 'assistant',
                content: null,
                tool_calls: [{
                        id: `search_${Date.now()}`,
                        type: 'function',
                        function: {
                            name: 'searchWeb',
                            arguments: JSON.stringify({
                                query: topic,
                                maxResults: 5
                            })
                        }
                    }]
            };
            return {
                ...(0, lib_1.addMessage)(state, assistantMessage),
                topic,
                researchStatus: 'searching',
                iterationCount: 1,
                sources: [],
                researchNotes: [`Starting research on: ${topic}`]
            };
        }
        // Process search results
        if (lastMessage?.role === 'tool' && state.researchStatus === 'searching') {
            try {
                const searchResults = JSON.parse(lastMessage.content);
                const sources = searchResults.results?.map((result, index) => ({
                    url: result.url || `https://example.com/${index}`,
                    title: result.title || `Result ${index + 1}`,
                    type: 'web',
                    relevance: Math.min(5, Math.max(1, 5 - index)), // Decreasing relevance
                    summary: result.snippet || 'No summary available',
                    accessedAt: new Date()
                })) || [];
                // Decide next action based on research depth and iteration count
                const currentIteration = (state.iterationCount || 0) + 1;
                const needsMoreResearch = currentIteration < maxIterations &&
                    sources.length < researchDepth * 3;
                if (needsMoreResearch) {
                    // Continue research with more specific queries
                    const nextQuery = generateNextQuery(state.topic || '', sources);
                    const assistantMessage = {
                        role: 'assistant',
                        content: null,
                        tool_calls: [{
                                id: `search_${Date.now()}`,
                                type: 'function',
                                function: {
                                    name: 'searchWeb',
                                    arguments: JSON.stringify({
                                        query: nextQuery,
                                        maxResults: 3
                                    })
                                }
                            }]
                    };
                    return {
                        ...(0, lib_1.addMessage)(state, assistantMessage),
                        sources: [...(state.sources || []), ...sources],
                        iterationCount: currentIteration,
                        researchNotes: [
                            ...(state.researchNotes || []),
                            `Iteration ${currentIteration}: Found ${sources.length} sources, continuing with: ${nextQuery}`
                        ]
                    };
                }
                else {
                    // Synthesize research findings
                    const allSources = [...(state.sources || []), ...sources];
                    const findings = synthesizeFindings(state.topic || '', allSources);
                    const assistantMessage = {
                        role: 'assistant',
                        content: formatResearchReport(findings)
                    };
                    return {
                        ...(0, lib_1.addMessage)(state, assistantMessage),
                        sources: allSources,
                        findings,
                        researchStatus: 'completed',
                        completenessScore: calculateCompletenessScore(findings),
                        researchNotes: [
                            ...(state.researchNotes || []),
                            `Research completed with ${allSources.length} sources analyzed`
                        ]
                    };
                }
            }
            catch (error) {
                const assistantMessage = {
                    role: 'assistant',
                    content: `Research failed: ${error instanceof Error ? error.message : String(error)}`
                };
                return {
                    ...(0, lib_1.addMessage)(state, assistantMessage),
                    researchStatus: 'failed'
                };
            }
        }
        return {};
    };
    // Use provided research node or default
    const finalResearchNode = researchNode || defaultResearchNode;
    // Build and return the graph
    return new core_1.Graph()
        .addNode('researcher', finalResearchNode)
        .addNode('tools', toolNode)
        .setEntryPoint('researcher')
        .addConditionalEdge('researcher', lib_1.shouldCallTools, {
        'tools': 'tools',
        '__end__': '__END__'
    })
        .addEdge('tools', 'researcher');
}
/**
 * Create default research tools
 */
function createDefaultResearchTools() {
    return {
        searchWeb: async (args) => {
            const { query, maxResults = 5 } = args;
            // Mock search results
            const mockResults = [
                {
                    title: `${query} - Wikipedia`,
                    url: `https://en.wikipedia.org/wiki/${encodeURIComponent(query)}`,
                    snippet: `Comprehensive information about ${query} from Wikipedia...`
                },
                {
                    title: `${query} Research Papers`,
                    url: `https://scholar.google.com/search?q=${encodeURIComponent(query)}`,
                    snippet: `Academic research and papers related to ${query}...`
                },
                {
                    title: `${query} News and Updates`,
                    url: `https://news.google.com/search?q=${encodeURIComponent(query)}`,
                    snippet: `Latest news and developments about ${query}...`
                }
            ].slice(0, maxResults);
            return JSON.stringify({
                query,
                results: mockResults,
                totalResults: mockResults.length
            });
        },
        scrapeWebpage: async (args) => {
            const { url } = args;
            // Mock webpage content
            return JSON.stringify({
                url,
                title: `Content from ${url}`,
                content: `This is mock content scraped from ${url}. In a real implementation, this would contain the actual webpage content.`,
                extractedAt: new Date().toISOString()
            });
        },
        summarizeContent: async (args) => {
            const { content, maxLength = 200 } = args;
            // Mock summarization
            const summary = content.length > maxLength
                ? content.substring(0, maxLength) + '...'
                : content;
            return JSON.stringify({
                originalLength: content.length,
                summary,
                keyPoints: ['Key point 1', 'Key point 2', 'Key point 3']
            });
        }
    };
}
/**
 * Generate next research query based on current findings
 */
function generateNextQuery(topic, sources) {
    const queries = [
        `${topic} latest developments`,
        `${topic} research studies`,
        `${topic} expert opinions`,
        `${topic} case studies`,
        `${topic} best practices`
    ];
    return queries[sources.length % queries.length];
}
/**
 * Synthesize research findings from sources
 */
function synthesizeFindings(topic, sources) {
    const insights = [
        `${topic} is a complex and evolving field`,
        `Multiple perspectives exist on ${topic}`,
        `Recent developments in ${topic} show promising trends`
    ];
    const evidence = sources.slice(0, 5); // Top 5 sources as evidence
    const findings = {
        topic,
        insights,
        evidence,
        confidence: Math.min(5, Math.max(1, sources.length / 2)),
        recommendations: ['Conduct follow-up research', 'Verify findings with additional sources']
    };
    if (sources.length < 5) {
        findings.gaps = ['Limited source diversity', 'Need more recent data'];
    }
    return findings;
}
/**
 * Format research report
 */
function formatResearchReport(findings) {
    return `# Research Report: ${findings.topic}

## Key Insights
${findings.insights.map(insight => `• ${insight}`).join('\n')}

## Evidence Sources
${findings.evidence.map(source => `• ${source.title} (${source.type}) - Relevance: ${source.relevance}/5`).join('\n')}

## Confidence Level
${findings.confidence}/5

${findings.gaps ? `\n## Research Gaps\n${findings.gaps.map(gap => `• ${gap}`).join('\n')}` : ''}

${findings.recommendations ? `\n## Recommendations\n${findings.recommendations.map(rec => `• ${rec}`).join('\n')}` : ''}`;
}
/**
 * Calculate research completeness score
 */
function calculateCompletenessScore(findings) {
    let score = 0;
    score += Math.min(2, findings.insights.length / 2); // Up to 2 points for insights
    score += Math.min(2, findings.evidence.length / 3); // Up to 2 points for evidence
    score += findings.confidence / 5; // Up to 1 point for confidence
    return Math.round(score);
}
/**
 * Create a simple research agent with minimal configuration
 */
function createSimpleResearcher(systemMessage) {
    const options = {};
    if (systemMessage) {
        options.systemMessage = systemMessage;
    }
    return createResearchAgent(options);
}
/**
 * Create a deep research agent for thorough investigation
 */
function createDeepResearcher(maxIterations = 15) {
    return createResearchAgent({
        maxIterations,
        researchDepth: 5,
        systemMessage: `You are a thorough research specialist focused on comprehensive, deep-dive investigations. Leave no stone unturned in your research process.`
    });
}
//# sourceMappingURL=index.js.map