"use strict";
// src/integrations/anthropic/client.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnthropicClient = void 0;
exports.createAnthropicClient = createAnthropicClient;
const agent_helpers_1 = require("../../lib/agent-helpers");
/**
 * Anthropic client wrapper for AG3NTIC
 * This assumes you have the official Anthropic SDK installed: npm install @anthropic-ai/sdk
 */
class AnthropicClient {
    constructor(anthropicInstance) {
        this.anthropic = anthropicInstance;
    }
    /**
     * Create a message using Anthropic Claude
     */
    async createMessage(messages, options = {}) {
        const { model = 'claude-3-5-sonnet-20241022', temperature = 0.7, maxTokens = 4096, systemMessage, tools } = options;
        // Separate system message from conversation messages
        const conversationMessages = messages.filter(msg => msg.role !== 'system');
        const systemContent = systemMessage || messages.find(msg => msg.role === 'system')?.content;
        // Convert MCP messages to Anthropic format
        const anthropicMessages = conversationMessages.map(msg => {
            if (msg.role === 'tool') {
                return {
                    role: 'user',
                    content: [{
                            type: 'tool_result',
                            tool_use_id: msg.tool_call_id,
                            content: msg.content
                        }]
                };
            }
            if (msg.role === 'assistant' && msg.tool_calls) {
                const content = [];
                // Add text content if present
                if (msg.content) {
                    content.push({
                        type: 'text',
                        text: msg.content
                    });
                }
                // Add tool use blocks
                msg.tool_calls.forEach(tc => {
                    content.push({
                        type: 'tool_use',
                        id: tc.id,
                        name: tc.function.name,
                        input: JSON.parse(tc.function.arguments)
                    });
                });
                return {
                    role: 'assistant',
                    content
                };
            }
            return {
                role: msg.role === 'user' ? 'user' : 'assistant',
                content: msg.content || ''
            };
        });
        const requestParams = {
            model,
            messages: anthropicMessages,
            temperature,
            max_tokens: maxTokens
        };
        // Add system message if provided
        if (systemContent) {
            requestParams.system = systemContent;
        }
        // Add tools if provided
        if (tools && tools.length > 0) {
            requestParams.tools = tools;
        }
        try {
            const response = await this.anthropic.messages.create(requestParams);
            // Convert Anthropic response to MCP format
            let content = null;
            let tool_calls = undefined;
            if (response.content) {
                const textBlocks = response.content.filter((block) => block.type === 'text');
                const toolBlocks = response.content.filter((block) => block.type === 'tool_use');
                // Combine text content
                if (textBlocks.length > 0) {
                    content = textBlocks.map((block) => block.text).join('\n');
                }
                // Convert tool use blocks to MCP format
                if (toolBlocks.length > 0) {
                    tool_calls = toolBlocks.map((block) => ({
                        id: block.id,
                        type: 'function',
                        function: {
                            name: block.name,
                            arguments: JSON.stringify(block.input)
                        }
                    }));
                }
            }
            const assistantMessage = {
                role: 'assistant',
                content,
                ...(tool_calls && { tool_calls })
            };
            return assistantMessage;
        }
        catch (error) {
            throw new Error(`Anthropic API error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * Create an agent node that uses Anthropic for LLM calls
     */
    createAgentNode(options = {}) {
        return async (state) => {
            try {
                const assistantMessage = await this.createMessage(state.messages, options);
                return (0, agent_helpers_1.addMessage)(state, assistantMessage);
            }
            catch (error) {
                throw new Error(`Anthropic agent error: ${error instanceof Error ? error.message : String(error)}`);
            }
        };
    }
    /**
     * Test the connection to Anthropic
     */
    async testConnection() {
        try {
            const testMessage = {
                role: 'user',
                content: 'Hello, this is a connection test.'
            };
            await this.createMessage([testMessage], {
                model: 'claude-3-haiku-20240307',
                maxTokens: 10
            });
            return true;
        }
        catch (error) {
            console.error('Anthropic connection test failed:', error);
            return false;
        }
    }
    /**
     * Get available models
     */
    getAvailableModels() {
        return [
            'claude-3-5-sonnet-20241022',
            'claude-3-5-haiku-20241022',
            'claude-3-opus-20240229',
            'claude-3-sonnet-20240229',
            'claude-3-haiku-20240307'
        ];
    }
}
exports.AnthropicClient = AnthropicClient;
/**
 * Factory function to create Anthropic client from API key
 * This requires the Anthropic SDK to be installed
 */
function createAnthropicClient(apiKey) {
    try {
        // Dynamic import to avoid requiring Anthropic SDK as dependency
        const Anthropic = require('@anthropic-ai/sdk');
        const anthropic = new Anthropic({ apiKey });
        return new AnthropicClient(anthropic);
    }
    catch (error) {
        throw new Error('Anthropic SDK not found. Please install it with: npm install @anthropic-ai/sdk');
    }
}
//# sourceMappingURL=client.js.map