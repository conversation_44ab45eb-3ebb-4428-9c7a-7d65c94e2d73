import { AgentState, MCPMessage, MCPAssistantMessage, NodeFunction } from "../../core/types";
/**
 * OpenAI tool definition
 */
export interface OpenAITool {
    type: 'function';
    function: {
        name: string;
        description: string;
        parameters: {
            type: 'object';
            properties: Record<string, any>;
            required?: string[];
        };
    };
}
/**
 * Options for creating an OpenAI agent node
 */
export interface OpenAIAgentOptions {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemMessage?: string;
    tools?: OpenAITool[];
}
/**
 * OpenAI client wrapper for AG3NTIC
 * This assumes you have the official OpenAI SDK installed: npm install openai
 */
export declare class OpenAIClient {
    private openai;
    constructor(openaiInstance: any);
    /**
     * Create a chat completion using OpenAI
     */
    createChatCompletion(messages: MCPMessage[], options?: OpenAIAgentOptions): Promise<MCPAssistantMessage>;
    /**
     * Create an agent node that uses OpenAI for LLM calls
     */
    createAgentNode<TState extends AgentState>(options?: OpenAIAgentOptions): NodeFunction<TState>;
    /**
     * Test the connection to OpenAI
     */
    testConnection(): Promise<boolean>;
    /**
     * Get available models (requires OpenAI SDK)
     */
    getAvailableModels(): Promise<string[]>;
}
/**
 * Factory function to create OpenAI client from API key
 * This requires the OpenAI SDK to be installed
 */
export declare function createOpenAIClient(apiKey: string): OpenAIClient;
//# sourceMappingURL=client.d.ts.map