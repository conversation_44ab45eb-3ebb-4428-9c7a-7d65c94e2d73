import { AgentState, MCPMessage, MCPAssistantMessage, NodeFunction } from "../../core/types";
/**
 * OpenAI-compatible types (mirrors official SDK without requiring it)
 */
export interface OpenAITool {
    type: 'function';
    function: {
        name: string;
        description: string;
        parameters: {
            type: 'object';
            properties: Record<string, any>;
            required?: string[];
        };
        strict?: boolean;
    };
}
export interface OpenAIToolCall {
    id: string;
    type: 'function';
    function: {
        name: string;
        arguments: string;
    };
}
export interface OpenAIMessage {
    role: 'system' | 'user' | 'assistant' | 'tool';
    content: string | null;
    tool_calls?: OpenAIToolCall[];
    tool_call_id?: string;
    name?: string;
}
export interface OpenAIChoice {
    index: number;
    message: OpenAIMessage;
    finish_reason: 'stop' | 'length' | 'tool_calls' | 'content_filter' | null;
}
export interface OpenAIUsage {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
}
export interface OpenAIChatCompletion {
    id: string;
    object: 'chat.completion';
    created: number;
    model: string;
    choices: OpenAIChoice[];
    usage?: OpenAIUsage;
}
export interface OpenAIError extends Error {
    status?: number;
    code?: string;
    type?: string;
}
/**
 * Configuration options for OpenAI client
 */
export interface OpenAIClientConfig {
    apiKey?: string;
    baseURL?: string;
    timeout?: number;
    maxRetries?: number;
    defaultHeaders?: Record<string, string>;
}
/**
 * Options for creating an OpenAI agent node
 */
export interface OpenAIAgentOptions {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemMessage?: string;
    tools?: OpenAITool[];
    toolChoice?: 'auto' | 'none' | {
        type: 'function';
        function: {
            name: string;
        };
    };
    stream?: boolean;
    timeout?: number;
    responseFormat?: {
        type: 'json_object';
    } | {
        type: 'text';
    };
}
/**
 * OpenAI client wrapper for AG3NTIC
 * This assumes you have the official OpenAI SDK installed: npm install openai
 *
 * Features:
 * - Proper TypeScript types
 * - Enhanced error handling
 * - Streaming support
 * - Tool calling with runTools pattern
 * - Timeout configuration
 * - Structured outputs
 */
export declare class OpenAIClient {
    private openai;
    private config;
    constructor(openaiInstance: any, config?: OpenAIClientConfig);
    /**
     * Create a chat completion using OpenAI with enhanced error handling
     */
    createChatCompletion(messages: MCPMessage[], options?: OpenAIAgentOptions): Promise<MCPAssistantMessage>;
    /**
     * Convert MCP messages to OpenAI format
     */
    private convertMCPToOpenAI;
    /**
     * Convert OpenAI response to MCP format
     */
    private convertOpenAIToMCP;
    /**
     * Handle OpenAI API errors with proper typing
     */
    private handleOpenAIError;
    /**
     * Create a streaming chat completion
     */
    createStreamingCompletion(messages: MCPMessage[], options?: OpenAIAgentOptions, onChunk?: (chunk: string) => void): Promise<MCPAssistantMessage>;
    /**
     * Create an agent node that uses OpenAI for LLM calls
     */
    createAgentNode<TState extends AgentState>(options?: OpenAIAgentOptions): NodeFunction<TState>;
    /**
     * Test the connection to OpenAI
     */
    testConnection(): Promise<boolean>;
    /**
     * Get available models (requires OpenAI SDK)
     */
    getAvailableModels(): Promise<string[]>;
}
/**
 * Factory function to create OpenAI client from API key
 * This requires the OpenAI SDK to be installed
 */
export declare function createOpenAIClient(apiKey: string): OpenAIClient;
//# sourceMappingURL=client.d.ts.map