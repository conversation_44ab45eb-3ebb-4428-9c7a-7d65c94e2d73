"use strict";
// src/core/graph.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.Graph = void 0;
const types_1 = require("./types");
class Graph {
    constructor() {
        this.nodes = new Map();
        this.edges = new Map();
        this.conditionalEdges = new Map();
        this.entryPoint = null;
    }
    /**
     * Add a node to the graph
     * @param id Unique identifier for the node
     * @param action Function to execute when this node is reached
     * @returns this (for method chaining)
     */
    addNode(id, action) {
        if (this.nodes.has(id)) {
            throw new Error(`Node with ID "${id}" already exists in the graph.`);
        }
        this.nodes.set(id, action);
        // Automatically set the first node as entry point if none is set
        if (!this.entryPoint) {
            this.entryPoint = id;
            console.log(`Entry point automatically set to the first node added: "${id}"`);
        }
        return this;
    }
    /**
     * Add a direct edge between two nodes
     * @param sourceNodeId The node to transition from
     * @param targetNodeId The node to transition to
     * @returns this (for method chaining)
     */
    addEdge(sourceNodeId, targetNodeId) {
        if (!this.nodes.has(sourceNodeId)) {
            throw new Error(`Source node "${sourceNodeId}" does not exist in the graph.`);
        }
        if (targetNodeId !== types_1.END && !this.nodes.has(targetNodeId)) {
            throw new Error(`Target node "${targetNodeId}" does not exist in the graph.`);
        }
        this.edges.set(sourceNodeId, targetNodeId);
        return this;
    }
    /**
     * Add a conditional edge that routes based on state
     * @param sourceNodeId The node to transition from
     * @param condition Function that returns the routing key
     * @param targetMap Map from routing keys to target node IDs
     * @returns this (for method chaining)
     */
    addConditionalEdge(sourceNodeId, condition, targetMap) {
        if (!this.nodes.has(sourceNodeId)) {
            throw new Error(`Source node "${sourceNodeId}" for conditional edge does not exist.`);
        }
        // Validate that all target nodes exist (except END)
        for (const [key, targetNodeId] of Object.entries(targetMap)) {
            if (targetNodeId !== types_1.END && !this.nodes.has(targetNodeId)) {
                throw new Error(`Target node "${targetNodeId}" for route key "${key}" does not exist in the graph.`);
            }
        }
        this.conditionalEdges.set(sourceNodeId, { condition, targetMap });
        return this;
    }
    /**
     * Set the entry point for graph execution
     * @param nodeId The node to start execution from
     * @returns this (for method chaining)
     */
    setEntryPoint(nodeId) {
        if (!this.nodes.has(nodeId)) {
            throw new Error(`Entry point node "${nodeId}" does not exist.`);
        }
        this.entryPoint = nodeId;
        return this;
    }
    /**
     * Set a finish point (convenience method for adding edge to END)
     * @param nodeId The node that should end the graph
     * @returns this (for method chaining)
     */
    setFinishPoint(nodeId) {
        return this.addEdge(nodeId, types_1.END);
    }
    /**
     * Stream execution of the graph, yielding state after each node
     * @param initialState The starting state for the graph
     * @yields The state after each node execution
     */
    async *stream(initialState) {
        if (!this.entryPoint) {
            throw new Error("Graph has no nodes and therefore no entry point.");
        }
        let currentState = { ...initialState };
        let currentNodeId = this.entryPoint;
        while (currentNodeId && currentNodeId !== types_1.END) {
            const nodeAction = this.nodes.get(currentNodeId);
            if (!nodeAction) {
                throw new Error(`Node with ID "${currentNodeId}" not found in graph.`);
            }
            try {
                const stateUpdate = await nodeAction(currentState);
                currentState = { ...currentState, ...stateUpdate };
                yield currentState;
            }
            catch (error) {
                throw new Error(`Error executing node "${currentNodeId}": ${error instanceof Error ? error.message : String(error)}`);
            }
            // Determine next node
            const conditionalRouter = this.conditionalEdges.get(currentNodeId);
            if (conditionalRouter) {
                try {
                    const routeKey = conditionalRouter.condition(currentState);
                    const nextNodeId = conditionalRouter.targetMap[routeKey];
                    if (nextNodeId === undefined) {
                        throw new Error(`Route key "${routeKey}" from node "${currentNodeId}" is not in target map. Available keys: ${Object.keys(conditionalRouter.targetMap).join(', ')}`);
                    }
                    currentNodeId = nextNodeId;
                }
                catch (error) {
                    throw new Error(`Error in conditional routing from node "${currentNodeId}": ${error instanceof Error ? error.message : String(error)}`);
                }
            }
            else {
                currentNodeId = this.edges.get(currentNodeId) ?? types_1.END;
            }
        }
    }
    /**
     * Get information about the graph structure (for debugging)
     */
    getGraphInfo() {
        return {
            nodes: Array.from(this.nodes.keys()),
            edges: Array.from(this.edges.entries()).map(([from, to]) => ({ from, to })),
            conditionalEdges: Array.from(this.conditionalEdges.entries()).map(([from, { targetMap }]) => ({
                from,
                routes: Object.keys(targetMap)
            })),
            entryPoint: this.entryPoint
        };
    }
}
exports.Graph = Graph;
//# sourceMappingURL=graph.js.map