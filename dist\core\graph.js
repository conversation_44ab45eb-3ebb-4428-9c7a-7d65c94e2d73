"use strict";
// src/core/graph.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.Graph = void 0;
const types_1 = require("./types");
/**
 * Ultra-high-performance Graph implementation
 *
 * Optimizations applied:
 * - Pre-compiled execution paths (no runtime lookups)
 * - Minimal object allocation in hot paths
 * - Direct function calls instead of map operations
 * - Cached conditional evaluations
 * - Zero-cost abstractions for unused features
 */
class Graph {
    constructor() {
        this.nodes = new Map();
        this.compiledPaths = new Map();
        this.entryPoint = null;
        this.isCompiled = false;
    }
    /**
     * Add a node to the graph (optimized for performance)
     */
    addNode(id, action) {
        this.nodes.set(id, action);
        this.isCompiled = false;
        // Auto-set entry point for convenience
        if (!this.entryPoint) {
            this.entryPoint = id;
        }
        return this;
    }
    /**
     * Add a direct edge (optimized compilation)
     */
    addEdge(from, to) {
        const path = this.compiledPaths.get(from) || {
            node: this.nodes.get(from),
            nextNode: null,
            isConditional: false
        };
        this.compiledPaths.set(from, {
            ...path,
            nextNode: to === types_1.END ? null : to
        });
        this.isCompiled = false;
        return this;
    }
    /**
     * Add conditional edge (pre-compiled for speed)
     */
    addConditionalEdge(from, condition, targetMap) {
        // Pre-compile conditional mapping for O(1) lookup
        const optimizedMap = {};
        for (const [key, value] of Object.entries(targetMap)) {
            optimizedMap[key] = value === types_1.END ? null : value;
        }
        this.compiledPaths.set(from, {
            node: this.nodes.get(from),
            nextNode: null,
            isConditional: true,
            conditionalFn: condition,
            conditionalMap: optimizedMap
        });
        this.isCompiled = false;
        return this;
    }
    /**
     * Set entry point (fast validation)
     */
    setEntryPoint(nodeId) {
        this.entryPoint = nodeId;
        return this;
    }
    /**
     * Set finish point (convenience method)
     */
    setFinishPoint(nodeId) {
        return this.addEdge(nodeId, types_1.END);
    }
    /**
     * Compile graph for optimal execution (called automatically)
     */
    compile() {
        if (this.isCompiled)
            return;
        // Ensure all nodes have compiled paths
        for (const [name, nodeFunction] of this.nodes) {
            if (!this.compiledPaths.has(name)) {
                this.compiledPaths.set(name, {
                    node: nodeFunction,
                    nextNode: null,
                    isConditional: false
                });
            }
            else {
                // Update node reference
                const path = this.compiledPaths.get(name);
                path.node = nodeFunction;
            }
        }
        this.isCompiled = true;
    }
    /**
     * Ultra-fast graph execution (primary method)
     */
    async execute(initialState) {
        if (!this.entryPoint) {
            throw new Error('No entry point set');
        }
        this.compile();
        let currentNodeKey = this.entryPoint;
        let state = initialState; // Direct reference for performance
        // Hot path: optimized execution loop
        while (currentNodeKey) {
            const path = this.compiledPaths.get(currentNodeKey);
            if (!path) {
                throw new Error(`Node '${currentNodeKey}' not found`);
            }
            // Execute node function directly
            const result = await path.node(state);
            // Efficient state update - only spread if result has properties
            if (result && typeof result === 'object' && Object.keys(result).length > 0) {
                state = { ...state, ...result };
            }
            // Fast next node resolution
            if (path.isConditional && path.conditionalFn && path.conditionalMap) {
                const conditionResult = path.conditionalFn(state);
                currentNodeKey = path.conditionalMap[conditionResult] || null;
            }
            else {
                currentNodeKey = path.nextNode;
            }
        }
        return state;
    }
    /**
     * Stream execution (for monitoring/debugging)
     */
    async *stream(initialState) {
        if (!this.entryPoint) {
            throw new Error('No entry point set');
        }
        this.compile();
        let currentNodeKey = this.entryPoint;
        let state = initialState;
        while (currentNodeKey) {
            const path = this.compiledPaths.get(currentNodeKey);
            if (!path) {
                throw new Error(`Node '${currentNodeKey}' not found`);
            }
            const result = await path.node(state);
            if (result && typeof result === 'object' && Object.keys(result).length > 0) {
                state = { ...state, ...result };
            }
            yield state;
            // Get next node
            if (path.isConditional && path.conditionalFn && path.conditionalMap) {
                const conditionResult = path.conditionalFn(state);
                currentNodeKey = path.conditionalMap[conditionResult] || null;
            }
            else {
                currentNodeKey = path.nextNode;
            }
        }
    }
    /**
     * Get performance metrics and graph info
     */
    getMetrics() {
        return {
            nodeCount: this.nodes.size,
            pathCount: this.compiledPaths.size,
            isCompiled: this.isCompiled,
            entryPoint: this.entryPoint,
            nodes: Array.from(this.nodes.keys())
        };
    }
    /**
     * Fast validation (minimal checks)
     */
    validate() {
        if (!this.entryPoint || !this.nodes.has(this.entryPoint)) {
            throw new Error('Invalid entry point');
        }
    }
}
exports.Graph = Graph;
//# sourceMappingURL=graph.js.map