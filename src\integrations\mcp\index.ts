// src/integrations/mcp/index.ts

/**
 * Model Context Protocol (MCP) Integration for AG3NTIC
 * 
 * This module provides comprehensive MCP integration, allowing AG3NTIC to:
 * 
 * 1. **Act as an MCP Client**: Connect to external MCP servers to access tools, resources, and prompts
 * 2. **Act as an MCP Server**: Expose AG3NTIC capabilities via the standardized MCP protocol
 * 3. **Seamless Integration**: Work with the entire MCP ecosystem while maintaining AG3NTIC's simplicity
 * 
 * Key Features:
 * - Full JSON-RPC 2.0 compliance
 * - Support for Streamable HTTP and SSE transports
 * - Backwards compatibility with legacy MCP servers
 * - Type-safe integration with AG3NTIC agent templates
 * - Automatic tool function conversion
 * - Resource and prompt management
 * 
 * Usage Examples:
 * 
 * ```typescript
 * // As MCP Client (connect to external servers)
 * const client = await createMCPClient({
 *   name: 'my-agent',
 *   version: '1.0.0',
 *   serverUrl: 'http://localhost:3000/mcp'
 * });
 * 
 * const tools = await client.getToolFunctions();
 * const agent = createExecutorAgent(llmNode, tools);
 * 
 * // As MCP Server (expose AG3NTIC capabilities)
 * const server = new MCPServer({
 *   name: 'ag3ntic-server',
 *   version: '1.0.0'
 * });
 * 
 * server.registerAG3NTICTools(myTools);
 * await server.startHTTP();
 * ```
 */

// Export client functionality
export {
  MCPClient,
  createMCPClient,
  createMCPClientWithFallback,
  type MCPClientConfig
} from './client';

// Export server functionality
export {
  MCPServer,
  createMCPServer,
  createMCPHTTPServer,
  type MCPServerConfig,
  type MCPToolHandler,
  type MCPResourceHandler,
  type MCPPromptHandler
} from './server';

// Re-export MCP types from core
export {
  // Protocol types
  type MCPRequest,
  type MCPResponse,
  type MCPNotification,
  type MCPProtocolMessage,
  
  // Content types
  type MCPContent,
  type MCPTextContent,
  type MCPImageContent,
  type MCPResourceContent,
  
  // Feature types
  type MCPTool,
  type MCPToolResult,
  type MCPResource,
  type MCPResourceContents,
  type MCPPrompt,
  type MCPPromptResult,
  type MCPPromptMessage
} from '../../core/types';

/**
 * MCP Integration Utilities
 */

/**
 * Check if MCP SDK is available
 */
export async function isMCPAvailable(): Promise<boolean> {
  try {
    await import('@modelcontextprotocol/sdk/client/index.js');
    return true;
  } catch {
    return false;
  }
}

/**
 * Get MCP SDK version if available
 */
export async function getMCPVersion(): Promise<string | null> {
  try {
    const pkg = await import('@modelcontextprotocol/sdk/package.json');
    return pkg.version;
  } catch {
    return null;
  }
}

/**
 * Create an MCP-enabled tool function that can work with both
 * AG3NTIC's tool system and MCP protocol
 */
export function createMCPCompatibleTool(
  name: string,
  description: string,
  inputSchema: any,
  handler: (args: any) => Promise<any>
) {
  return {
    // AG3NTIC tool function
    toolFunction: handler,
    
    // MCP tool definition
    mcpDefinition: {
      name,
      description,
      inputSchema
    },
    
    // MCP tool handler
    mcpHandler: async (args: Record<string, unknown>) => {
      try {
        const result = await handler(args);
        return {
          content: [{
            type: "text" as const,
            text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text" as const,
            text: `Error: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  };
}

/**
 * Convert AG3NTIC tools to MCP format
 */
export function convertAG3NTICToolsToMCP(tools: Record<string, (args: any) => any>) {
  const mcpTools: Array<{
    name: string;
    definition: any;
    handler: any;
  }> = [];

  for (const [name, toolFunction] of Object.entries(tools)) {
    mcpTools.push({
      name,
      definition: {
        name,
        description: `AG3NTIC tool: ${name}`,
        inputSchema: {
          type: "object",
          properties: {},
          required: []
        }
      },
      handler: async (args: Record<string, unknown>) => {
        try {
          const result = await Promise.resolve(toolFunction(args));
          return {
            content: [{
              type: "text" as const,
              text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
            }]
          };
        } catch (error) {
          return {
            content: [{
              type: "text" as const,
              text: `Error: ${error instanceof Error ? error.message : String(error)}`
            }],
            isError: true
          };
        }
      }
    });
  }

  return mcpTools;
}

/**
 * Create a unified MCP + AG3NTIC agent that can use both local tools
 * and remote MCP server tools
 */
export async function createMCPEnabledAgent(config: {
  localTools?: Record<string, (args: any) => any>;
  mcpServers?: Array<{
    name: string;
    url: string;
    transport?: 'http' | 'sse';
  }>;
  agentConfig: {
    name: string;
    version: string;
  };
}) {
  const allTools: Record<string, (args: any) => any> = { ...config.localTools };
  const mcpClients: any[] = [];

  // Connect to MCP servers and get their tools
  if (config.mcpServers) {
    for (const serverConfig of config.mcpServers) {
      try {
        // Dynamic import to avoid requiring MCP SDK if not used
        const { createMCPClient } = await import('./client');

        const client = await createMCPClient({
          name: config.agentConfig.name,
          version: config.agentConfig.version,
          serverUrl: serverConfig.url,
          transport: serverConfig.transport || 'http'
        });

        const serverTools = await client.getToolFunctions();

        // Prefix server tools with server name to avoid conflicts
        for (const [toolName, toolFunction] of Object.entries(serverTools)) {
          const prefixedName = `${serverConfig.name}_${toolName}`;
          allTools[prefixedName] = toolFunction as (args: any) => any;
        }

        mcpClients.push(client);
        console.log(`✅ Connected to MCP server: ${serverConfig.name}`);
      } catch (error) {
        console.warn(`⚠️  Failed to connect to MCP server ${serverConfig.name}:`, error);
      }
    }
  }

  return {
    tools: allTools,
    mcpClients,
    async cleanup() {
      for (const client of mcpClients) {
        await client.disconnect();
      }
    }
  };
}

/**
 * MCP Integration Status
 */
export async function getMCPIntegrationStatus() {
  const available = await isMCPAvailable();
  const version = await getMCPVersion();
  
  return {
    available,
    version,
    features: {
      client: available,
      server: available,
      streamableHttp: available,
      sse: available
    }
  };
}
