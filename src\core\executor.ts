// src/core/executor.ts

import { Graph } from "./graph";
import { AgentState } from "./types";

/**
 * Ultra-lightweight Executor optimized for maximum performance
 *
 * Optimizations:
 * - Direct delegation to graph (zero overhead)
 * - Minimal method calls and object allocation
 * - Fast path for common operations
 */
export class Executor<TState extends AgentState> {
  constructor(private readonly graph: Graph<TState>) {}

  /**
   * Execute graph (ultra-fast direct delegation)
   */
  execute(initialState: TState): Promise<TState> {
    return this.graph.execute(initialState);
  }

  /**
   * Execute with history collection (optimized)
   */
  async executeWithHistory(initialState: TState): Promise<TState[]> {
    const states: TState[] = [initialState];

    for await (const state of this.graph.stream(initialState)) {
      states.push(state);
    }

    return states;
  }

  /**
   * Execute with callback (optimized for monitoring)
   */
  async executeWithCallback(
    initialState: TState,
    onStateChange: (state: TState, stepNumber: number) => void | Promise<void>
  ): Promise<TState> {
    let lastState: TState = initialState;
    let stepNumber = 0;

    for await (const state of this.graph.stream(initialState)) {
      stepNumber++;
      await onStateChange(state, stepNumber);
      lastState = state;
    }

    return lastState;
  }

  /**
   * Stream execution (direct delegation)
   */
  stream(initialState: TState): AsyncGenerator<TState> {
    return this.graph.stream(initialState);
  }

  /**
   * Get graph instance
   */
  getGraph(): Graph<TState> {
    return this.graph;
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    return this.graph.getMetrics();
  }

  /**
   * Fast validation
   */
  validate(): void {
    this.graph.validate();
  }
}
