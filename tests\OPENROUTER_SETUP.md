# OpenRouter Integration Setup for AG3NTIC Tests

This guide explains how to run the AG3NTIC framework tests with OpenRouter's Kimi-K2 model for realistic LLM interactions.

## 🔑 API Configuration

The tests are pre-configured with your OpenRouter credentials:

- **Model**: `moonshotai/kimi-k2`
- **API Key**: `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`
- **Base URL**: `https://openrouter.ai/api/v1`

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Run OpenRouter Demo Tests
```bash
# Run the comprehensive OpenRouter integration demo
npm run test:openrouter

# Run all tests with OpenRouter integration
npm test

# Run specific test categories
npm run test:agents        # Individual agent tests
npm run test:multi-agent   # Multi-agent system tests
npm run test:integration   # End-to-end integration tests
```

### 3. View Test Results
```bash
# Run with coverage report
npm run test:coverage

# Run in watch mode for development
npm run test:watch
```

## 🎯 Test Features with OpenRouter

### **Intelligent Agent Responses**
- Real LLM-powered conversations with Kimi-K2
- Context-aware responses based on agent roles
- Natural language understanding and generation

### **Smart Tool Usage**
- Automatic tool selection based on user requests
- Intelligent parameter extraction from natural language
- Tool execution with real reasoning

### **Advanced Capabilities**
- Multi-step reasoning and problem solving
- Strategic planning and analysis
- Data interpretation and insights
- Complex workflow orchestration

## 📊 Test Categories

### **1. OpenRouter Demo Tests** (`openrouter-demo.test.ts`)
```bash
npm run test:openrouter
```

**Features Tested:**
- ✅ Intelligent conversation with Kimi-K2
- ✅ Smart tool usage and reasoning
- ✅ Advanced data analysis
- ✅ Comprehensive project planning
- ✅ Complex multi-step reasoning
- ✅ API performance and reliability
- ✅ Model capability validation

### **2. Enhanced Agent Tests** (`agents/*.test.ts`)
```bash
npm run test:agents
```

**Features Tested:**
- ✅ Executor agents with real LLM responses
- ✅ Planner agents with strategic thinking
- ✅ Analyst agents with data insights
- ✅ Tool integration with intelligent selection
- ✅ Memory persistence across conversations

### **3. Multi-Agent Systems** (`multi-agent/*.test.ts`)
```bash
npm run test:multi-agent
```

**Features Tested:**
- ✅ Agent coordination with real reasoning
- ✅ Intelligent task routing and handoffs
- ✅ Collaborative problem solving
- ✅ Consensus building and decision making
- ✅ Swarm intelligence behaviors

## 🔧 Configuration Options

### **Fallback Behavior**
If OpenRouter is unavailable, tests automatically fall back to mock responses:

```typescript
// Tests will show this message and continue with mocks
console.log('⏭️ Skipping OpenRouter test - using mock responses');
```

### **Performance Settings**
```typescript
export const openRouterTestConfig = {
  apiKey: '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',
  model: 'moonshotai/kimi-k2',
  maxTokens: 2048,
  temperature: 0.7,
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
};
```

### **Custom Test Scenarios**
You can create custom test scenarios by modifying the agent instructions:

```typescript
const customAgent = createEnhancedAgent({
  name: 'Custom Agent',
  instructions: 'Your custom instructions here...',
  tools: [/* your tools */],
  maxIterations: 5,
});
```

## 📈 Expected Results

### **Performance Benchmarks**
- **Simple Conversations**: < 3 seconds
- **Tool Usage**: < 5 seconds  
- **Complex Reasoning**: < 10 seconds
- **Multi-Agent Coordination**: < 15 seconds

### **Quality Indicators**
- **Response Length**: > 50 characters for meaningful responses
- **Tool Selection**: Accurate tool choice based on context
- **Reasoning Quality**: Logical, coherent, and relevant responses
- **Error Handling**: Graceful fallback to mock responses

### **Success Criteria**
- ✅ All tests pass with real LLM integration
- ✅ Intelligent responses demonstrate understanding
- ✅ Tools are used appropriately and effectively
- ✅ Multi-agent coordination shows emergent behavior
- ✅ Performance meets acceptable thresholds

## 🐛 Troubleshooting

### **Common Issues**

1. **API Connection Failed**
   ```
   ⚠️ OpenRouter connection failed, using mock responses
   ```
   - Check internet connection
   - Verify API key is correct
   - Ensure OpenRouter service is available

2. **Timeout Errors**
   ```
   OpenRouter request timeout after 30000ms
   ```
   - Increase timeout in configuration
   - Check network stability
   - Try reducing request complexity

3. **Rate Limiting**
   ```
   OpenRouter API error: 429 - Rate limit exceeded
   ```
   - Wait before retrying
   - Reduce concurrent test execution
   - Check OpenRouter usage limits

### **Debug Mode**
Run tests with verbose output:
```bash
npm test -- --verbose --detectOpenHandles
```

### **Manual API Testing**
Test the OpenRouter connection directly:
```bash
curl -X POST "https://openrouter.ai/api/v1/chat/completions" \
  -H "Authorization: Bearer 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" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "moonshotai/kimi-k2",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

## 🎉 Success Examples

### **Intelligent Conversation**
```
🤖 Kimi-K2 Response: Hello! I'm an AI assistant powered by Kimi-K2, designed to help with complex tasks through the AG3NTIC framework. I can understand natural language, use tools intelligently, and engage in sophisticated reasoning...
```

### **Smart Tool Usage**
```
🧮 Calculator Tool Result: I'll solve that math problem for you using the calculator tool. 15 * 23 + 47 - 12 = 380. The calculation breaks down as: first 15×23=345, then 345+47=392, and finally 392-12=380.
```

### **Strategic Planning**
```
📋 Project Plan: I've created a comprehensive 6-month development plan for your social media platform. The project will proceed through 9 phases: Research, Analysis, Planning, Design, Development, Testing, Integration, Deployment, and Monitoring...
```

## 🚀 Next Steps

1. **Run the Demo**: Start with `npm run test:openrouter`
2. **Explore Results**: Review the intelligent responses and tool usage
3. **Customize Tests**: Modify agent instructions for your use cases
4. **Scale Up**: Use the patterns for production applications

The OpenRouter integration demonstrates the full potential of the AG3NTIC framework with real LLM intelligence! 🎊
