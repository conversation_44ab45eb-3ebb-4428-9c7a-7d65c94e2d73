"use strict";
// src/integrations/mcp/server.ts
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPServer = void 0;
exports.createMCPServer = createMCPServer;
exports.createMCPHTTPServer = createMCPHTTPServer;
/**
 * Check if MCP SDK is available
 */
async function checkMCPSDK() {
    try {
        await Promise.resolve().then(() => __importStar(require('@modelcontextprotocol/sdk/server/mcp.js')));
    }
    catch (error) {
        throw new Error('MCP SDK not found. Install with: npm install @modelcontextprotocol/sdk\n' +
            'Or use AG3NTIC without MCP server capabilities.');
    }
}
/**
 * MCP Server for exposing AG3NTIC capabilities as an MCP server
 *
 * This allows AG3NTIC to:
 * - Expose its tools, resources, and prompts via MCP protocol
 * - Be consumed by other AI systems and MCP clients
 * - Provide standardized access to AG3NTIC capabilities
 */
class MCPServer {
    constructor(config) {
        this.tools = new Map();
        this.resources = new Map();
        this.prompts = new Map();
        this.running = false;
        this.config = {
            port: 3000,
            host: 'localhost',
            enableCORS: true,
            enableAuth: false,
            capabilities: {
                tools: true,
                resources: true,
                prompts: true,
                logging: true
            },
            ...config
        };
    }
    /**
     * Register a tool with the MCP server
     */
    registerTool(name, definition, handler) {
        this.tools.set(name, { definition, handler });
        console.log(`🔧 Registered MCP tool: ${name}`);
    }
    /**
     * Register a resource with the MCP server
     */
    registerResource(name, definition, handler) {
        this.resources.set(name, { definition, handler });
        console.log(`📄 Registered MCP resource: ${name}`);
    }
    /**
     * Register a prompt with the MCP server
     */
    registerPrompt(name, definition, handler) {
        this.prompts.set(name, { definition, handler });
        console.log(`💬 Registered MCP prompt: ${name}`);
    }
    /**
     * Start the MCP server
     */
    async start() {
        try {
            // Check if MCP SDK is available
            await checkMCPSDK();
            // Dynamic import to avoid requiring MCP SDK if not used
            const { McpServer } = await Promise.resolve().then(() => __importStar(require('@modelcontextprotocol/sdk/server/mcp.js')));
            this.server = new McpServer({
                name: this.config.name,
                version: this.config.version
            });
            // Register all tools
            for (const [name, { definition, handler }] of this.tools) {
                this.server.registerTool(name, {
                    title: definition.name,
                    description: definition.description,
                    inputSchema: definition.inputSchema
                }, async (args) => {
                    try {
                        const result = await handler(args);
                        return result;
                    }
                    catch (error) {
                        console.error(`❌ Tool ${name} failed:`, error);
                        return {
                            content: [{
                                    type: "text",
                                    text: `Error: ${error instanceof Error ? error.message : String(error)}`
                                }],
                            isError: true
                        };
                    }
                });
            }
            // Register all resources
            for (const [name, { definition, handler }] of this.resources) {
                this.server.registerResource(name, definition.uri, {
                    title: definition.name,
                    description: definition.description,
                    mimeType: definition.mimeType
                }, async (uri) => {
                    try {
                        const result = await handler(uri);
                        return result;
                    }
                    catch (error) {
                        console.error(`❌ Resource ${name} failed:`, error);
                        return {
                            contents: [{
                                    uri,
                                    text: `Error: ${error instanceof Error ? error.message : String(error)}`,
                                    mimeType: 'text/plain'
                                }]
                        };
                    }
                });
            }
            // Register all prompts
            for (const [name, { definition, handler }] of this.prompts) {
                this.server.registerPrompt(name, {
                    title: definition.name,
                    description: definition.description,
                    argsSchema: definition.arguments ?
                        definition.arguments.reduce((schema, arg) => {
                            schema[arg.name] = { type: 'string', description: arg.description };
                            return schema;
                        }, {}) : {}
                }, async (args) => {
                    try {
                        const result = await handler(args);
                        return result;
                    }
                    catch (error) {
                        console.error(`❌ Prompt ${name} failed:`, error);
                        return {
                            description: `Error in prompt ${name}`,
                            messages: [{
                                    role: 'assistant',
                                    content: {
                                        type: 'text',
                                        text: `Error: ${error instanceof Error ? error.message : String(error)}`
                                    }
                                }]
                        };
                    }
                });
            }
            // Start with stdio transport by default
            const { StdioServerTransport } = await Promise.resolve().then(() => __importStar(require('@modelcontextprotocol/sdk/server/stdio.js')));
            const transport = new StdioServerTransport();
            await this.server.connect(transport);
            this.running = true;
            console.log(`🚀 MCP Server "${this.config.name}" started with stdio transport`);
        }
        catch (error) {
            console.error('❌ Failed to start MCP server:', error);
            throw error;
        }
    }
    /**
     * Start HTTP server (for web-based clients)
     */
    async startHTTP() {
        try {
            // Check if MCP SDK is available
            await checkMCPSDK();
            // Dynamic import Express and MCP HTTP transport
            const express = await Promise.resolve().then(() => __importStar(require('express')));
            const { StreamableHTTPServerTransport } = await Promise.resolve().then(() => __importStar(require('@modelcontextprotocol/sdk/server/streamableHttp.js')));
            const app = express.default();
            app.use(express.default.json());
            // Enable CORS if configured
            if (this.config.enableCORS) {
                try {
                    const cors = await Promise.resolve().then(() => __importStar(require('cors')));
                    app.use(cors.default({
                        origin: '*',
                        exposedHeaders: ['Mcp-Session-Id'],
                        allowedHeaders: ['Content-Type', 'mcp-session-id'],
                    }));
                }
                catch (error) {
                    console.warn('⚠️  CORS package not found. Install with: npm install cors @types/cors');
                    console.warn('⚠️  Continuing without CORS support...');
                }
            }
            // Map to store transports by session ID
            const transports = {};
            // Handle MCP requests
            app.all('/mcp', async (req, res) => {
                const sessionId = req.headers['mcp-session-id'];
                let transport;
                if (sessionId && transports[sessionId]) {
                    transport = transports[sessionId];
                }
                else {
                    const { randomUUID } = await Promise.resolve().then(() => __importStar(require('node:crypto')));
                    transport = new StreamableHTTPServerTransport({
                        sessionIdGenerator: () => randomUUID(),
                        onsessioninitialized: (sessionId) => {
                            transports[sessionId] = transport;
                        }
                    });
                    transport.onclose = () => {
                        if (transport.sessionId) {
                            delete transports[transport.sessionId];
                        }
                    };
                    await this.server.connect(transport);
                }
                await transport.handleRequest(req, res, req.body);
            });
            // Start the HTTP server
            const server = app.listen(this.config.port, this.config.host, () => {
                console.log(`🌐 MCP HTTP Server listening on http://${this.config.host}:${this.config.port}/mcp`);
            });
            this.running = true;
            return server;
        }
        catch (error) {
            console.error('❌ Failed to start MCP HTTP server:', error);
            throw error;
        }
    }
    /**
     * Stop the MCP server
     */
    async stop() {
        if (this.server && this.running) {
            await this.server.close();
            this.running = false;
            console.log('🛑 MCP Server stopped');
        }
    }
    /**
     * Check if server is running
     */
    isRunning() {
        return this.running;
    }
    /**
     * Get server statistics
     */
    getStats() {
        return {
            name: this.config.name,
            version: this.config.version,
            running: this.running,
            tools: this.tools.size,
            resources: this.resources.size,
            prompts: this.prompts.size,
            capabilities: this.config.capabilities
        };
    }
    /**
     * Register AG3NTIC tool functions as MCP tools
     */
    registerAG3NTICTools(tools) {
        for (const [name, toolFunction] of Object.entries(tools)) {
            this.registerTool(name, {
                name,
                description: `AG3NTIC tool: ${name}`,
                inputSchema: {
                    type: "object",
                    properties: {},
                    required: []
                }
            }, async (args) => {
                try {
                    const result = await Promise.resolve(toolFunction(args));
                    // Convert result to MCP format
                    const content = [{
                            type: "text",
                            text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
                        }];
                    return { content };
                }
                catch (error) {
                    return {
                        content: [{
                                type: "text",
                                text: `Error: ${error instanceof Error ? error.message : String(error)}`
                            }],
                        isError: true
                    };
                }
            });
        }
    }
}
exports.MCPServer = MCPServer;
/**
 * Create and start an MCP server
 */
async function createMCPServer(config) {
    const server = new MCPServer(config);
    await server.start();
    return server;
}
/**
 * Create and start an MCP HTTP server
 */
async function createMCPHTTPServer(config) {
    const server = new MCPServer(config);
    await server.startHTTP();
    return server;
}
//# sourceMappingURL=server.js.map