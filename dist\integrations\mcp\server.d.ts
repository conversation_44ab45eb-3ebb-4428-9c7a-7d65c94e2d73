import { MCP<PERSON>ool, MCP<PERSON>ool<PERSON>esult, MCPResource, MCPResourceContents, MCPPrompt, MCPPromptResult } from '../../core/types';
/**
 * MCP Server Configuration
 */
export interface MCPServerConfig {
    name: string;
    version: string;
    port?: number;
    host?: string;
    enableCORS?: boolean;
    enableAuth?: boolean;
    capabilities?: {
        tools?: boolean;
        resources?: boolean;
        prompts?: boolean;
        logging?: boolean;
    };
}
/**
 * Tool handler function type
 */
export type MCPToolHandler = (args: Record<string, unknown>) => Promise<MCPToolResult>;
/**
 * Resource handler function type
 */
export type MCPResourceHandler = (uri: string) => Promise<MCPResourceContents>;
/**
 * Prompt handler function type
 */
export type MCPPromptHandler = (args: Record<string, unknown>) => Promise<MCPPromptResult>;
/**
 * MCP Server for exposing AG3NTIC capabilities as an MCP server
 *
 * This allows AG3NTIC to:
 * - Expose its tools, resources, and prompts via MCP protocol
 * - Be consumed by other AI systems and MCP clients
 * - Provide standardized access to AG3NTIC capabilities
 */
export declare class MCPServer {
    private config;
    private server;
    private tools;
    private resources;
    private prompts;
    private running;
    constructor(config: MCPServerConfig);
    /**
     * Register a tool with the MCP server
     */
    registerTool(name: string, definition: MCPTool, handler: MCPToolHandler): void;
    /**
     * Register a resource with the MCP server
     */
    registerResource(name: string, definition: MCPResource, handler: MCPResourceHandler): void;
    /**
     * Register a prompt with the MCP server
     */
    registerPrompt(name: string, definition: MCPPrompt, handler: MCPPromptHandler): void;
    /**
     * Start the MCP server
     */
    start(): Promise<void>;
    /**
     * Start HTTP server (for web-based clients)
     */
    startHTTP(): Promise<void>;
    /**
     * Stop the MCP server
     */
    stop(): Promise<void>;
    /**
     * Check if server is running
     */
    isRunning(): boolean;
    /**
     * Get server statistics
     */
    getStats(): {
        name: string;
        version: string;
        running: boolean;
        tools: number;
        resources: number;
        prompts: number;
        capabilities: {
            tools?: boolean;
            resources?: boolean;
            prompts?: boolean;
            logging?: boolean;
        } | undefined;
    };
    /**
     * Register AG3NTIC tool functions as MCP tools
     */
    registerAG3NTICTools(tools: Record<string, (args: any) => any>): void;
}
/**
 * Create and start an MCP server
 */
export declare function createMCPServer(config: MCPServerConfig): Promise<MCPServer>;
/**
 * Create and start an MCP HTTP server
 */
export declare function createMCPHTTPServer(config: MCPServerConfig): Promise<MCPServer>;
//# sourceMappingURL=server.d.ts.map