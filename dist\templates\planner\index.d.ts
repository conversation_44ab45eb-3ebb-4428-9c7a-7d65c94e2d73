import { Graph, AgentState, NodeFunction } from '../../core';
/**
 * Configuration options for the Planner Agent
 */
export interface PlannerAgentOptions {
    /** System message for planning */
    systemMessage?: string;
    /** Custom planner node function */
    plannerNode?: NodeFunction<any>;
    /** Maximum number of steps in a plan */
    maxSteps?: number;
    /** Planning strategy */
    strategy?: 'sequential' | 'parallel' | 'hierarchical';
}
/**
 * Represents a single step in a plan
 */
export interface PlanStep {
    /** Unique identifier for the step */
    id: string;
    /** Description of what needs to be done */
    description: string;
    /** Type of step (task, decision, etc.) */
    type: 'task' | 'decision' | 'review' | 'synthesis';
    /** Dependencies (step IDs that must complete first) */
    dependencies?: string[];
    /** Estimated effort/complexity (1-5) */
    complexity?: number;
    /** Required tools or capabilities */
    requiredCapabilities?: string[];
    /** Expected output format */
    expectedOutput?: string;
}
/**
 * State interface for Planner Agent
 */
export interface PlannerAgentState extends AgentState {
    /** The high-level goal to plan for */
    goal?: string;
    /** Generated plan */
    plan?: PlanStep[];
    /** Planning status */
    planningStatus?: 'analyzing' | 'planning' | 'completed' | 'failed';
    /** Planning metadata */
    planningMetadata?: {
        strategy: string;
        estimatedDuration?: string;
        complexity: number;
        requiredCapabilities: string[];
    };
}
/**
 * Create a Planner Agent - breaks down complex goals into actionable steps
 *
 * This agent specializes in taking high-level objectives and creating
 * detailed, step-by-step plans that other agents can execute.
 *
 * @param options Configuration options
 * @returns Configured Graph ready for execution
 */
export declare function createPlannerAgent<TState extends PlannerAgentState = PlannerAgentState>(options?: PlannerAgentOptions): Graph<TState>;
/**
 * Create a simple planner with minimal configuration
 */
export declare function createSimplePlanner<TState extends PlannerAgentState = PlannerAgentState>(systemMessage?: string): Graph<TState>;
/**
 * Create a hierarchical planner for complex, multi-level planning
 */
export declare function createHierarchicalPlanner<TState extends PlannerAgentState = PlannerAgentState>(maxSteps?: number): Graph<TState>;
//# sourceMappingURL=index.d.ts.map