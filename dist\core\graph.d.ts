import { AgentState, NodeFunction, ConditionalFunction, EdgeTargetMap } from "./types";
export declare class Graph<TState extends AgentState> {
    private nodes;
    private edges;
    private conditionalEdges;
    private entryPoint;
    /**
     * Add a node to the graph
     * @param id Unique identifier for the node
     * @param action Function to execute when this node is reached
     * @returns this (for method chaining)
     */
    addNode(id: string, action: NodeFunction<TState>): this;
    /**
     * Add a direct edge between two nodes
     * @param sourceNodeId The node to transition from
     * @param targetNodeId The node to transition to
     * @returns this (for method chaining)
     */
    addEdge(sourceNodeId: string, targetNodeId: string): this;
    /**
     * Add a conditional edge that routes based on state
     * @param sourceNodeId The node to transition from
     * @param condition Function that returns the routing key
     * @param targetMap Map from routing keys to target node IDs
     * @returns this (for method chaining)
     */
    addConditionalEdge(sourceNodeId: string, condition: ConditionalFunction<TState>, targetMap: EdgeTargetMap): this;
    /**
     * Set the entry point for graph execution
     * @param nodeId The node to start execution from
     * @returns this (for method chaining)
     */
    setEntryPoint(nodeId: string): this;
    /**
     * Set a finish point (convenience method for adding edge to END)
     * @param nodeId The node that should end the graph
     * @returns this (for method chaining)
     */
    setFinishPoint(nodeId: string): this;
    /**
     * Stream execution of the graph, yielding state after each node
     * @param initialState The starting state for the graph
     * @yields The state after each node execution
     */
    stream(initialState: TState): AsyncGenerator<TState>;
    /**
     * Get information about the graph structure (for debugging)
     */
    getGraphInfo(): {
        nodes: string[];
        edges: Array<{
            from: string;
            to: string;
        }>;
        conditionalEdges: Array<{
            from: string;
            routes: string[];
        }>;
        entryPoint: string | null;
    };
}
//# sourceMappingURL=graph.d.ts.map