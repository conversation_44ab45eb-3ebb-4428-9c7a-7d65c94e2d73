import { AgentState, NodeFunction, ConditionalFunction, EdgeTargetMap } from "./types";
/**
 * Ultra-high-performance Graph implementation
 *
 * Optimizations applied:
 * - Pre-compiled execution paths (no runtime lookups)
 * - Minimal object allocation in hot paths
 * - Direct function calls instead of map operations
 * - Cached conditional evaluations
 * - Zero-cost abstractions for unused features
 */
export declare class Graph<TState extends AgentState> {
    private readonly nodes;
    private readonly compiledPaths;
    private entryPoint;
    private isCompiled;
    /**
     * Add a node to the graph (optimized for performance)
     */
    addNode(id: string, action: NodeFunction<TState>): this;
    /**
     * Add a direct edge (optimized compilation)
     */
    addEdge(from: string, to: string): this;
    /**
     * Add conditional edge (pre-compiled for speed)
     */
    addConditionalEdge(from: string, condition: ConditionalFunction<TState>, targetMap: EdgeTargetMap): this;
    /**
     * Set entry point (fast validation)
     */
    setEntryPoint(nodeId: string): this;
    /**
     * Set finish point (convenience method)
     */
    setFinishPoint(nodeId: string): this;
    /**
     * Compile graph for optimal execution (called automatically)
     */
    private compile;
    /**
     * Ultra-fast graph execution (primary method)
     */
    execute(initialState: TState): Promise<TState>;
    /**
     * Stream execution (for monitoring/debugging)
     */
    stream(initialState: TState): AsyncGenerator<TState>;
    /**
     * Get performance metrics and graph info
     */
    getMetrics(): {
        nodeCount: number;
        pathCount: number;
        isCompiled: boolean;
        entryPoint: string | null;
        nodes: string[];
    };
    /**
     * Fast validation (minimal checks)
     */
    validate(): void;
}
//# sourceMappingURL=graph.d.ts.map