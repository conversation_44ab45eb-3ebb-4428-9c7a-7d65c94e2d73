// src/lib/tool-helpers.ts

import {
  AgentState,
  MCPAssistantMessage,
  MCPToolMessage,
  MCPToolCall,
  NodeFunction
} from "../core/types";
import { getLastMessage } from "./agent-helpers";

/**
 * Ultra-fast tool execution helpers optimized for performance
 *
 * Optimizations:
 * - Pre-compiled tool maps for O(1) lookup
 * - Minimal object allocation in hot paths
 * - Efficient error handling
 * - Cached JSON parsing where possible
 */

/**
 * Optimized tool function map type
 */
export type ToolMap = Record<string, (...args: any[]) => any>;

/**
 * Get tool calls (fast extraction)
 */
export const getToolCalls = <TState extends AgentState>(state: TState): MCPToolCall[] => {
  const lastMessage = getLastMessage(state);
  return (lastMessage?.role === 'assistant' && lastMessage.tool_calls)
    ? lastMessage.tool_calls
    : [];
};

/**
 * Check for tool calls (O(1) operation)
 */
export const hasToolCalls = <TState extends AgentState>(state: TState): boolean => {
  const lastMessage = getLastMessage(state);
  return lastMessage?.role === 'assistant' && !!lastMessage.tool_calls?.length;
};

/**
 * Fast routing for tool execution
 */
export const shouldCallTools = <TState extends AgentState>(state: TState): 'tools' | '__end__' =>
  hasToolCalls(state) ? 'tools' : '__end__';

/**
 * Create optimized tool node (ultra-fast execution)
 */
export const createToolNode = <TState extends AgentState>(
  tools: ToolMap
): NodeFunction<TState> => {
  // Pre-compile tool names for faster error messages
  const toolNames = Object.keys(tools);

  return async (state: TState): Promise<Partial<TState>> => {
    const lastMessage = getLastMessage(state) as MCPAssistantMessage;

    if (!lastMessage?.tool_calls) {
      throw new Error('Tool node called without tool calls');
    }

    const toolResults: MCPToolMessage[] = [];
    const toolCalls = lastMessage.tool_calls;

    // Optimized loop for tool execution
    for (let i = 0; i < toolCalls.length; i++) {
      const toolCall = toolCalls[i];
      const { id, function: { name, arguments: argsString } } = toolCall;

      try {
        // Fast tool lookup
        const toolFunction = tools[name];
        if (!toolFunction) {
          throw new Error(`Tool "${name}" not found. Available: ${toolNames.join(', ')}`);
        }

        // Parse and execute
        const args = JSON.parse(argsString);
        const result = await toolFunction(args);

        // Create result message
        toolResults.push({
          role: 'tool',
          tool_call_id: id,
          content: typeof result === 'string' ? result : JSON.stringify(result)
        });

      } catch (error) {
        // Fast error handling
        toolResults.push({
          role: 'tool',
          tool_call_id: id,
          content: `Error: ${error instanceof Error ? error.message : String(error)}`
        });
      }
    }

    return {
      messages: [...state.messages, ...toolResults]
    } as Partial<TState>;
  };
};

/**
 * Create fast tool validator (pre-compiled)
 */
export const createToolValidator = (
  requiredTools: string[],
  availableTools: ToolMap
): (() => void) => {
  // Pre-compute missing tools for faster validation
  const missing = requiredTools.filter(tool => !(tool in availableTools));

  if (missing.length > 0) {
    const errorMessage = `Missing required tools: ${missing.join(', ')}`;
    return () => { throw new Error(errorMessage); };
  }

  // Return no-op function if all tools are available
  return () => {};
};

/**
 * Create enhanced tool node (optimized with features)
 */
export const createEnhancedToolNode = <TState extends AgentState>(
  tools: ToolMap,
  options: {
    logExecution?: boolean;
    timeout?: number;
    retries?: number;
  } = {}
): NodeFunction<TState> => {
  const { logExecution = false, timeout = 30000, retries = 0 } = options;
  const toolNames = Object.keys(tools); // Pre-compile for error messages

  return async (state: TState): Promise<Partial<TState>> => {
    const lastMessage = getLastMessage(state) as MCPAssistantMessage;

    if (!lastMessage?.tool_calls) {
      throw new Error('Enhanced tool node called without tool calls');
    }

    const toolResults: MCPToolMessage[] = [];
    const toolCalls = lastMessage.tool_calls;

    // Optimized execution loop
    for (let i = 0; i < toolCalls.length; i++) {
      const toolCall = toolCalls[i];
      const { id, function: { name, arguments: argsString } } = toolCall;

      if (logExecution) {
        console.log(`🛠️ Executing tool: ${name}`);
      }

      let attempts = 0;
      let success = false;

      while (attempts <= retries && !success) {
        try {
          const toolFunction = tools[name];
          if (!toolFunction) {
            throw new Error(`Tool "${name}" not found. Available: ${toolNames.join(', ')}`);
          }

          const args = JSON.parse(argsString);

          // Execute with timeout (optimized Promise.race)
          const result = timeout > 0
            ? await Promise.race([
                toolFunction(args),
                new Promise((_, reject) =>
                  setTimeout(() => reject(new Error('Timeout')), timeout)
                )
              ])
            : await toolFunction(args);

          toolResults.push({
            role: 'tool',
            tool_call_id: id,
            content: typeof result === 'string' ? result : JSON.stringify(result)
          });

          success = true;

          if (logExecution) {
            console.log(`✅ Tool ${name} completed`);
          }

        } catch (error) {
          attempts++;

          if (attempts > retries) {
            const errorMessage = `Error: ${error instanceof Error ? error.message : String(error)}`;

            toolResults.push({
              role: 'tool',
              tool_call_id: id,
              content: errorMessage
            });

            if (logExecution) {
              console.error(`❌ Tool ${name} failed: ${errorMessage}`);
            }
          } else if (logExecution) {
            console.warn(`⚠️ Tool ${name} retrying... (${attempts}/${retries + 1})`);
          }
        }
      }
    }

    return {
      messages: [...state.messages, ...toolResults]
    } as Partial<TState>;
  };
};

/**
 * Additional optimized tool helpers
 */

/**
 * Fast tool name extraction
 */
export const getToolNames = (tools: ToolMap): string[] => Object.keys(tools);

/**
 * Check if tool exists (O(1))
 */
export const hasToolFunction = (tools: ToolMap, name: string): boolean => name in tools;

/**
 * Get tool count (O(1))
 */
export const getToolCount = (tools: ToolMap): number => Object.keys(tools).length;

/**
 * Create tool execution logger (optimized)
 */
export const createToolLogger = (prefix = '🛠️') => ({
  start: (name: string) => console.log(`${prefix} Executing: ${name}`),
  success: (name: string) => console.log(`✅ Completed: ${name}`),
  error: (name: string, error: string) => console.error(`❌ Failed: ${name} - ${error}`)
});
