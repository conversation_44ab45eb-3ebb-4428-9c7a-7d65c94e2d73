// src/lib/tool-helpers.ts

import {
  AgentState,
  MCPAssistantMessage,
  MCPToolMessage,
  MCPToolCall,
  NodeFunction
} from "../core/types";
import { getLastMessage } from "./agent-helpers";

/**
 * Type for tool function map
 */
export type ToolMap = Record<string, (...args: any[]) => any>;

/**
 * Get tool calls from the last assistant message
 * @param state The current state
 * @returns Array of tool calls or empty array if none exist
 */
export function getToolCalls<TState extends AgentState>(state: TState): MCPToolCall[] {
  const lastMessage = getLastMessage(state);
  if (lastMessage?.role === 'assistant' && lastMessage.tool_calls) {
    return lastMessage.tool_calls;
  }
  return [];
}

/**
 * Check if the last message contains tool calls
 * @param state The current state
 * @returns True if the last message has tool calls
 */
export function hasToolCalls<TState extends AgentState>(state: TState): boolean {
  return getToolCalls(state).length > 0;
}

/**
 * Simple routing function for agent -> tool -> agent loops
 * @param state The current state
 * @returns 'tools' if the last message has tool calls, '__end__' otherwise
 */
export function shouldCallTools<TState extends AgentState>(state: TState): 'tools' | '__end__' {
  return hasToolCalls(state) ? 'tools' : '__end__';
}

/**
 * Create a tool node that can execute a map of tools
 * @param tools Map of tool names to their executable functions
 * @returns A node function that can execute tools based on the last assistant message
 */
export function createToolNode<TState extends AgentState>(
  tools: ToolMap
): NodeFunction<TState> {
  return async (state: TState): Promise<Partial<TState>> => {
    const lastMessage = getLastMessage(state) as MCPAssistantMessage;
    
    if (!lastMessage || lastMessage.role !== 'assistant' || !lastMessage.tool_calls) {
      throw new Error('Tool node called but last message is not an assistant message with tool calls');
    }

    const toolResults: MCPToolMessage[] = [];

    for (const toolCall of lastMessage.tool_calls) {
      const { id, function: { name, arguments: argsString } } = toolCall;
      
      try {
        // Parse arguments
        const args = JSON.parse(argsString);
        
        // Find the tool function
        const toolFunction = tools[name];
        if (!toolFunction) {
          throw new Error(`Tool "${name}" not found in available tools: ${Object.keys(tools).join(', ')}`);
        }

        // Execute the tool
        const result = await toolFunction(args);
        
        // Create tool result message
        toolResults.push({
          role: 'tool',
          tool_call_id: id,
          content: typeof result === 'string' ? result : JSON.stringify(result)
        });
        
      } catch (error) {
        // Create error result message
        toolResults.push({
          role: 'tool',
          tool_call_id: id,
          content: `Error executing tool "${name}": ${error instanceof Error ? error.message : String(error)}`
        });
      }
    }

    return {
      messages: [...state.messages, ...toolResults]
    } as Partial<TState>;
  };
}

/**
 * Create a tool validation function that checks if required tools are available
 * @param requiredTools Array of tool names that must be available
 * @param availableTools Map of available tools
 * @returns Validation function that throws if tools are missing
 */
export function createToolValidator(
  requiredTools: string[],
  availableTools: ToolMap
): () => void {
  return () => {
    const missing = requiredTools.filter(tool => !(tool in availableTools));
    if (missing.length > 0) {
      throw new Error(`Missing required tools: ${missing.join(', ')}`);
    }
  };
}

/**
 * Create a tool execution wrapper with error handling and logging
 * @param tools Map of tool functions
 * @param options Configuration options
 * @returns Enhanced tool node with additional features
 */
export function createEnhancedToolNode<TState extends AgentState>(
  tools: ToolMap,
  options: {
    logExecution?: boolean;
    timeout?: number;
    retries?: number;
  } = {}
): NodeFunction<TState> {
  const { logExecution = false, timeout = 30000, retries = 0 } = options;
  
  return async (state: TState): Promise<Partial<TState>> => {
    const lastMessage = getLastMessage(state) as MCPAssistantMessage;
    
    if (!lastMessage || lastMessage.role !== 'assistant' || !lastMessage.tool_calls) {
      throw new Error('Enhanced tool node called but last message is not an assistant message with tool calls');
    }

    const toolResults: MCPToolMessage[] = [];

    for (const toolCall of lastMessage.tool_calls) {
      const { id, function: { name, arguments: argsString } } = toolCall;
      
      if (logExecution) {
        console.log(`🛠️ Executing tool: ${name} with args: ${argsString}`);
      }
      
      let attempts = 0;
      let success = false;
      
      while (attempts <= retries && !success) {
        try {
          const args = JSON.parse(argsString);
          const toolFunction = tools[name];
          
          if (!toolFunction) {
            throw new Error(`Tool "${name}" not found`);
          }

          // Execute with timeout
          const result = await Promise.race([
            toolFunction(args),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('Tool execution timeout')), timeout)
            )
          ]);
          
          toolResults.push({
            role: 'tool',
            tool_call_id: id,
            content: typeof result === 'string' ? result : JSON.stringify(result)
          });
          
          success = true;
          
          if (logExecution) {
            console.log(`✅ Tool ${name} completed successfully`);
          }
          
        } catch (error) {
          attempts++;
          
          if (attempts > retries) {
            const errorMessage = `Error executing tool "${name}" (${attempts} attempts): ${error instanceof Error ? error.message : String(error)}`;
            
            toolResults.push({
              role: 'tool',
              tool_call_id: id,
              content: errorMessage
            });
            
            if (logExecution) {
              console.error(`❌ ${errorMessage}`);
            }
          } else if (logExecution) {
            console.warn(`⚠️ Tool ${name} failed, retrying... (attempt ${attempts}/${retries + 1})`);
          }
        }
      }
    }

    return {
      messages: [...state.messages, ...toolResults]
    } as Partial<TState>;
  };
}
