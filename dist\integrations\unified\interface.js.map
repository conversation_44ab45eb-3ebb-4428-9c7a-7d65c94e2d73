{"version": 3, "file": "interface.js", "sourceRoot": "", "sources": ["../../../src/integrations/unified/interface.ts"], "names": [], "mappings": ";AAAA,wCAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+FxC,wDA2CC;AAuLD,sDAiBC;AAtPD;;GAEG;AACI,KAAK,UAAU,sBAAsB,CAAC,MAAwB;IACnE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE,GAAG,MAAM,CAAC;IAEhD,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,QAAQ,CAAC,CAAC,CAAC;YACd,MAAM,EAAE,YAAY,EAAE,GAAG,wDAAa,kBAAkB,GAAC,CAAC;YAE1D,IAAI,CAAC;gBACH,6DAA6D;gBAC7D,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACjC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC;oBACxB,MAAM,EAAE,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc;oBAC5C,OAAO,EAAE,OAAO,CAAC,OAAO;iBACzB,CAAC,CAAC;gBAEH,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACjD,OAAO,IAAI,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;YACtF,CAAC;QACH,CAAC;QAED,KAAK,WAAW,CAAC,CAAC,CAAC;YACjB,MAAM,EAAE,eAAe,EAAE,GAAG,wDAAa,qBAAqB,GAAC,CAAC;YAEhE,IAAI,CAAC;gBACH,gEAAgE;gBAChE,MAAM,SAAS,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;gBAC/C,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC;oBAC9B,MAAM,EAAE,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB;oBAC/C,OAAO,EAAE,OAAO,CAAC,OAAO;iBACzB,CAAC,CAAC;gBAEH,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBACvD,OAAO,IAAI,uBAAuB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACtD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,gFAAgF,CAAC,CAAC;YACpG,CAAC;QACH,CAAC;QAED;YACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;IACzD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,oBAAoB;IACxB,YACU,MAAW,EAAE,eAAe;IAC5B,MAAiC;QADjC,WAAM,GAAN,MAAM,CAAK;QACX,WAAM,GAAN,MAAM,CAA2B;IACxC,CAAC;IAEJ,KAAK,CAAC,gBAAgB,CACpB,QAAsB,EACtB,UAA6B,EAAE;QAE/B,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;IACnE,CAAC;IAED,KAAK,CAAC,yBAAyB,CAC7B,QAAsB,EACtB,UAA6B,EAAE,EAC/B,OAA6B,EAC7B,OAA6B;QAE7B,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAC3D,6EAA6E;QAC7E,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,GAAG,CAAC,iEAAiE,CAAC,CAAC;QACjF,CAAC;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;IACjF,CAAC;IAED,eAAe,CACb,UAA6B,EAAE;QAE/B,MAAM,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;IACtC,CAAC;IAED,WAAW;QACT,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;IAC1C,CAAC;IAEO,sBAAsB,CAAC,OAA0B;QACvD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,cAAc,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QAE/D,MAAM,aAAa,GAAQ;YACzB,GAAG,IAAI;YACP,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;YAChC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;SAC7B,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACV,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACvC,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE;oBACR,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;iBAC5B;aACF,CAAC,CAAC,CAAC;QACN,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;gBACnC,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACN,aAAa,CAAC,UAAU,GAAG;oBACzB,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE;iBACpC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,cAAc,KAAK,MAAM,EAAE,CAAC;YAC9B,aAAa,CAAC,cAAc,GAAG,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;QACzD,CAAC;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,uBAAuB;IAC3B,YACU,MAAW,EAAE,kBAAkB;IAC/B,MAAiC;QADjC,WAAM,GAAN,MAAM,CAAK;QACX,WAAM,GAAN,MAAM,CAA2B;IACxC,CAAC;IAEJ,KAAK,CAAC,gBAAgB,CACpB,QAAsB,EACtB,UAA6B,EAAE;QAE/B,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;IAC/D,CAAC;IAED,KAAK,CAAC,yBAAyB,CAC7B,QAAsB,EACtB,UAA6B,EAAE,EAC/B,OAA6B,EAC7B,OAA6B;QAE7B,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC1F,CAAC;IAED,eAAe,CACb,UAA6B,EAAE;QAE/B,MAAM,gBAAgB,GAAG,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;IACtC,CAAC;IAED,WAAW;QACT,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,iEAAiE;QACjE,OAAO;YACL,4BAA4B;YAC5B,2BAA2B;YAC3B,wBAAwB;YACxB,0BAA0B;YAC1B,yBAAyB;SAC1B,CAAC;IACJ,CAAC;IAEO,yBAAyB,CAAC,OAA0B;QAC1D,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;QAE/C,MAAM,gBAAgB,GAAQ;YAC5B,GAAG,IAAI;YACP,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;YACxB,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;YAChC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;SAC7B,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACV,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC1C,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,YAAY,EAAE,IAAI,CAAC,UAAU;aAC9B,CAAC,CAAC,CAAC;QACN,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;gBACnC,gBAAgB,CAAC,UAAU,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;YACrD,CAAC;iBAAM,CAAC;gBACN,gBAAgB,CAAC,UAAU,GAAG;oBAC5B,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,UAAU,CAAC,IAAI;iBACtB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;CACF;AAED;;GAEG;AACI,KAAK,UAAU,qBAAqB,CACzC,QAAgC,EAChC,MAAe,EACf,KAAc;IAEd,MAAM,MAAM,GAAqB;QAC/B,QAAQ;QACR,KAAK,EAAE,KAAK,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,4BAA4B,CAAC;QACjF,WAAW,EAAE,GAAG;QAChB,SAAS,EAAE,IAAI;KAChB,CAAC;IAEF,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAED,OAAO,sBAAsB,CAAC,MAAM,CAAC,CAAC;AACxC,CAAC"}