{"version": 3, "file": "real-llm-templates.js", "sourceRoot": "", "sources": ["../../src/examples/real-llm-templates.ts"], "names": [], "mappings": ";;AAEA,qCAAqC;;AAmYpB,8BAAc;AAjY/B,kCAAmC;AACnC,gCASgB;AAEhB;;;;;;;;GAQG;AAEH,uCAAuC;AAEvC,MAAM,SAAS,GAAyB;IACtC,iBAAiB,EAAE,KAAK,EAAE,IAAyC,EAAE,EAAE;QACrE,OAAO,CAAC,GAAG,CAAC,4BAA4B,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEzD,uCAAuC;QACvC,MAAM,WAAW,GAAG;YAClB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;YAC5E,SAAS,EAAE,eAAe;YAC1B,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE,SAAS;YACpB,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,SAAS;SAC7B,CAAC;QAEF,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,gBAAgB,EAAE,KAAK,EAAE,IAA+D,EAAE,EAAE;QAC1F,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,QAAQ,IAAI,QAAQ,6BAA6B,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAElG,gCAAgC;QAChC,OAAO;YACL,cAAc,EAAE,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;YAClE,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,QAAQ;SACpC,CAAC;IACJ,CAAC;IAED,mBAAmB,EAAE,KAAK,EAAE,IAA0C,EAAE,EAAE;QACxE,OAAO,CAAC,GAAG,CAAC,oCAAoC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAE9D,iCAAiC;QACjC,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE;gBACP;oBACE,EAAE,EAAE,QAAQ;oBACZ,KAAK,EAAE,iBAAiB,IAAI,CAAC,KAAK,EAAE;oBACpC,OAAO,EAAE,6BAA6B,IAAI,CAAC,KAAK,2CAA2C;oBAC3F,SAAS,EAAE,IAAI;oBACf,MAAM,EAAE,wBAAwB;iBACjC;gBACD;oBACE,EAAE,EAAE,QAAQ;oBACZ,KAAK,EAAE,sBAAsB,IAAI,CAAC,KAAK,EAAE;oBACzC,OAAO,EAAE,mDAAmD,IAAI,CAAC,KAAK,GAAG;oBACzE,SAAS,EAAE,IAAI;oBACf,MAAM,EAAE,sBAAsB;iBAC/B;aACF;YACD,YAAY,EAAE,CAAC;SAChB,CAAC;IACJ,CAAC;IAED,eAAe,EAAE,KAAK,EAAE,IAAgE,EAAE,EAAE;QAC1F,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QAE5D,8BAA8B;QAC9B,OAAO;YACL,UAAU,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;YAC3D,IAAI,EAAE,IAAI,CAAC,YAAY;YACvB,MAAM,EAAE,WAAW;YACnB,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,EAAE;YACjC,aAAa,EAAE,MAAM;YACrB,MAAM,EAAE,YAAY,IAAI,CAAC,YAAY,yBAAyB;SAC/D,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,wCAAwC;AAExC;;;GAGG;AACH,MAAM,kBAAkB,GAAG,CAAC,aAAqB,EAAE,YAAoB,SAAS,EAAE,EAAE;IAClF,OAAO,KAAK,EAAE,KAAU,EAAE,EAAE;QAC1B,MAAM,WAAW,GAAG,IAAA,oBAAc,EAAC,KAAK,CAAC,CAAC;QAE1C,8DAA8D;QAC9D,eAAe;QACf,qEAAqE;QACrE,0DAA0D;QAC1D,oBAAoB;QACpB,gBAAgB;QAChB,kDAAkD;QAClD,wBAAwB;QACxB,OAAO;QACP,kCAAkC;QAClC,MAAM;QAEN,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,kBAAkB,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;QAEzF,IAAI,QAAgB,CAAC;QACrB,IAAI,SAA4B,CAAC;QAEjC,IAAI,SAAS,KAAK,UAAU,EAAE,CAAC;YAC7B,6CAA6C;YAC7C,IAAI,WAAW,EAAE,IAAI,KAAK,MAAM,EAAE,CAAC;gBACjC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACnD,QAAQ,GAAG,iFAAiF,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,4DAA4D,CAAC;YAC9L,CAAC;iBAAM,CAAC;gBACN,qDAAqD;gBACrD,MAAM,OAAO,GAAG,WAAW,EAAE,OAAO,IAAI,EAAE,CAAC;gBAE3C,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC9C,QAAQ,GAAG,oDAAoD,CAAC;oBAChE,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;wBACtC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,eAAe,CAAC;oBACxE,SAAS,GAAG,CAAC;4BACX,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;4BACrD,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE;gCACR,IAAI,EAAE,mBAAmB;gCACzB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;6BACzD;yBACF,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACtG,QAAQ,GAAG,mCAAmC,CAAC;oBAC/C,SAAS,GAAG,CAAC;4BACX,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;4BACrD,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE;gCACR,IAAI,EAAE,kBAAkB;gCACxB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;oCACxB,SAAS,EAAE,kBAAkB;oCAC7B,OAAO,EAAE,6BAA6B;oCACtC,QAAQ,EAAE,QAAQ;iCACnB,CAAC;6BACH;yBACF,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC9F,QAAQ,GAAG,0DAA0D,CAAC;oBACtE,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;oBACpE,SAAS,GAAG,CAAC;4BACX,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;4BACrD,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE;gCACR,IAAI,EAAE,qBAAqB;gCAC3B,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,KAAK,IAAI,qBAAqB,EAAE,CAAC;6BACrE;yBACF,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBACnG,QAAQ,GAAG,8CAA8C,CAAC;oBAC1D,SAAS,GAAG,CAAC;4BACX,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;4BACrD,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE;gCACR,IAAI,EAAE,iBAAiB;gCACvB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;oCACxB,YAAY,EAAE,0BAA0B;oCACxC,UAAU,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE;iCACvC,CAAC;6BACH;yBACF,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,QAAQ,GAAG,2CAA2C,OAAO,sFAAsF,CAAC;gBACtJ,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YACnC,MAAM,IAAI,GAAG,WAAW,EAAE,OAAO,IAAI,EAAE,CAAC;YACxC,QAAQ,GAAG,oDAAoD,IAAI,kzBAAkzB,CAAC;QACx3B,CAAC;aAAM,IAAI,SAAS,KAAK,cAAc,EAAE,CAAC;YACxC,QAAQ,GAAG,sNAAsN,CAAC;QACpO,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,SAAS,SAAS,+BAA+B,WAAW,EAAE,OAAO,yFAAyF,CAAC;QAC5K,CAAC;QAED,MAAM,gBAAgB,GAAG;YACvB,IAAI,EAAE,WAAoB;YAC1B,OAAO,EAAE,QAAQ;YACjB,GAAG,CAAC,SAAS,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;SAC5C,CAAC;QAEF,OAAO,EAAE,QAAQ,EAAE,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,gBAAgB,CAAC,EAAE,CAAC;IAC7D,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,yBAAyB;AAEzB,KAAK,UAAU,qBAAqB;IAClC,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IACzD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7B,MAAM,OAAO,GAAG,kBAAkB,CAChC,8JAA8J,EAC9J,UAAU,CACX,CAAC;IAEF,MAAM,aAAa,GAAG,IAAA,yBAAmB,EAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAC9D,MAAM,QAAQ,GAAG,IAAI,eAAQ,CAAC,aAAa,CAAC,CAAC;IAE7C,MAAM,SAAS,GAAG;QAChB,mCAAmC;QACnC,kDAAkD;QAClD,wDAAwD;QACxD,sCAAsC;KACvC,CAAC;IAEF,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,cAAc,QAAQ,EAAE,CAAC,CAAC;QAEtC,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC;YACpC,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;SAChD,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,IAAA,oBAAc,EAAC,MAAM,CAAC,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,iBAAiB,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;IACxD,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;AAC1D,CAAC;AAED,KAAK,UAAU,oBAAoB;IACjC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7B,MAAM,OAAO,GAAG,kBAAkB,CAChC,mKAAmK,EACnK,SAAS,CACV,CAAC;IAEF,MAAM,YAAY,GAAG,IAAA,wBAAkB,EAAoB,OAAO,EAAE;QAClE,QAAQ,EAAE,CAAC;QACX,QAAQ,EAAE,YAAY;QACtB,oBAAoB,EAAE,IAAI;KAC3B,CAAC,CAAC;IAEH,MAAM,QAAQ,GAAG,IAAI,eAAQ,CAAC,YAAY,CAAC,CAAC;IAE5C,MAAM,KAAK,GAAG;QACZ,2CAA2C;QAC3C,kCAAkC;QAClC,iDAAiD;KAClD,CAAC;IAEF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC;QAElC,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC;YACpC,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SAC5C,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,IAAA,oBAAc,EAAC,MAAM,CAAC,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,YAAY,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;QAEjD,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YAChB,OAAO,CAAC,GAAG,CAAC,kBAAkB,MAAM,CAAC,IAAI,CAAC,MAAM,iBAAiB,CAAC,CAAC;YACnE,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;AACzD,CAAC;AAED,KAAK,UAAU,yBAAyB;IACtC,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;IAClD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7B,mCAAmC;IACnC,MAAM,OAAO,GAAkB;QAC7B;YACE,EAAE,EAAE,kBAAkB;YACtB,IAAI,EAAE,sBAAsB;YAC5B,WAAW,EAAE,wDAAwD;YACrE,YAAY,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC;YAChD,eAAe,EAAE,CAAC,sBAAsB,EAAE,uBAAuB,CAAC;YAClE,KAAK,EAAE,IAAA,yBAAmB,EACxB,kBAAkB,CAAC,gCAAgC,EAAE,UAAU,CAAC,EAChE,EAAE,mBAAmB,EAAE,SAAS,CAAC,mBAAmB,EAAE,CACvD;SACF;QACD;YACE,EAAE,EAAE,qBAAqB;YACzB,IAAI,EAAE,0BAA0B;YAChC,WAAW,EAAE,kDAAkD;YAC/D,YAAY,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,WAAW,CAAC;YAC7D,eAAe,EAAE,CAAC,oBAAoB,EAAE,eAAe,CAAC;YACxD,KAAK,EAAE,IAAA,yBAAmB,EACxB,kBAAkB,CAAC,oCAAoC,EAAE,UAAU,CAAC,EACpE,EAAE,gBAAgB,EAAE,SAAS,CAAC,gBAAgB,EAAE,CACjD;SACF;QACD;YACE,EAAE,EAAE,iBAAiB;YACrB,IAAI,EAAE,uBAAuB;YAC7B,WAAW,EAAE,4CAA4C;YACzD,YAAY,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,CAAC;YACvD,eAAe,EAAE,CAAC,oBAAoB,EAAE,qBAAqB,CAAC;YAC9D,KAAK,EAAE,IAAA,yBAAmB,EACxB,kBAAkB,CAAC,kCAAkC,EAAE,UAAU,CAAC,EAClE,EAAE,eAAe,EAAE,SAAS,CAAC,eAAe,EAAE,CAC/C;SACF;KACF,CAAC;IAEF,MAAM,gBAAgB,GAAG,kBAAkB,CACzC,wGAAwG,EACxG,cAAc,CACf,CAAC;IAEF,MAAM,iBAAiB,GAAG,IAAA,6BAAuB,EAC/C,gBAAgB,EAChB,OAAO,EACP;QACE,kBAAkB,EAAE,CAAC;QACrB,gBAAgB,EAAE,IAAI;QACtB,cAAc,EAAE,KAAK;KACtB,CACF,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAE7D,MAAM,QAAQ,GAAG,IAAI,eAAQ,CAAC,iBAAiB,CAAC,CAAC;IACjD,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC;QACpC,QAAQ,EAAE,CAAC;gBACT,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,8FAA8F;aACxG,CAAC;KACH,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,IAAA,oBAAc,EAAC,MAAM,CAAC,CAAC;IAC5C,OAAO,CAAC,GAAG,CAAC,4BAA4B,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;IAEjE,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;AAC9D,CAAC;AAED,6BAA6B;AAE7B,KAAK,UAAU,IAAI;IACjB,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAC1D,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7B,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;IAEjE,IAAI,CAAC;QACH,MAAM,qBAAqB,EAAE,CAAC;QAC9B,MAAM,oBAAoB,EAAE,CAAC;QAC7B,MAAM,yBAAyB,EAAE,CAAC;QAElC,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;QAC5E,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;QAC1E,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC;QAC7E,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,6EAA6E,CAAC,CAAC;IAE7F,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC;AACH,CAAC;AAED,eAAe;AACf,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC"}