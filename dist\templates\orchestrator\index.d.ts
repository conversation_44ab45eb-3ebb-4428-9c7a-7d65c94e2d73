import { Graph, AgentState, NodeFunction } from '../../core';
import { PlanStep } from '../planner';
/**
 * Configuration for worker agents
 */
export interface WorkerAgent<TState extends AgentState = AgentState> {
    /** Unique identifier for the worker */
    id: string;
    /** Human-readable name */
    name: string;
    /** Description of capabilities */
    description: string;
    /** The graph that implements this worker */
    graph: Graph<TState>;
    /** Capabilities this worker provides */
    capabilities: string[];
    /** Specialization areas */
    specializations?: string[];
}
/**
 * Configuration options for the Orchestrator Agent
 */
export interface OrchestratorAgentOptions<TState extends AgentState = AgentState> {
    /** Available worker agents */
    workers: WorkerAgent<TState>[];
    /** System message for orchestration */
    systemMessage?: string;
    /** Custom orchestrator node function */
    orchestratorNode?: NodeFunction<any>;
    /** Maximum number of iterations */
    maxIterations?: number;
    /** Whether to run steps in parallel when possible */
    enableParallelExecution?: boolean;
}
/**
 * Represents the execution status of a plan step
 */
export interface StepExecution {
    /** The step being executed */
    step: PlanStep;
    /** Current status */
    status: 'pending' | 'assigned' | 'executing' | 'completed' | 'failed' | 'skipped';
    /** Assigned worker ID */
    assignedWorker?: string;
    /** Execution result */
    result?: string;
    /** Error message if failed */
    error?: string;
    /** Start time */
    startTime?: Date;
    /** End time */
    endTime?: Date;
}
/**
 * State interface for Orchestrator Agent
 */
export interface OrchestratorAgentState extends AgentState {
    /** The plan to execute */
    plan?: PlanStep[];
    /** Current execution status of each step */
    stepExecutions?: StepExecution[];
    /** Overall orchestration status */
    orchestrationStatus?: 'planning' | 'executing' | 'completed' | 'failed';
    /** Current step index being processed */
    currentStepIndex?: number;
    /** Scratchpad for intermediate results */
    scratchpad?: Record<string, any>;
    /** Final synthesized result */
    finalResult?: string;
    /** Available workers */
    availableWorkers?: string[];
}
/**
 * Create an Orchestrator Agent - manages and coordinates multiple worker agents
 *
 * This is the "team lead" that takes a plan and delegates tasks to specialized
 * worker agents, managing their execution and synthesizing the final result.
 *
 * @param options Configuration options
 * @returns Configured Graph ready for execution
 */
export declare function createOrchestratorAgent<TState extends OrchestratorAgentState = OrchestratorAgentState>(options: OrchestratorAgentOptions): Graph<TState>;
//# sourceMappingURL=index.d.ts.map