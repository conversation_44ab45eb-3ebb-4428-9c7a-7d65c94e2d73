import { describe, test, expect } from '@jest/globals';

/**
 * Optimized Multi-Agent Tests
 * 
 * Tests multi-agent coordination with performance optimizations
 * and shorter timeouts to avoid API rate limiting issues.
 */

// Optimized Agent with shorter responses
class OptimizedAgent {
  name: string;
  instructions: string;
  
  constructor(config: { name: string; instructions: string }) {
    this.name = config.name;
    this.instructions = config.instructions;
  }

  async run(input: string): Promise<{ finalOutput: string; finalState: any; metrics: any }> {
    const apiKey = '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';
    const model = 'moonshotai/kimi-k2';
    
    const messages = [
      {
        role: 'system' as const,
        content: `You are ${this.name}. ${this.instructions}
        
IMPORTANT: Keep responses concise (under 100 words) and focused. Be direct and actionable.`
      },
      {
        role: 'user' as const,
        content: input
      }
    ];

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://ag3ntic-framework.dev',
        'X-Title': 'AG3NTIC Framework Tests',
      },
      body: JSON.stringify({
        model,
        messages,
        max_tokens: 300, // Reduced for faster responses
        temperature: 0.3, // Lower for more focused responses
        stream: false,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const data = await response.json() as any;
    const finalOutput = data.choices[0].message.content;

    return {
      finalOutput,
      finalState: {
        messages: [...messages, { role: 'assistant', content: finalOutput }],
        metadata: { agentName: this.name }
      },
      metrics: {
        totalSteps: 1,
        toolCalls: 0,
        executionTime: 1000,
      }
    };
  }
}

describe('Optimized Multi-Agent Tests', () => {
  test('should coordinate between planner and executor agents', async () => {
    console.log('🤝 Testing planner → executor coordination...');
    
    const plannerAgent = new OptimizedAgent({
      name: 'Quick Planner',
      instructions: 'Create concise, actionable plans. List 3 key steps maximum.'
    });

    const executorAgent = new OptimizedAgent({
      name: 'Quick Executor',
      instructions: 'Execute plans efficiently. Provide brief status updates.'
    });

    // Step 1: Planner creates plan
    const planResult = await plannerAgent.run('Create a quick plan for setting up a new website.');
    expect(planResult.finalOutput).toBeDefined();
    expect(planResult.finalOutput.toLowerCase()).toContain('plan');
    console.log('📋 Plan:', planResult.finalOutput.substring(0, 80) + '...');

    // Step 2: Executor takes the plan
    const execResult = await executorAgent.run(`Execute this plan: ${planResult.finalOutput}`);
    expect(execResult.finalOutput).toBeDefined();
    expect(execResult.finalOutput.toLowerCase()).toContain('execut');
    console.log('⚡ Execution:', execResult.finalOutput.substring(0, 80) + '...');

    // Verify coordination
    expect(planResult.finalOutput.length).toBeGreaterThan(20);
    expect(execResult.finalOutput.length).toBeGreaterThan(20);
  }, 30000); // 30 second timeout

  test('should enable analyst → planner → executor workflow', async () => {
    console.log('📊 Testing analyst → planner → executor workflow...');
    
    const analystAgent = new OptimizedAgent({
      name: 'Quick Analyst',
      instructions: 'Analyze data quickly. Provide 2-3 key insights maximum.'
    });

    const plannerAgent = new OptimizedAgent({
      name: 'Strategic Planner',
      instructions: 'Create strategic plans based on analysis. Be concise.'
    });

    const executorAgent = new OptimizedAgent({
      name: 'Action Executor',
      instructions: 'Execute strategic plans. Focus on immediate next steps.'
    });

    // Step 1: Analyst analyzes
    const analysisResult = await analystAgent.run('Analyze this trend: Sales increased 20% last quarter.');
    expect(analysisResult.finalOutput).toBeDefined();
    console.log('📊 Analysis:', analysisResult.finalOutput.substring(0, 60) + '...');

    // Step 2: Planner creates strategy
    const strategyResult = await plannerAgent.run(`Based on this analysis: ${analysisResult.finalOutput}, create a strategy.`);
    expect(strategyResult.finalOutput).toBeDefined();
    console.log('📋 Strategy:', strategyResult.finalOutput.substring(0, 60) + '...');

    // Step 3: Executor implements
    const implementResult = await executorAgent.run(`Implement this strategy: ${strategyResult.finalOutput}`);
    expect(implementResult.finalOutput).toBeDefined();
    console.log('⚡ Implementation:', implementResult.finalOutput.substring(0, 60) + '...');

    // Verify workflow
    expect(analysisResult.finalOutput.toLowerCase()).toContain('20%');
    expect(strategyResult.finalOutput.toLowerCase()).toContain('strategy');
    expect(implementResult.finalOutput.toLowerCase()).toContain('implement');
  }, 45000); // 45 second timeout

  test('should handle parallel agent execution', async () => {
    console.log('⚡ Testing parallel agent execution...');
    
    const researchAgent = new OptimizedAgent({
      name: 'Researcher',
      instructions: 'Conduct quick research. Provide key findings only.'
    });

    const designAgent = new OptimizedAgent({
      name: 'Designer',
      instructions: 'Create design concepts. Be brief and visual.'
    });

    const techAgent = new OptimizedAgent({
      name: 'Tech Lead',
      instructions: 'Assess technical feasibility. Give quick recommendations.'
    });

    // Execute in parallel
    const startTime = Date.now();
    const [researchResult, designResult, techResult] = await Promise.all([
      researchAgent.run('Research mobile app trends for 2024.'),
      designAgent.run('Design a modern mobile app interface concept.'),
      techAgent.run('Assess technical requirements for a mobile app.')
    ]);
    const duration = Date.now() - startTime;

    // Verify all completed
    expect(researchResult.finalOutput).toBeDefined();
    expect(designResult.finalOutput).toBeDefined();
    expect(techResult.finalOutput).toBeDefined();

    console.log('🔍 Research:', researchResult.finalOutput.substring(0, 50) + '...');
    console.log('🎨 Design:', designResult.finalOutput.substring(0, 50) + '...');
    console.log('💻 Tech:', techResult.finalOutput.substring(0, 50) + '...');
    console.log(`⚡ Parallel execution completed in ${duration}ms`);

    // Should be faster than sequential
    expect(duration).toBeLessThan(25000); // Should complete in under 25 seconds
  }, 30000);

  test('should demonstrate agent handoffs', async () => {
    console.log('🔄 Testing agent handoffs...');
    
    const coordinatorAgent = new OptimizedAgent({
      name: 'Coordinator',
      instructions: 'Coordinate tasks between specialists. Decide who should handle what.'
    });

    const specialistAgent = new OptimizedAgent({
      name: 'Specialist',
      instructions: 'Handle specialized tasks with expertise. Be efficient.'
    });

    // Step 1: Coordinator receives task
    const coordinationResult = await coordinatorAgent.run('We need to optimize our database performance. Who should handle this?');
    expect(coordinationResult.finalOutput).toBeDefined();
    console.log('🎯 Coordination:', coordinationResult.finalOutput.substring(0, 70) + '...');

    // Step 2: Handoff to specialist
    const specialistResult = await specialistAgent.run(`Handle this database optimization task: ${coordinationResult.finalOutput}`);
    expect(specialistResult.finalOutput).toBeDefined();
    console.log('🔧 Specialist:', specialistResult.finalOutput.substring(0, 70) + '...');

    // Verify handoff
    expect(coordinationResult.finalOutput.toLowerCase()).toContain('database');
    expect(specialistResult.finalOutput.toLowerCase()).toContain('optim');
  }, 25000);

  test('should handle agent consensus building', async () => {
    console.log('🗳️ Testing agent consensus building...');
    
    const agent1 = new OptimizedAgent({
      name: 'Agent Alpha',
      instructions: 'Provide your perspective on decisions. Be decisive.'
    });

    const agent2 = new OptimizedAgent({
      name: 'Agent Beta',
      instructions: 'Offer alternative viewpoints. Consider different angles.'
    });

    const facilitatorAgent = new OptimizedAgent({
      name: 'Facilitator',
      instructions: 'Build consensus between different viewpoints. Find common ground.'
    });

    // Get different perspectives
    const perspective1 = await agent1.run('Should we prioritize speed or quality in our next release?');
    const perspective2 = await agent2.run('Should we prioritize speed or quality in our next release?');

    console.log('👤 Alpha:', perspective1.finalOutput.substring(0, 50) + '...');
    console.log('👤 Beta:', perspective2.finalOutput.substring(0, 50) + '...');

    // Build consensus
    const consensusResult = await facilitatorAgent.run(`
      Build consensus between these viewpoints:
      Agent Alpha: ${perspective1.finalOutput}
      Agent Beta: ${perspective2.finalOutput}
    `);

    expect(consensusResult.finalOutput).toBeDefined();
    expect(consensusResult.finalOutput.toLowerCase()).toContain('consensus');
    console.log('🤝 Consensus:', consensusResult.finalOutput.substring(0, 70) + '...');
  }, 35000);

  test('should demonstrate swarm intelligence', async () => {
    console.log('🐝 Testing swarm intelligence...');
    
    const agents = [
      new OptimizedAgent({
        name: 'Swarm Agent 1',
        instructions: 'Contribute to collective problem solving. Share insights.'
      }),
      new OptimizedAgent({
        name: 'Swarm Agent 2',
        instructions: 'Build on others ideas. Add unique perspective.'
      }),
      new OptimizedAgent({
        name: 'Swarm Agent 3',
        instructions: 'Synthesize collective knowledge. Find patterns.'
      })
    ];

    const problem = 'How can we reduce customer churn by 30%?';

    // Collective problem solving
    const swarmResults = await Promise.all(
      agents.map(agent => agent.run(`Contribute to solving: ${problem}`))
    );

    // Verify swarm responses
    swarmResults.forEach((result, index) => {
      expect(result.finalOutput).toBeDefined();
      expect(result.finalOutput.length).toBeGreaterThan(20);
      console.log(`🐝 Agent ${index + 1}:`, result.finalOutput.substring(0, 50) + '...');
    });

    // Check for diverse perspectives
    const outputs = swarmResults.map(r => r.finalOutput.toLowerCase());
    const uniqueWords = new Set(outputs.join(' ').split(' '));
    expect(uniqueWords.size).toBeGreaterThan(50); // Should have diverse vocabulary
  }, 30000);
});
