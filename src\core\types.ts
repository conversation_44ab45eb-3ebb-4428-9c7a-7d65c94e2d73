// src/core/types.ts

// --- MCP (Model Context Protocol) Aligned Types ---

export interface MCPToolCallFunction {
  name: string;
  arguments: string; // JSON string
}

export interface MCPToolCall {
  id: string;
  type: 'function';
  function: MCPToolCallFunction;
}

export interface MCPSystemMessage {
    role: 'system';
    content: string;
}

export interface MCPUserMessage {
    role: 'user';
    content: string;
}

export interface MCPAssistantMessage {
    role: 'assistant';
    content: string | null;
    tool_calls?: MCPToolCall[];
}

export interface MCPToolMessage {
    role: 'tool';
    tool_call_id: string;
    content: string; // The result of the tool call
}

export type MCPMessage = MCPSystemMessage | MCPUserMessage | MCPAssistantMessage | MCPToolMessage;

// --- AG3NTIC Core Types ---

export interface BaseAgentState {
    messages: MCPMessage[];
    [key: string]: any; // Allow for other state properties
}

export type AgentState = BaseAgentState;

export type NodeFunction<TState extends AgentState> = (
  state: TState
) => Promise<Partial<TState>>;

export const END = "__END__";

// --- Conditional Edge Types ---

export type ConditionalFunction<TState extends AgentState> = (state: TState) => string;

export type EdgeTargetMap = Record<string, string>;
