import { AgentState, MCPMessage, MCPToolCall, NodeFunction, ToolMap, CreateAgentNodeOptions } from "./types";
/**
 * Get the last message from the state
 * @param state The current state
 * @returns The last message or undefined if no messages exist
 */
export declare function getLastMessage<TState extends AgentState>(state: TState): MCPMessage | undefined;
/**
 * Add a message to the state
 * @param state The current state
 * @param message The message to add
 * @returns A partial state update with the new message added
 */
export declare function addMessage<TState extends AgentState>(state: TState, message: MCPMessage): Partial<TState>;
/**
 * Get tool calls from the last assistant message
 * @param state The current state
 * @returns Array of tool calls or empty array if none exist
 */
export declare function getToolCalls<TState extends AgentState>(state: TState): MCPToolCall[];
/**
 * Check if the last message contains tool calls
 * @param state The current state
 * @returns True if the last message has tool calls
 */
export declare function hasToolCalls<TState extends AgentState>(state: TState): boolean;
/**
 * Create a tool node that can execute a map of tools
 * @param tools Map of tool names to their executable functions
 * @returns A node function that can execute tools based on the last assistant message
 */
export declare function createToolNode<TState extends AgentState>(tools: ToolMap): NodeFunction<TState>;
/**
 * Create an agent node that can call an LLM with tools
 * This is a simplified version - in a real implementation, this would integrate with specific LLM clients
 * @param _llmClient The LLM client (e.g., OpenAI instance) - currently unused in mock implementation
 * @param options Configuration options for the agent
 * @returns A node function that calls the LLM
 */
export declare function createAgentNode<TState extends AgentState>(_llmClient: any, // This would be typed based on the specific LLM client
options?: CreateAgentNodeOptions): NodeFunction<TState>;
/**
 * Simple routing function for agent -> tool -> agent loops
 * @param state The current state
 * @returns 'tools' if the last message has tool calls, '__end__' otherwise
 */
export declare function shouldCallTools<TState extends AgentState>(state: TState): 'tools' | '__end__';
/**
 * Get all messages of a specific role
 * @param state The current state
 * @param role The role to filter by
 * @returns Array of messages with the specified role
 */
export declare function getMessagesByRole<TState extends AgentState>(state: TState, role: MCPMessage['role']): MCPMessage[];
/**
 * Count messages by role
 * @param state The current state
 * @param role The role to count
 * @returns Number of messages with the specified role
 */
export declare function countMessagesByRole<TState extends AgentState>(state: TState, role: MCPMessage['role']): number;
//# sourceMappingURL=helpers.d.ts.map