// src/examples/weather-agent.ts

import { <PERSON><PERSON><PERSON>, Executor, AgentState, MCPMessage, MCPAssistantMessage } from '../core';
import { addMessage } from '../lib';

// --- 1. Define the application-specific state ---
interface WeatherState extends AgentState {
  topic: string;
}

// --- 2. Define the Tools ---
// A simple, synchronous tool function.
const get_current_weather = (location: string, unit: 'celsius' | 'fahrenheit' = 'fahrenheit') => {
  if (location.toLowerCase().includes("tokyo")) {
    return { location: "Tokyo", temperature: unit === 'celsius' ? "10" : "50", unit };
  }
  if (location.toLowerCase().includes("san francisco")) {
    return { location: "San Francisco", temperature: unit === 'celsius' ? "18" : "72", unit };
  }
  return { location, temperature: "unknown", unit };
};

// --- 3. Define the Nodes ---
// This is our "Agent" node. It's a function that simulates an LLM call.
const agentNode = async (state: WeatherState): Promise<Partial<WeatherState>> => {
  const lastMessage = state.messages[state.messages.length - 1];
  console.log(`\n🤖 Agent is thinking based on: "${lastMessage.role}: ${lastMessage.content}"`);

  // Simulate LLM deciding to call a tool.
  if (lastMessage.role === 'user') {
    const assistantMessage: MCPAssistantMessage = {
      role: 'assistant',
      content: null,
      tool_calls: [{
        id: "call_123",
        type: 'function',
        function: {
          name: 'get_current_weather',
          arguments: JSON.stringify({ location: 'Tokyo' })
        }
      }]
    };
    return addMessage(state, assistantMessage);
  }

  // Simulate LLM summarizing the tool's response.
  if (lastMessage.role === 'tool') {
    const assistantMessage: MCPAssistantMessage = {
      role: 'assistant',
      content: `The weather in Tokyo is 10 degrees Celsius.`
    };
    return addMessage(state, assistantMessage);
  }

  return {};
};

// This is our "Tool" node. It executes the actual tool function.
const toolNode = async (state: WeatherState): Promise<Partial<WeatherState>> => {
  const lastMessage = state.messages[state.messages.length - 1] as MCPAssistantMessage;
  const toolCall = lastMessage.tool_calls![0];
  const toolName = toolCall.function.name;
  const args = JSON.parse(toolCall.function.arguments);

  console.log(`\n🛠️ Calling tool "${toolName}" with args:`, args);

  if (toolName === 'get_current_weather') {
    const result = get_current_weather(args.location);
    const newMessages: MCPMessage[] = [
      ...state.messages,
      { role: 'tool', tool_call_id: toolCall.id, content: JSON.stringify(result) }
    ];
    return { messages: newMessages };
  }
  throw new Error(`Tool "${toolName}" not found.`);
};

// --- 4. Define the Graph and Logic ---

// The conditional router decides if we should call a tool or end the process.
const shouldCallTool = (state: WeatherState): 'call_tool' | '__end__' => {
  const lastMessage = state.messages[state.messages.length - 1];
  if (lastMessage.role === 'assistant' && lastMessage.tool_calls) {
    return 'call_tool';
  }
  return '__end__';
};

// Instantiate the graph
const weatherGraph = new Graph<WeatherState>()
  .addNode('agent', agentNode)
  .addNode('tools', toolNode)
  .addConditionalEdge('agent', shouldCallTool, {
    'call_tool': 'tools',
    '__end__': '__END__'
  })
  .addEdge('tools', 'agent'); // Loop back to the agent after the tool runs

// --- 5. Run it! ---
const main = async () => {
  console.log("--- Starting Weather Agent ---");

  const initialState: WeatherState = {
    topic: "weather",
    messages: [{
      role: 'user',
      content: 'What is the weather in Tokyo?'
    }]
  };

  const executor = new Executor(weatherGraph);
  const finalState = await executor.execute(initialState);

  console.log("\n--- Final Result ---");
  console.log(finalState.messages[finalState.messages.length - 1].content);
};

// Export for use in other files
export { main as runWeatherAgent, weatherGraph, WeatherState };

// Run if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}
