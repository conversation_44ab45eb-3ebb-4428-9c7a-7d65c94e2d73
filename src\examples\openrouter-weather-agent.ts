// src/examples/openrouter-weather-agent.ts

import { <PERSON><PERSON>h, Executor, AgentState } from '../core';
import { createToolNode, shouldCallTools, getLastMessage } from '../lib';
import { OpenRouterClient, OpenRouterTool } from '../integrations/openrouter';

// --- 1. Define the State ---
interface WeatherState extends AgentState {
  topic: string;
}

// --- 2. Define <PERSON>ls ---
/**
 * Gets the current weather for a given location.
 * @param location The city and state, e.g., "San Francisco, CA"
 * @param unit Temperature unit preference
 */
const getCurrentWeather = async (args: { location: string; unit?: 'celsius' | 'fahrenheit' }): Promise<string> => {
  const { location, unit = 'fahrenheit' } = args;
  
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  if (location.toLowerCase().includes('san francisco')) {
    return JSON.stringify({ 
      location: "San Francisco, CA", 
      temperature: unit === 'celsius' ? '18°C' : '65°F', 
      condition: 'foggy',
      humidity: '85%'
    });
  }
  
  if (location.toLowerCase().includes('tokyo')) {
    return JSON.stringify({ 
      location: "Tokyo, Japan", 
      temperature: unit === 'celsius' ? '10°C' : '50°F', 
      condition: 'clear',
      humidity: '60%'
    });
  }
  
  if (location.toLowerCase().includes('london')) {
    return JSON.stringify({ 
      location: "London, UK", 
      temperature: unit === 'celsius' ? '12°C' : '54°F', 
      condition: 'rainy',
      humidity: '90%'
    });
  }
  
  return JSON.stringify({ 
    location, 
    temperature: 'unknown', 
    condition: 'unknown',
    error: 'Location not found in weather database'
  });
};

// --- 3. Define Tool Definitions for OpenRouter ---
const weatherTools: OpenRouterTool[] = [
  {
    type: 'function',
    function: {
      name: 'getCurrentWeather',
      description: 'Get the current weather conditions for a specific location',
      parameters: {
        type: 'object',
        properties: {
          location: {
            type: 'string',
            description: 'The city and state/country, e.g., "San Francisco, CA" or "Tokyo, Japan"'
          },
          unit: {
            type: 'string',
            enum: ['celsius', 'fahrenheit'],
            description: 'Temperature unit preference'
          }
        },
        required: ['location']
      }
    }
  }
];

// --- 4. Create Tool Map ---
const tools = {
  getCurrentWeather
};

// --- 5. Build the Graph with OpenRouter Integration ---
const buildWeatherGraph = (openRouterClient: OpenRouterClient): Graph<WeatherState> => {
  // Create agent node using OpenRouter
  const agentNode = openRouterClient.createAgentNode<WeatherState>({
    model: 'anthropic/claude-3.5-sonnet',
    temperature: 0.7,
    systemMessage: `You are a helpful weather assistant. You can provide current weather information for any location using the getCurrentWeather tool. 

When a user asks about weather, use the tool to get accurate, real-time information. Always specify the location clearly and ask for clarification if the location is ambiguous.

Be conversational and helpful in your responses.`,
    tools: weatherTools
  });
  
  // Create tool execution node
  const toolNode = createToolNode<WeatherState>(tools);
  
  return new Graph<WeatherState>()
    .addNode('agent', agentNode)
    .addNode('tools', toolNode)
    .setEntryPoint('agent')
    .addConditionalEdge('agent', shouldCallTools, {
      'tools': 'tools',
      '__end__': '__END__'
    })
    .addEdge('tools', 'agent');
};

// --- 6. Demo Function ---
const runOpenRouterWeatherDemo = async () => {
  console.log("=== OpenRouter Weather Agent Demo ===\n");
  
  // Check if API key is provided
  const apiKey = process.env.OPENROUTER_API_KEY;
  if (!apiKey) {
    console.error("❌ Please set OPENROUTER_API_KEY environment variable");
    console.log("   Get your API key from: https://openrouter.ai/keys");
    return;
  }
  
  // Create OpenRouter client
  const openRouterClient = new OpenRouterClient({
    apiKey,
    defaultModel: 'anthropic/claude-3.5-sonnet'
  });
  
  // Test connection
  console.log("🔗 Testing OpenRouter connection...");
  const isConnected = await openRouterClient.testConnection();
  if (!isConnected) {
    console.error("❌ Failed to connect to OpenRouter API");
    return;
  }
  console.log("✅ Connected to OpenRouter API\n");
  
  // Build graph
  const graph = buildWeatherGraph(openRouterClient);
  const executor = new Executor(graph);
  
  const queries = [
    "What's the weather like in San Francisco?",
    "How about Tokyo? I prefer Celsius.",
    "Is it raining in London right now?"
  ];
  
  for (const query of queries) {
    console.log(`\n--- Query: "${query}" ---`);
    
    const initialState: WeatherState = {
      topic: "weather",
      messages: [{ role: 'user', content: query }]
    };
    
    try {
      const finalState = await executor.executeWithCallback(
        initialState,
        (state, step) => {
          const lastMsg = getLastMessage(state);
          if (lastMsg?.role === 'assistant' && lastMsg.tool_calls) {
            console.log(`Step ${step}: 🤖 Agent is calling tools...`);
          } else if (lastMsg?.role === 'tool') {
            console.log(`Step ${step}: 🛠️ Tool executed`);
          } else if (lastMsg?.role === 'assistant' && lastMsg.content) {
            console.log(`Step ${step}: 🤖 Agent response ready`);
          }
        }
      );
      
      const finalAnswer = getLastMessage(finalState)?.content;
      console.log(`\n✅ Final Answer: ${finalAnswer}\n`);
      
    } catch (error) {
      console.error(`❌ Error: ${error instanceof Error ? error.message : String(error)}\n`);
    }
  }
};

// Export for use in other files
export { runOpenRouterWeatherDemo, buildWeatherGraph, WeatherState };

// Run if this file is executed directly
if (require.main === module) {
  runOpenRouterWeatherDemo().catch(console.error);
}
