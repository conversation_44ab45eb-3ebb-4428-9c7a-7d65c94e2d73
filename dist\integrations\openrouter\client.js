"use strict";
// src/integrations/openrouter/client.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenRouterClient = void 0;
const agent_helpers_1 = require("../../lib/agent-helpers");
/**
 * OpenRouter API client
 */
class OpenRouterClient {
    constructor(config) {
        this.config = {
            baseUrl: 'https://openrouter.ai/api/v1',
            defaultModel: 'anthropic/claude-3.5-sonnet',
            defaultTemperature: 0.7,
            timeout: 30000,
            ...config
        };
    }
    /**
     * Make a chat completion request to OpenRouter
     */
    async createChatCompletion(messages, options = {}) {
        const { model = this.config.defaultModel, temperature = this.config.defaultTemperature, maxTokens = 4096, tools } = options;
        // Convert MCP messages to OpenRouter format
        const openRouterMessages = messages
            .filter(msg => msg.role !== 'system' || msg.content) // Filter out empty system messages
            .map(msg => ({
            role: msg.role,
            content: msg.content || (msg.role === 'tool' ? msg.content : null)
        }));
        const requestBody = {
            model,
            messages: openRouterMessages,
            temperature,
            max_tokens: maxTokens
        };
        // Add tools if provided
        if (tools && tools.length > 0) {
            requestBody.tools = tools;
            requestBody.tool_choice = 'auto';
        }
        try {
            const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': 'https://ag3ntic.dev', // Optional: for OpenRouter analytics
                    'X-Title': 'AG3NTIC Framework' // Optional: for OpenRouter analytics
                },
                body: JSON.stringify(requestBody),
                signal: AbortSignal.timeout(this.config.timeout)
            });
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
            }
            const data = await response.json();
            const choice = data.choices?.[0];
            if (!choice) {
                throw new Error('No response choice returned from OpenRouter API');
            }
            // Convert OpenRouter response to MCP format
            const assistantMessage = {
                role: 'assistant',
                content: choice.message.content,
                tool_calls: choice.message.tool_calls?.map((tc) => ({
                    id: tc.id,
                    type: 'function',
                    function: {
                        name: tc.function.name,
                        arguments: tc.function.arguments
                    }
                }))
            };
            return assistantMessage;
        }
        catch (error) {
            if (error instanceof Error && error.name === 'AbortError') {
                throw new Error(`OpenRouter API request timeout after ${this.config.timeout}ms`);
            }
            throw error;
        }
    }
    /**
     * Create an agent node that uses OpenRouter for LLM calls
     */
    createAgentNode(options = {}) {
        return async (state) => {
            const { systemMessage } = options;
            // Prepare messages for the LLM
            let messages = [...state.messages];
            // Add system message if provided and not already present
            if (systemMessage && (messages.length === 0 || messages[0].role !== 'system')) {
                messages.unshift({
                    role: 'system',
                    content: systemMessage
                });
            }
            try {
                const assistantMessage = await this.createChatCompletion(messages, options);
                return (0, agent_helpers_1.addMessage)(state, assistantMessage);
            }
            catch (error) {
                throw new Error(`OpenRouter agent error: ${error instanceof Error ? error.message : String(error)}`);
            }
        };
    }
    /**
     * Test the connection to OpenRouter
     */
    async testConnection() {
        try {
            const testMessage = {
                role: 'user',
                content: 'Hello, this is a connection test.'
            };
            await this.createChatCompletion([testMessage], {
                model: this.config.defaultModel,
                maxTokens: 10
            });
            return true;
        }
        catch (error) {
            console.error('OpenRouter connection test failed:', error);
            return false;
        }
    }
    /**
     * Get available models from OpenRouter
     */
    async getAvailableModels() {
        try {
            const response = await fetch(`${this.config.baseUrl}/models`, {
                headers: {
                    'Authorization': `Bearer ${this.config.apiKey}`
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to fetch models: ${response.statusText}`);
            }
            const data = await response.json();
            return data.data?.map((model) => model.id) || [];
        }
        catch (error) {
            console.error('Failed to get available models:', error);
            return [];
        }
    }
}
exports.OpenRouterClient = OpenRouterClient;
//# sourceMappingURL=client.js.map