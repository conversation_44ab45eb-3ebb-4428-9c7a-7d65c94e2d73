"use strict";
// src/lib/agent-helpers.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLastMessage = getLastMessage;
exports.addMessage = addMessage;
exports.getMessagesByRole = getMessagesByRole;
exports.countMessagesByRole = countMessagesByRole;
exports.hasAssistantResponse = hasAssistantResponse;
exports.getConversationHistory = getConversationHistory;
exports.createStaticAgentNode = createStaticAgentNode;
exports.createConditionalAgentNode = createConditionalAgentNode;
/**
 * Get the last message from the state
 * @param state The current state
 * @returns The last message or undefined if no messages exist
 */
function getLastMessage(state) {
    return state.messages[state.messages.length - 1];
}
/**
 * Add a message to the state
 * @param state The current state
 * @param message The message to add
 * @returns A partial state update with the new message added
 */
function addMessage(state, message) {
    return {
        messages: [...state.messages, message]
    };
}
/**
 * Get all messages of a specific role
 * @param state The current state
 * @param role The role to filter by
 * @returns Array of messages with the specified role
 */
function getMessagesByRole(state, role) {
    return state.messages.filter(message => message.role === role);
}
/**
 * Count messages by role
 * @param state The current state
 * @param role The role to count
 * @returns Number of messages with the specified role
 */
function countMessagesByRole(state, role) {
    return getMessagesByRole(state, role).length;
}
/**
 * Check if the last message is from an assistant and has content
 * @param state The current state
 * @returns True if the last message is a completed assistant response
 */
function hasAssistantResponse(state) {
    const lastMessage = getLastMessage(state);
    return lastMessage?.role === 'assistant' && !!lastMessage.content;
}
/**
 * Get the conversation history as a formatted string
 * @param state The current state
 * @param includeSystem Whether to include system messages
 * @returns Formatted conversation string
 */
function getConversationHistory(state, includeSystem = false) {
    return state.messages
        .filter(msg => includeSystem || msg.role !== 'system')
        .map(msg => `${msg.role}: ${msg.content || '[tool_calls]'}`)
        .join('\n');
}
/**
 * Create a simple agent node that returns a static response
 * This is useful for testing or simple workflows
 * @param response The response content to return
 * @returns A node function that adds the response message
 */
function createStaticAgentNode(response) {
    return async (state) => {
        const newMessage = {
            role: 'assistant',
            content: response
        };
        return addMessage(state, newMessage);
    };
}
/**
 * Create a conditional agent node that responds based on state
 * @param responseFunction Function that generates response based on state
 * @returns A node function that calls the response function
 */
function createConditionalAgentNode(responseFunction) {
    return async (state) => {
        const response = responseFunction(state);
        const message = typeof response === 'string'
            ? { role: 'assistant', content: response }
            : response;
        return addMessage(state, message);
    };
}
//# sourceMappingURL=agent-helpers.js.map