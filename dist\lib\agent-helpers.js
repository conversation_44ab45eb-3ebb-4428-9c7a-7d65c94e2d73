"use strict";
// src/lib/agent-helpers.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearMessages = exports.getFirstMessage = exports.hasMessages = exports.getMessageCount = exports.createConditionalAgentNode = exports.createStaticAgentNode = exports.getConversationHistory = exports.hasAssistantResponse = exports.countMessagesByRole = exports.getMessagesByRole = exports.addMessage = exports.getLastMessage = void 0;
/**
 * Ultra-fast agent helper functions optimized for performance
 *
 * Optimizations:
 * - Direct array access (O(1) operations)
 * - Minimal object allocation
 * - Efficient loops over built-in methods
 * - Cached computations where possible
 */
/**
 * Get last message (O(1) operation)
 */
const getLastMessage = (state) => state.messages[state.messages.length - 1];
exports.getLastMessage = getLastMessage;
/**
 * Add message efficiently (minimal allocation)
 */
const addMessage = (state, message) => ({
    messages: [...state.messages, message]
});
exports.addMessage = addMessage;
/**
 * Get messages by role (optimized loop)
 */
const getMessagesByRole = (state, role) => {
    const result = [];
    const messages = state.messages;
    for (let i = 0; i < messages.length; i++) {
        if (messages[i].role === role) {
            result.push(messages[i]);
        }
    }
    return result;
};
exports.getMessagesByRole = getMessagesByRole;
/**
 * Count messages by role (optimized counter)
 */
const countMessagesByRole = (state, role) => {
    let count = 0;
    const messages = state.messages;
    for (let i = 0; i < messages.length; i++) {
        if (messages[i].role === role) {
            count++;
        }
    }
    return count;
};
exports.countMessagesByRole = countMessagesByRole;
/**
 * Check assistant response (fast boolean check)
 */
const hasAssistantResponse = (state) => {
    const lastMessage = (0, exports.getLastMessage)(state);
    return lastMessage?.role === 'assistant' && !!lastMessage.content;
};
exports.hasAssistantResponse = hasAssistantResponse;
/**
 * Get conversation history (optimized string building)
 */
const getConversationHistory = (state, includeSystem = false) => {
    const messages = state.messages;
    if (messages.length === 0)
        return '';
    let result = '';
    let first = true;
    for (let i = 0; i < messages.length; i++) {
        const msg = messages[i];
        if (!includeSystem && msg.role === 'system')
            continue;
        if (!first)
            result += '\n';
        result += `${msg.role}: ${msg.content || '[tool_calls]'}`;
        first = false;
    }
    return result;
};
exports.getConversationHistory = getConversationHistory;
/**
 * Create static agent node (pre-compiled message)
 */
const createStaticAgentNode = (response) => {
    // Pre-compile the message for performance
    const precompiledMessage = {
        role: 'assistant',
        content: response
    };
    return async (state) => (0, exports.addMessage)(state, precompiledMessage);
};
exports.createStaticAgentNode = createStaticAgentNode;
/**
 * Create conditional agent node (optimized)
 */
const createConditionalAgentNode = (responseFunction) => async (state) => {
    const response = responseFunction(state);
    const message = typeof response === 'string'
        ? { role: 'assistant', content: response }
        : response;
    return (0, exports.addMessage)(state, message);
};
exports.createConditionalAgentNode = createConditionalAgentNode;
/**
 * Additional optimized helpers
 */
/**
 * Get message count (O(1))
 */
const getMessageCount = (state) => state.messages.length;
exports.getMessageCount = getMessageCount;
/**
 * Check if state has messages (O(1))
 */
const hasMessages = (state) => state.messages.length > 0;
exports.hasMessages = hasMessages;
/**
 * Get first message (O(1))
 */
const getFirstMessage = (state) => state.messages[0];
exports.getFirstMessage = getFirstMessage;
/**
 * Clear messages (minimal allocation)
 */
const clearMessages = () => ({
    messages: []
});
exports.clearMessages = clearMessages;
//# sourceMappingURL=agent-helpers.js.map