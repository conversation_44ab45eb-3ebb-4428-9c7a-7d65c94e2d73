{"version": 3, "file": "agent.js", "sourceRoot": "", "sources": ["../../src/core/agent.ts"], "names": [], "mappings": ";;;AA4VA,kBAMC;AAlWD,6BAAwB;AAGxB,2CAAqC;AAmDrC;;;;;;;;;;GAUG;AACH,MAAa,KAAK;IAKhB,YAAY,MAA2B;QACrC,IAAI,CAAC,MAAM,GAAG;YACZ,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,EAAE;YACT,QAAQ,EAAE,EAAE;YACZ,MAAM,EAAE,IAAI,kBAAM,EAAU;YAC5B,aAAa,EAAE,EAAE;YACjB,aAAa,EAAE,EAAE;YACjB,oBAAoB,EAAE,KAAK;YAC3B,GAAG,MAAM;SACV,CAAC;QAEF,uCAAuC;QACvC,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACnC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,MAAM,CACX,MAA2B;QAE3B,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO;YACL,IAAI,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;YACpE,WAAW,EAAE,eAAe,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC3E,UAAU,EAAE,OAAC,CAAC,MAAM,CAAC;gBACnB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,4BAA4B,CAAC;aACzD,CAAC;YACF,OAAO,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;gBAC3B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBACrC,OAAO,MAAM,CAAC,WAAW,CAAC;YAC5B,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG,CACP,KAAsB,EACtB,OAA6B;QAE7B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,KAAK,GAAiC,EAAE,CAAC;QAC/C,IAAI,YAAoB,CAAC;QACzB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,mBAAmB;QACnB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,YAAY,GAAG;gBACb,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;gBAC5C,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;gBACjE,GAAG,OAAO;aACD,CAAC;QACd,CAAC;aAAM,CAAC;YACN,YAAY,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC;QAC9B,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACvB,YAAY,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC7D,CAAC;QAED,sCAAsC;QACtC,OAAO,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YAC7C,SAAS,EAAE,CAAC;YAEZ,IAAI,CAAC;gBACH,8BAA8B;gBAC9B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;gBAC7D,KAAK,CAAC,IAAI,CAAC;oBACT,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,WAAW,CAAC,OAAO;oBAC5B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC,CAAC;gBAEH,6BAA6B;gBAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAC/D,IAAI,YAAY,EAAE,CAAC;oBACjB,MAAM,aAAa,GAAG,MAAM,YAAY,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;oBACpE,OAAO;wBACL,GAAG,aAAa;wBAChB,UAAU,EAAE,YAAY;wBACxB,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,GAAG,aAAa,CAAC,KAAK,CAAC;qBAC1C,CAAC;gBACJ,CAAC;gBAED,iCAAiC;gBACjC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;gBACvE,IAAI,UAAU,EAAE,CAAC;oBACf,KAAK,CAAC,IAAI,CAAC;wBACT,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,UAAU,CAAC,OAAO;wBAC3B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;qBACtB,CAAC,CAAC;oBACH,SAAS,EAAE,CAAC;oBAEZ,mCAAmC;oBACnC,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CACnD,YAAY,EACZ,UAAU,CACX,CAAC;oBACF,KAAK,CAAC,IAAI,CAAC;wBACT,IAAI,EAAE,aAAa;wBACnB,OAAO,EAAE,eAAe,CAAC,OAAO;wBAChC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;qBACtB,CAAC,CAAC;oBAEH,YAAY,GAAG,eAAe,CAAC,KAAK,CAAC;gBACvC,CAAC;gBAED,uBAAuB;gBACvB,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;oBAClC,MAAM;gBACR,CAAC;gBAED,uCAAuC;gBACvC,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,IAAI,SAAS,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC5D,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;oBACnE,KAAK,CAAC,IAAI,CAAC;wBACT,IAAI,EAAE,YAAY;wBAClB,OAAO,EAAE,cAAc,CAAC,OAAO;wBAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;qBACtB,CAAC,CAAC;gBACL,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,2BAA2B;gBAC3B,KAAK,CAAC,IAAI,CAAC;oBACT,IAAI,EAAE,aAAa;oBACnB,OAAO,EAAE,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;oBAC7E,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC,CAAC;gBACH,MAAM;YACR,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAC7C,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QAE1D,OAAO;YACL,WAAW;YACX,UAAU,EAAE,YAAY;YACxB,UAAU,EAAE,IAAI;YAChB,KAAK;YACL,OAAO,EAAE;gBACP,UAAU,EAAE,KAAK,CAAC,MAAM;gBACxB,SAAS;gBACT,aAAa;aACd;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,KAAa;QAIzC,qDAAqD;QACrD,+BAA+B;QAC/B,OAAO;YACL,OAAO,EAAE,+DAA+D;YACxE,cAAc,EAAE,KAAK;SACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAAe;QACrC,yCAAyC;QACzC,uDAAuD;QACvD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC5C,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACvD,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CACzB,KAAa,EACb,OAAqD;QAErD,IAAI,CAAC,OAAO,CAAC,cAAc;YAAE,OAAO,IAAI,CAAC;QAEzC,qCAAqC;QACrC,kDAAkD;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,KAAa,EACb,MAA0D;QAE1D,uCAAuC;QACvC,OAAO;YACL,OAAO,EAAE,QAAQ,MAAM,CAAC,QAAQ,wBAAwB;YACxD,KAAK;SACN,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,KAAa;QAC5C,OAAO;YACL,OAAO,EAAE,kDAAkD;SAC5D,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,KAAa;QAC9B,qDAAqD;QACrD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,KAAa;QACtC,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC9D,OAAO,WAAW,EAAE,OAAO,IAAI,gBAAgB,CAAC;IAClD,CAAC;IAED,UAAU;IACV,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;IAC3B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IAC9B,CAAC;CACF;AAtRD,sBAsRC;AAED;;GAEG;AACI,KAAK,UAAU,GAAG,CACvB,KAAoB,EACpB,KAAsB,EACtB,OAA6B;IAE7B,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACnC,CAAC"}