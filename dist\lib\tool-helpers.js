"use strict";
// src/lib/tool-helpers.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.createToolLogger = exports.getToolCount = exports.hasToolFunction = exports.getToolNames = exports.createEnhancedToolNode = exports.createToolValidator = exports.createToolNode = exports.shouldCallTools = exports.hasToolCalls = exports.getToolCalls = void 0;
const agent_helpers_1 = require("./agent-helpers");
/**
 * Get tool calls (fast extraction)
 */
const getToolCalls = (state) => {
    const lastMessage = (0, agent_helpers_1.getLastMessage)(state);
    return (lastMessage?.role === 'assistant' && lastMessage.tool_calls)
        ? lastMessage.tool_calls
        : [];
};
exports.getToolCalls = getToolCalls;
/**
 * Check for tool calls (O(1) operation)
 */
const hasToolCalls = (state) => {
    const lastMessage = (0, agent_helpers_1.getLastMessage)(state);
    return lastMessage?.role === 'assistant' && !!lastMessage.tool_calls?.length;
};
exports.hasToolCalls = hasToolCalls;
/**
 * Fast routing for tool execution
 */
const shouldCallTools = (state) => (0, exports.hasToolCalls)(state) ? 'tools' : '__end__';
exports.shouldCallTools = shouldCallTools;
/**
 * Create optimized tool node (ultra-fast execution)
 */
const createToolNode = (tools) => {
    // Pre-compile tool names for faster error messages
    const toolNames = Object.keys(tools);
    return async (state) => {
        const lastMessage = (0, agent_helpers_1.getLastMessage)(state);
        if (!lastMessage?.tool_calls) {
            throw new Error('Tool node called without tool calls');
        }
        const toolResults = [];
        const toolCalls = lastMessage.tool_calls;
        // Optimized loop for tool execution
        for (let i = 0; i < toolCalls.length; i++) {
            const toolCall = toolCalls[i];
            const { id, function: { name, arguments: argsString } } = toolCall;
            try {
                // Fast tool lookup
                const toolFunction = tools[name];
                if (!toolFunction) {
                    throw new Error(`Tool "${name}" not found. Available: ${toolNames.join(', ')}`);
                }
                // Parse and execute
                const args = JSON.parse(argsString);
                const result = await toolFunction(args);
                // Create result message
                toolResults.push({
                    role: 'tool',
                    tool_call_id: id,
                    content: typeof result === 'string' ? result : JSON.stringify(result)
                });
            }
            catch (error) {
                // Fast error handling
                toolResults.push({
                    role: 'tool',
                    tool_call_id: id,
                    content: `Error: ${error instanceof Error ? error.message : String(error)}`
                });
            }
        }
        return {
            messages: [...state.messages, ...toolResults]
        };
    };
};
exports.createToolNode = createToolNode;
/**
 * Create fast tool validator (pre-compiled)
 */
const createToolValidator = (requiredTools, availableTools) => {
    // Pre-compute missing tools for faster validation
    const missing = requiredTools.filter(tool => !(tool in availableTools));
    if (missing.length > 0) {
        const errorMessage = `Missing required tools: ${missing.join(', ')}`;
        return () => { throw new Error(errorMessage); };
    }
    // Return no-op function if all tools are available
    return () => { };
};
exports.createToolValidator = createToolValidator;
/**
 * Create enhanced tool node (optimized with features)
 */
const createEnhancedToolNode = (tools, options = {}) => {
    const { logExecution = false, timeout = 30000, retries = 0 } = options;
    const toolNames = Object.keys(tools); // Pre-compile for error messages
    return async (state) => {
        const lastMessage = (0, agent_helpers_1.getLastMessage)(state);
        if (!lastMessage?.tool_calls) {
            throw new Error('Enhanced tool node called without tool calls');
        }
        const toolResults = [];
        const toolCalls = lastMessage.tool_calls;
        // Optimized execution loop
        for (let i = 0; i < toolCalls.length; i++) {
            const toolCall = toolCalls[i];
            const { id, function: { name, arguments: argsString } } = toolCall;
            if (logExecution) {
                console.log(`🛠️ Executing tool: ${name}`);
            }
            let attempts = 0;
            let success = false;
            while (attempts <= retries && !success) {
                try {
                    const toolFunction = tools[name];
                    if (!toolFunction) {
                        throw new Error(`Tool "${name}" not found. Available: ${toolNames.join(', ')}`);
                    }
                    const args = JSON.parse(argsString);
                    // Execute with timeout (optimized Promise.race)
                    const result = timeout > 0
                        ? await Promise.race([
                            toolFunction(args),
                            new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), timeout))
                        ])
                        : await toolFunction(args);
                    toolResults.push({
                        role: 'tool',
                        tool_call_id: id,
                        content: typeof result === 'string' ? result : JSON.stringify(result)
                    });
                    success = true;
                    if (logExecution) {
                        console.log(`✅ Tool ${name} completed`);
                    }
                }
                catch (error) {
                    attempts++;
                    if (attempts > retries) {
                        const errorMessage = `Error: ${error instanceof Error ? error.message : String(error)}`;
                        toolResults.push({
                            role: 'tool',
                            tool_call_id: id,
                            content: errorMessage
                        });
                        if (logExecution) {
                            console.error(`❌ Tool ${name} failed: ${errorMessage}`);
                        }
                    }
                    else if (logExecution) {
                        console.warn(`⚠️ Tool ${name} retrying... (${attempts}/${retries + 1})`);
                    }
                }
            }
        }
        return {
            messages: [...state.messages, ...toolResults]
        };
    };
};
exports.createEnhancedToolNode = createEnhancedToolNode;
/**
 * Additional optimized tool helpers
 */
/**
 * Fast tool name extraction
 */
const getToolNames = (tools) => Object.keys(tools);
exports.getToolNames = getToolNames;
/**
 * Check if tool exists (O(1))
 */
const hasToolFunction = (tools, name) => name in tools;
exports.hasToolFunction = hasToolFunction;
/**
 * Get tool count (O(1))
 */
const getToolCount = (tools) => Object.keys(tools).length;
exports.getToolCount = getToolCount;
/**
 * Create tool execution logger (optimized)
 */
const createToolLogger = (prefix = '🛠️') => ({
    start: (name) => console.log(`${prefix} Executing: ${name}`),
    success: (name) => console.log(`✅ Completed: ${name}`),
    error: (name, error) => console.error(`❌ Failed: ${name} - ${error}`)
});
exports.createToolLogger = createToolLogger;
//# sourceMappingURL=tool-helpers.js.map