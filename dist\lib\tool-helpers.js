"use strict";
// src/lib/tool-helpers.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.getToolCalls = getToolCalls;
exports.hasToolCalls = hasToolCalls;
exports.shouldCallTools = shouldCallTools;
exports.createToolNode = createToolNode;
exports.createToolValidator = createToolValidator;
exports.createEnhancedToolNode = createEnhancedToolNode;
const agent_helpers_1 = require("./agent-helpers");
/**
 * Get tool calls from the last assistant message
 * @param state The current state
 * @returns Array of tool calls or empty array if none exist
 */
function getToolCalls(state) {
    const lastMessage = (0, agent_helpers_1.getLastMessage)(state);
    if (lastMessage?.role === 'assistant' && lastMessage.tool_calls) {
        return lastMessage.tool_calls;
    }
    return [];
}
/**
 * Check if the last message contains tool calls
 * @param state The current state
 * @returns True if the last message has tool calls
 */
function hasToolCalls(state) {
    return getToolCalls(state).length > 0;
}
/**
 * Simple routing function for agent -> tool -> agent loops
 * @param state The current state
 * @returns 'tools' if the last message has tool calls, '__end__' otherwise
 */
function shouldCallTools(state) {
    return hasToolCalls(state) ? 'tools' : '__end__';
}
/**
 * Create a tool node that can execute a map of tools
 * @param tools Map of tool names to their executable functions
 * @returns A node function that can execute tools based on the last assistant message
 */
function createToolNode(tools) {
    return async (state) => {
        const lastMessage = (0, agent_helpers_1.getLastMessage)(state);
        if (!lastMessage || lastMessage.role !== 'assistant' || !lastMessage.tool_calls) {
            throw new Error('Tool node called but last message is not an assistant message with tool calls');
        }
        const toolResults = [];
        for (const toolCall of lastMessage.tool_calls) {
            const { id, function: { name, arguments: argsString } } = toolCall;
            try {
                // Parse arguments
                const args = JSON.parse(argsString);
                // Find the tool function
                const toolFunction = tools[name];
                if (!toolFunction) {
                    throw new Error(`Tool "${name}" not found in available tools: ${Object.keys(tools).join(', ')}`);
                }
                // Execute the tool
                const result = await toolFunction(args);
                // Create tool result message
                toolResults.push({
                    role: 'tool',
                    tool_call_id: id,
                    content: typeof result === 'string' ? result : JSON.stringify(result)
                });
            }
            catch (error) {
                // Create error result message
                toolResults.push({
                    role: 'tool',
                    tool_call_id: id,
                    content: `Error executing tool "${name}": ${error instanceof Error ? error.message : String(error)}`
                });
            }
        }
        return {
            messages: [...state.messages, ...toolResults]
        };
    };
}
/**
 * Create a tool validation function that checks if required tools are available
 * @param requiredTools Array of tool names that must be available
 * @param availableTools Map of available tools
 * @returns Validation function that throws if tools are missing
 */
function createToolValidator(requiredTools, availableTools) {
    return () => {
        const missing = requiredTools.filter(tool => !(tool in availableTools));
        if (missing.length > 0) {
            throw new Error(`Missing required tools: ${missing.join(', ')}`);
        }
    };
}
/**
 * Create a tool execution wrapper with error handling and logging
 * @param tools Map of tool functions
 * @param options Configuration options
 * @returns Enhanced tool node with additional features
 */
function createEnhancedToolNode(tools, options = {}) {
    const { logExecution = false, timeout = 30000, retries = 0 } = options;
    return async (state) => {
        const lastMessage = (0, agent_helpers_1.getLastMessage)(state);
        if (!lastMessage || lastMessage.role !== 'assistant' || !lastMessage.tool_calls) {
            throw new Error('Enhanced tool node called but last message is not an assistant message with tool calls');
        }
        const toolResults = [];
        for (const toolCall of lastMessage.tool_calls) {
            const { id, function: { name, arguments: argsString } } = toolCall;
            if (logExecution) {
                console.log(`🛠️ Executing tool: ${name} with args: ${argsString}`);
            }
            let attempts = 0;
            let success = false;
            while (attempts <= retries && !success) {
                try {
                    const args = JSON.parse(argsString);
                    const toolFunction = tools[name];
                    if (!toolFunction) {
                        throw new Error(`Tool "${name}" not found`);
                    }
                    // Execute with timeout
                    const result = await Promise.race([
                        toolFunction(args),
                        new Promise((_, reject) => setTimeout(() => reject(new Error('Tool execution timeout')), timeout))
                    ]);
                    toolResults.push({
                        role: 'tool',
                        tool_call_id: id,
                        content: typeof result === 'string' ? result : JSON.stringify(result)
                    });
                    success = true;
                    if (logExecution) {
                        console.log(`✅ Tool ${name} completed successfully`);
                    }
                }
                catch (error) {
                    attempts++;
                    if (attempts > retries) {
                        const errorMessage = `Error executing tool "${name}" (${attempts} attempts): ${error instanceof Error ? error.message : String(error)}`;
                        toolResults.push({
                            role: 'tool',
                            tool_call_id: id,
                            content: errorMessage
                        });
                        if (logExecution) {
                            console.error(`❌ ${errorMessage}`);
                        }
                    }
                    else if (logExecution) {
                        console.warn(`⚠️ Tool ${name} failed, retrying... (attempt ${attempts}/${retries + 1})`);
                    }
                }
            }
        }
        return {
            messages: [...state.messages, ...toolResults]
        };
    };
}
//# sourceMappingURL=tool-helpers.js.map