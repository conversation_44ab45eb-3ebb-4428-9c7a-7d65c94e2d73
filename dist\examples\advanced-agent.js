"use strict";
// src/examples/advanced-agent.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildAdvancedGraph = exports.runAdvancedDemo = void 0;
const core_1 = require("../core");
// --- 2. Define Tools ---
/**
 * Gets the current weather for a given location.
 * @param location The city and state, e.g., "San Francisco, CA"
 */
const getCurrentWeather = async (args) => {
    const { location, unit = 'fahrenheit' } = args;
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 100));
    if (location.toLowerCase().includes('san francisco')) {
        return JSON.stringify({
            location: "San Francisco, CA",
            temperature: unit === 'celsius' ? '18°C' : '65°F',
            condition: 'foggy',
            humidity: '85%'
        });
    }
    if (location.toLowerCase().includes('tokyo')) {
        return JSON.stringify({
            location: "Tokyo, Japan",
            temperature: unit === 'celsius' ? '10°C' : '50°F',
            condition: 'clear',
            humidity: '60%'
        });
    }
    return JSON.stringify({
        location,
        temperature: 'unknown',
        condition: 'unknown',
        error: 'Location not found in weather database'
    });
};
/**
 * Searches the web for information.
 * @param query The search query
 */
const searchWeb = async (args) => {
    const { query, max_results = 3 } = args;
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 200));
    // Mock search results
    const mockResults = [
        { title: `${query} - Wikipedia`, url: 'https://wikipedia.org', snippet: `Information about ${query}...` },
        { title: `${query} News`, url: 'https://news.com', snippet: `Latest news about ${query}...` },
        { title: `${query} Guide`, url: 'https://guide.com', snippet: `Complete guide to ${query}...` }
    ].slice(0, max_results);
    return JSON.stringify({ results: mockResults, query });
};
/**
 * Performs a calculation.
 * @param expression Mathematical expression to evaluate
 */
const calculate = async (args) => {
    const { expression } = args;
    try {
        // Simple calculator - in production, use a proper math parser
        const result = eval(expression.replace(/[^0-9+\-*/().\s]/g, ''));
        return JSON.stringify({ expression, result });
    }
    catch (error) {
        return JSON.stringify({
            expression,
            error: 'Invalid mathematical expression',
            message: error instanceof Error ? error.message : String(error)
        });
    }
};
// --- 3. Create Tool Map ---
const tools = {
    getCurrentWeather,
    searchWeb,
    calculate
};
// --- 4. Create Agent Node (Simplified Mock) ---
const createMockAgentNode = () => {
    return async (state) => {
        const lastMessage = (0, core_1.getLastMessage)(state);
        console.log(`\n🤖 Agent processing: ${lastMessage?.role}: ${lastMessage?.content}`);
        // Simple logic to determine what tool to call based on user input
        if (lastMessage?.role === 'user') {
            const content = lastMessage.content.toLowerCase();
            if (content.includes('weather')) {
                // Extract location from the query (simple approach)
                const locationMatch = content.match(/weather.*?in\s+([^?]+)/);
                const location = locationMatch ? locationMatch[1].trim() : 'San Francisco';
                const newMessages = [
                    ...state.messages,
                    {
                        role: 'assistant',
                        content: null,
                        tool_calls: [{
                                id: `weather_${Date.now()}`,
                                type: 'function',
                                function: {
                                    name: 'getCurrentWeather',
                                    arguments: JSON.stringify({ location })
                                }
                            }]
                    }
                ];
                return { messages: newMessages };
            }
            if (content.includes('search') || content.includes('find')) {
                const query = content.replace(/search|find/g, '').trim();
                const newMessages = [
                    ...state.messages,
                    {
                        role: 'assistant',
                        content: null,
                        tool_calls: [{
                                id: `search_${Date.now()}`,
                                type: 'function',
                                function: {
                                    name: 'searchWeb',
                                    arguments: JSON.stringify({ query })
                                }
                            }]
                    }
                ];
                return { messages: newMessages };
            }
            if (content.includes('calculate') || content.match(/[\d+\-*/]/)) {
                const expression = content.replace(/calculate|what is|what's/g, '').trim();
                const newMessages = [
                    ...state.messages,
                    {
                        role: 'assistant',
                        content: null,
                        tool_calls: [{
                                id: `calc_${Date.now()}`,
                                type: 'function',
                                function: {
                                    name: 'calculate',
                                    arguments: JSON.stringify({ expression })
                                }
                            }]
                    }
                ];
                return { messages: newMessages };
            }
            // Default response
            const newMessages = [
                ...state.messages,
                {
                    role: 'assistant',
                    content: "I can help you with weather information, web searches, or calculations. What would you like to know?"
                }
            ];
            return { messages: newMessages };
        }
        // Handle tool responses
        if (lastMessage?.role === 'tool') {
            const toolResult = JSON.parse(lastMessage.content);
            let response = '';
            if (toolResult.temperature) {
                response = `The weather in ${toolResult.location} is ${toolResult.temperature} with ${toolResult.condition} conditions.`;
            }
            else if (toolResult.results) {
                response = `I found ${toolResult.results.length} results for "${toolResult.query}". Here are the top results: ${toolResult.results.map((r) => r.title).join(', ')}.`;
            }
            else if (toolResult.result !== undefined) {
                response = `The result of ${toolResult.expression} is ${toolResult.result}.`;
            }
            else if (toolResult.error) {
                response = `I encountered an error: ${toolResult.error}`;
            }
            else {
                response = `I received this information: ${lastMessage.content}`;
            }
            const newMessages = [
                ...state.messages,
                { role: 'assistant', content: response }
            ];
            return { messages: newMessages };
        }
        return {};
    };
};
// --- 5. Build the Graph ---
const buildAdvancedGraph = () => {
    const agentNode = createMockAgentNode();
    const toolNode = (0, core_1.createToolNode)(tools);
    return new core_1.Graph()
        .addNode('agent', agentNode)
        .addNode('tools', toolNode)
        .setEntryPoint('agent')
        .addConditionalEdge('agent', core_1.shouldCallTools, {
        'tools': 'tools',
        '__end__': '__END__'
    })
        .addEdge('tools', 'agent');
};
exports.buildAdvancedGraph = buildAdvancedGraph;
// --- 6. Demo Function ---
const runAdvancedDemo = async () => {
    console.log("=== Advanced AG3NTIC Agent Demo ===\n");
    const graph = buildAdvancedGraph();
    const executor = new core_1.Executor(graph);
    const queries = [
        "What's the weather in Tokyo?",
        "Search for information about TypeScript",
        "Calculate 15 * 7 + 23"
    ];
    for (const query of queries) {
        console.log(`\n--- Query: "${query}" ---`);
        const initialState = {
            userQuery: query,
            context: {},
            messages: [{ role: 'user', content: query }]
        };
        try {
            const finalState = await executor.executeWithCallback(initialState, (state, step) => {
                const lastMsg = (0, core_1.getLastMessage)(state);
                console.log(`Step ${step}: ${lastMsg?.role} - ${lastMsg?.content || '[tool_calls]'}`);
            });
            const finalAnswer = (0, core_1.getLastMessage)(finalState)?.content;
            console.log(`\n✅ Final Answer: ${finalAnswer}\n`);
        }
        catch (error) {
            console.error(`❌ Error: ${error instanceof Error ? error.message : String(error)}\n`);
        }
    }
};
exports.runAdvancedDemo = runAdvancedDemo;
// Run if this file is executed directly
if (require.main === module) {
    runAdvancedDemo().catch(console.error);
}
//# sourceMappingURL=advanced-agent.js.map