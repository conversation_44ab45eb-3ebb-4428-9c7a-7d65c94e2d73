"use strict";
// src/lib/templates.ts
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createOrchestratorAgent = exports.createAutoMCPAgent = exports.createMCPExecutorAgent = exports.createPlannerAgent = exports.createExecutorAgent = void 0;
const graph_1 = require("../core/graph");
const executor_1 = require("../core/executor");
const tool_helpers_1 = require("./tool-helpers");
const agent_helpers_1 = require("./agent-helpers");
/**
 * Create ultra-fast executor agent (optimized pattern)
 *
 * Key optimizations:
 * - Uses optimized tool execution from tool-helpers
 * - Pre-compiled conditional function
 * - Minimal graph construction overhead
 */
const createExecutorAgent = (llmNode, tools) => {
    // Pre-compile conditional function for performance
    const shouldCallTool = (state) => {
        const lastMessage = (0, agent_helpers_1.getLastMessage)(state);
        return (lastMessage?.role === 'assistant' && lastMessage.tool_calls) ? 'tools' : '__end__';
    };
    // Use optimized tool node
    const toolNode = (0, tool_helpers_1.createToolNode)(tools);
    return new graph_1.Graph()
        .addNode('agent', llmNode)
        .addNode('tools', toolNode)
        .setEntryPoint('agent')
        .addConditionalEdge('agent', shouldCallTool, {
        'tools': 'tools',
        '__end__': '__END__'
    })
        .addEdge('tools', 'agent');
};
exports.createExecutorAgent = createExecutorAgent;
/**
 * Create a planner agent that breaks down complex goals into actionable steps
 *
 * This agent specializes in:
 * 1. Analyzing complex goals or requests
 * 2. Breaking them down into logical, sequential steps
 * 3. Identifying dependencies between steps
 * 4. Creating a structured execution plan
 *
 * @param llmNode - The LLM node function that generates plans
 * @param options - Configuration options for the planner
 * @returns A graph that implements the planner pattern
 */
const createPlannerAgent = (llmNode, options = {}) => {
    const { maxSteps = 10, strategy = 'sequential', includeTimeEstimates = false } = options;
    const plannerNode = async (state) => {
        const lastMessage = state.messages[state.messages.length - 1];
        if (lastMessage.role !== 'user') {
            throw new Error("Planner node expects a user message with the goal to plan for.");
        }
        console.log(`\n📋 Planning Strategy: ${strategy}`);
        console.log(`📋 Max Steps: ${maxSteps}`);
        // Call the LLM to generate a response
        const llmResponse = await llmNode(state);
        const updatedState = { ...state, ...llmResponse };
        // Extract plan from the LLM response (this is a simplified version)
        // In a real implementation, you'd parse the LLM response to extract structured plan data
        const plan = generateMockPlan(lastMessage.content, strategy, maxSteps, includeTimeEstimates);
        return {
            ...updatedState,
            plan,
            currentStep: 0,
            planningStrategy: strategy
        };
    };
    return new graph_1.Graph()
        .addNode('planner', plannerNode)
        .setEntryPoint('planner')
        .addEdge('planner', '__END__');
};
exports.createPlannerAgent = createPlannerAgent;
/**
 * Generate a mock plan for demonstration purposes
 * In a real implementation, this would be replaced by LLM-generated structured output
 */
function generateMockPlan(goal, strategy, maxSteps, includeTimeEstimates) {
    const steps = [];
    // Simple heuristic-based planning for demo
    console.log(`📋 Generating ${strategy} plan for: ${goal}`);
    const keywords = goal.toLowerCase().split(' ');
    if (keywords.includes('research') || keywords.includes('analyze')) {
        steps.push({
            id: 'research-1',
            description: 'Gather initial information and define scope',
            priority: 'high',
            status: 'pending',
            ...(includeTimeEstimates && { estimatedTime: 30 })
        });
    }
    if (keywords.includes('plan') || keywords.includes('design')) {
        const designStep = {
            id: 'design-1',
            description: 'Create detailed design and specifications',
            priority: 'high',
            status: 'pending'
        };
        if (steps.length > 0) {
            designStep.dependencies = [steps[0].id];
        }
        if (includeTimeEstimates) {
            designStep.estimatedTime = 60;
        }
        steps.push(designStep);
    }
    if (keywords.includes('implement') || keywords.includes('build') || keywords.includes('create')) {
        const implementStep = {
            id: 'implement-1',
            description: 'Implement the solution according to specifications',
            priority: 'medium',
            status: 'pending'
        };
        if (steps.length > 0) {
            implementStep.dependencies = [steps[steps.length - 1].id];
        }
        if (includeTimeEstimates) {
            implementStep.estimatedTime = 120;
        }
        steps.push(implementStep);
    }
    if (keywords.includes('test') || keywords.includes('validate')) {
        const testStep = {
            id: 'test-1',
            description: 'Test and validate the implementation',
            priority: 'medium',
            status: 'pending'
        };
        if (steps.length > 0) {
            testStep.dependencies = [steps[steps.length - 1].id];
        }
        if (includeTimeEstimates) {
            testStep.estimatedTime = 45;
        }
        steps.push(testStep);
    }
    // Always add a completion step
    const completionStep = {
        id: 'complete-1',
        description: 'Review results and finalize deliverables',
        priority: 'low',
        status: 'pending'
    };
    if (steps.length > 0) {
        completionStep.dependencies = [steps[steps.length - 1].id];
    }
    if (includeTimeEstimates) {
        completionStep.estimatedTime = 15;
    }
    steps.push(completionStep);
    return steps.slice(0, maxSteps);
}
// --- MCP-Enhanced Templates ---
/**
 * Create an MCP-enabled executor agent that can use both local tools and remote MCP server tools
 *
 * @param llmNode - The LLM node function
 * @param localTools - Local tool functions
 * @param mcpClients - Array of connected MCP clients
 * @returns A graph that can execute both local and remote tools
 */
const createMCPExecutorAgent = (llmNode, localTools = {}, mcpClients = []) => {
    const mcpToolExecutorNode = async (state) => {
        const lastMessage = state.messages[state.messages.length - 1];
        if (!lastMessage.tool_calls) {
            throw new Error("Tool node called but no tool_calls found in the last message.");
        }
        const toolCall = lastMessage.tool_calls[0];
        const toolName = toolCall.function.name;
        // Check local tools first
        if (localTools[toolName]) {
            console.log(`\n🔧 Executing Local Tool: ${toolName}`);
            const args = JSON.parse(toolCall.function.arguments);
            const result = await Promise.resolve(localTools[toolName](args));
            const newMessages = [
                ...state.messages,
                { role: 'tool', tool_call_id: toolCall.id, content: JSON.stringify(result, null, 2) }
            ];
            return { messages: newMessages };
        }
        // Check MCP clients for remote tools
        for (const client of mcpClients) {
            if (!client.isConnected())
                continue;
            try {
                const tools = await client.listTools();
                const mcpTool = tools.find((t) => t.name === toolName);
                if (mcpTool) {
                    console.log(`\n🌐 Executing MCP Tool: ${toolName}`);
                    const args = JSON.parse(toolCall.function.arguments);
                    const result = await client.callTool(toolName, args);
                    // Convert MCP result to string
                    let resultText = '';
                    if (result.content && result.content.length > 0) {
                        resultText = result.content
                            .filter((c) => c.type === 'text')
                            .map((c) => c.text)
                            .join('\n');
                    }
                    const newMessages = [
                        ...state.messages,
                        { role: 'tool', tool_call_id: toolCall.id, content: resultText || JSON.stringify(result) }
                    ];
                    return { messages: newMessages };
                }
            }
            catch (error) {
                console.error(`❌ MCP tool execution failed for ${toolName}:`, error);
            }
        }
        throw new Error(`Tool "${toolName}" not found in local tools or MCP servers.`);
    };
    const shouldCallTool = (state) => {
        const lastMessage = state.messages[state.messages.length - 1];
        return lastMessage.role === 'assistant' && lastMessage.tool_calls ? 'call_tool' : '__end__';
    };
    return new graph_1.Graph()
        .addNode('agent', llmNode)
        .addNode('tools', mcpToolExecutorNode)
        .setEntryPoint('agent')
        .addConditionalEdge('agent', shouldCallTool, {
        'call_tool': 'tools',
        '__end__': '__END__'
    })
        .addEdge('tools', 'agent');
};
exports.createMCPExecutorAgent = createMCPExecutorAgent;
/**
 * Create an MCP-enabled agent that automatically connects to specified MCP servers
 *
 * @param llmNode - The LLM node function
 * @param localTools - Local tool functions
 * @param mcpServers - Array of MCP server configurations
 * @returns Promise resolving to an agent with MCP capabilities
 */
const createAutoMCPAgent = async (llmNode, localTools = {}, mcpServers = []) => {
    const mcpClients = [];
    // Connect to all MCP servers
    for (const serverConfig of mcpServers) {
        try {
            const { createMCPClient } = await Promise.resolve().then(() => __importStar(require('../integrations/mcp')));
            const client = await createMCPClient({
                name: 'ag3ntic-agent',
                version: '1.0.0',
                serverUrl: serverConfig.url,
                transport: serverConfig.transport || 'http'
            });
            mcpClients.push(client);
            console.log(`✅ Connected to MCP server: ${serverConfig.name}`);
        }
        catch (error) {
            console.warn(`⚠️  Failed to connect to MCP server ${serverConfig.name}:`, error);
        }
    }
    const graph = (0, exports.createMCPExecutorAgent)(llmNode, localTools, mcpClients);
    return {
        graph,
        mcpClients,
        cleanup: async () => {
            for (const client of mcpClients) {
                await client.disconnect();
            }
        }
    };
};
exports.createAutoMCPAgent = createAutoMCPAgent;
/**
 * Create an orchestrator agent that coordinates multiple worker agents
 *
 * This agent specializes in:
 * 1. Taking a plan and coordinating its execution
 * 2. Assigning tasks to the most suitable worker agents
 * 3. Managing dependencies between tasks
 * 4. Monitoring progress and handling failures
 * 5. Synthesizing results from multiple workers
 *
 * @param llmNode - The LLM node function that makes orchestration decisions
 * @param workers - Array of available worker agents
 * @param options - Configuration options for the orchestrator
 * @returns A graph that implements the orchestrator pattern
 */
const createOrchestratorAgent = (_llmNode, workers, options = {}) => {
    const { maxConcurrentTasks = 3, timeoutPerTask = 300000 // 5 minutes
     } = options;
    // Note: retryFailedTasks would be used in a more sophisticated implementation
    const retryFailedTasks = options.retryFailedTasks ?? true;
    console.log(`🎭 Orchestrator configured with retry: ${retryFailedTasks}`);
    const orchestratorNode = async (state) => {
        console.log(`\n🎭 Orchestrator coordinating ${workers.length} workers`);
        console.log(`🎭 Max concurrent tasks: ${maxConcurrentTasks}`);
        if (!state.plan || state.plan.length === 0) {
            throw new Error("Orchestrator requires a plan to execute. Use a planner agent first.");
        }
        // Initialize step executions if not present
        if (!state.stepExecutions) {
            const stepExecutions = state.plan.map(step => ({
                stepId: step.id,
                status: 'pending'
            }));
            return {
                stepExecutions,
                orchestrationStatus: 'executing',
                currentStepIndex: 0,
                workers
            };
        }
        // Find next executable step
        const nextStep = findNextExecutableStep(state.stepExecutions, state.plan);
        if (!nextStep) {
            // All steps completed or no more executable steps
            const allCompleted = state.stepExecutions.every(exec => exec.status === 'completed');
            if (allCompleted) {
                console.log("🎉 All steps completed successfully!");
                return {
                    orchestrationStatus: 'completed'
                };
            }
            else {
                console.log("⚠️  No more executable steps, but not all completed");
                return {
                    orchestrationStatus: 'failed'
                };
            }
        }
        // Assign worker to the step
        const assignedWorker = assignWorkerToStep(nextStep.step, workers);
        if (!assignedWorker) {
            console.log(`❌ No suitable worker found for step: ${nextStep.step.description}`);
            const updatedExecutions = state.stepExecutions.map(exec => exec.stepId === nextStep.step.id
                ? { ...exec, status: 'failed', error: 'No suitable worker available' }
                : exec);
            return { stepExecutions: updatedExecutions };
        }
        console.log(`👷 Assigning "${nextStep.step.description}" to ${assignedWorker.name}`);
        // Execute the step with the assigned worker
        try {
            const stepResult = await executeStepWithWorker(nextStep.step, assignedWorker, timeoutPerTask);
            const updatedExecutions = state.stepExecutions.map(exec => exec.stepId === nextStep.step.id
                ? {
                    ...exec,
                    status: 'completed',
                    assignedWorker: assignedWorker.id,
                    endTime: new Date(),
                    result: stepResult
                }
                : exec);
            return { stepExecutions: updatedExecutions };
        }
        catch (error) {
            console.log(`❌ Step execution failed: ${error instanceof Error ? error.message : String(error)}`);
            const updatedExecutions = state.stepExecutions.map(exec => exec.stepId === nextStep.step.id
                ? {
                    ...exec,
                    status: 'failed',
                    assignedWorker: assignedWorker.id,
                    endTime: new Date(),
                    error: error instanceof Error ? error.message : String(error)
                }
                : exec);
            return { stepExecutions: updatedExecutions };
        }
    };
    const shouldContinue = (state) => {
        if (!state.stepExecutions)
            return 'continue';
        const hasMoreWork = state.stepExecutions.some(exec => exec.status === 'pending' || exec.status === 'assigned' || exec.status === 'in_progress');
        return hasMoreWork ? 'continue' : '__end__';
    };
    return new graph_1.Graph()
        .addNode('orchestrator', orchestratorNode)
        .setEntryPoint('orchestrator')
        .addConditionalEdge('orchestrator', shouldContinue, {
        'continue': 'orchestrator',
        '__end__': '__END__'
    });
};
exports.createOrchestratorAgent = createOrchestratorAgent;
/**
 * Helper functions for orchestrator
 */
function findNextExecutableStep(executions, plan) {
    for (const execution of executions) {
        if (execution.status !== 'pending')
            continue;
        const step = plan.find(s => s.id === execution.stepId);
        if (!step)
            continue;
        // Check if all dependencies are completed
        if (step.dependencies) {
            const dependenciesCompleted = step.dependencies.every(depId => executions.find(exec => exec.stepId === depId)?.status === 'completed');
            if (!dependenciesCompleted)
                continue;
        }
        return { step, execution };
    }
    return null;
}
function assignWorkerToStep(step, workers) {
    // Simple assignment based on step description keywords
    const stepKeywords = step.description.toLowerCase().split(' ');
    let bestWorker = null;
    let bestScore = 0;
    for (const worker of workers) {
        if (worker.isAvailable === false)
            continue;
        let score = 0;
        // Check capabilities match
        for (const capability of worker.capabilities) {
            if (stepKeywords.some(keyword => capability.toLowerCase().includes(keyword))) {
                score += 2;
            }
        }
        // Check specializations match
        if (worker.specializations) {
            for (const specialization of worker.specializations) {
                if (stepKeywords.some(keyword => specialization.toLowerCase().includes(keyword))) {
                    score += 3;
                }
            }
        }
        if (score > bestScore) {
            bestScore = score;
            bestWorker = worker;
        }
    }
    return bestWorker;
}
async function executeStepWithWorker(step, worker, timeout) {
    console.log(`🔄 Executing step "${step.description}" with ${worker.name}`);
    // Create a timeout promise
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`Step execution timed out after ${timeout}ms`)), timeout);
    });
    // Execute the worker's graph with the step as input
    const executor = new executor_1.Executor(worker.graph);
    const executionPromise = executor.execute({
        messages: [
            { role: 'user', content: `Execute this task: ${step.description}` }
        ]
    });
    // Race between execution and timeout
    const result = await Promise.race([executionPromise, timeoutPromise]);
    return result;
}
//# sourceMappingURL=templates.js.map