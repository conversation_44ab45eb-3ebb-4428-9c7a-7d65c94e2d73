import { AgentState, MCPMessage, MCPAssistantMessage, NodeFunction } from "../core/types";
/**
 * Get the last message from the state
 * @param state The current state
 * @returns The last message or undefined if no messages exist
 */
export declare function getLastMessage<TState extends AgentState>(state: TState): MCPMessage | undefined;
/**
 * Add a message to the state
 * @param state The current state
 * @param message The message to add
 * @returns A partial state update with the new message added
 */
export declare function addMessage<TState extends AgentState>(state: TState, message: MCPMessage): Partial<TState>;
/**
 * Get all messages of a specific role
 * @param state The current state
 * @param role The role to filter by
 * @returns Array of messages with the specified role
 */
export declare function getMessagesByRole<TState extends AgentState>(state: TState, role: MCPMessage['role']): MCPMessage[];
/**
 * Count messages by role
 * @param state The current state
 * @param role The role to count
 * @returns Number of messages with the specified role
 */
export declare function countMessagesByRole<TState extends AgentState>(state: TState, role: MCPMessage['role']): number;
/**
 * Check if the last message is from an assistant and has content
 * @param state The current state
 * @returns True if the last message is a completed assistant response
 */
export declare function hasAssistantResponse<TState extends AgentState>(state: TState): boolean;
/**
 * Get the conversation history as a formatted string
 * @param state The current state
 * @param includeSystem Whether to include system messages
 * @returns Formatted conversation string
 */
export declare function getConversationHistory<TState extends AgentState>(state: TState, includeSystem?: boolean): string;
/**
 * Create a simple agent node that returns a static response
 * This is useful for testing or simple workflows
 * @param response The response content to return
 * @returns A node function that adds the response message
 */
export declare function createStaticAgentNode<TState extends AgentState>(response: string): NodeFunction<TState>;
/**
 * Create a conditional agent node that responds based on state
 * @param responseFunction Function that generates response based on state
 * @returns A node function that calls the response function
 */
export declare function createConditionalAgentNode<TState extends AgentState>(responseFunction: (state: TState) => string | MCPAssistantMessage): NodeFunction<TState>;
//# sourceMappingURL=agent-helpers.d.ts.map