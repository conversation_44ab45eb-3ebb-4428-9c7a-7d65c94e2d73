import { AgentState, MCPMessage, MCPAssistantMessage, NodeFunction } from "../core/types";
/**
 * Ultra-fast agent helper functions optimized for performance
 *
 * Optimizations:
 * - Direct array access (O(1) operations)
 * - Minimal object allocation
 * - Efficient loops over built-in methods
 * - Cached computations where possible
 */
/**
 * Get last message (O(1) operation)
 */
export declare const getLastMessage: <TState extends AgentState>(state: TState) => MCPMessage | undefined;
/**
 * Add message efficiently (minimal allocation)
 */
export declare const addMessage: <TState extends AgentState>(state: TState, message: MCPMessage) => Partial<TState>;
/**
 * Get messages by role (optimized loop)
 */
export declare const getMessagesByRole: <TState extends AgentState>(state: TState, role: MCPMessage["role"]) => MCPMessage[];
/**
 * Count messages by role (optimized counter)
 */
export declare const countMessagesByRole: <TState extends AgentState>(state: TState, role: MCPMessage["role"]) => number;
/**
 * Check assistant response (fast boolean check)
 */
export declare const hasAssistantResponse: <TState extends AgentState>(state: TState) => boolean;
/**
 * Get conversation history (optimized string building)
 */
export declare const getConversationHistory: <TState extends AgentState>(state: TState, includeSystem?: boolean) => string;
/**
 * Create static agent node (pre-compiled message)
 */
export declare const createStaticAgentNode: <TState extends AgentState>(response: string) => NodeFunction<TState>;
/**
 * Create conditional agent node (optimized)
 */
export declare const createConditionalAgentNode: <TState extends AgentState>(responseFunction: (state: TState) => string | MCPAssistantMessage) => NodeFunction<TState>;
/**
 * Additional optimized helpers
 */
/**
 * Get message count (O(1))
 */
export declare const getMessageCount: <TState extends AgentState>(state: TState) => number;
/**
 * Check if state has messages (O(1))
 */
export declare const hasMessages: <TState extends AgentState>(state: TState) => boolean;
/**
 * Get first message (O(1))
 */
export declare const getFirstMessage: <TState extends AgentState>(state: TState) => MCPMessage | undefined;
/**
 * Clear messages (minimal allocation)
 */
export declare const clearMessages: <TState extends AgentState>() => Partial<TState>;
//# sourceMappingURL=agent-helpers.d.ts.map