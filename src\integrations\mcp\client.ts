// src/integrations/mcp/client.ts

import {
  MCPRequest,
  MCPTool,
  MCPToolResult,
  MCPResource,
  MCPResourceContents,
  MCPPrompt,
  MCPPromptResult
} from '../../core/types';

/**
 * Check if MCP SDK is available
 */
async function checkMCPSDK(): Promise<void> {
  try {
    await import('@modelcontextprotocol/sdk/client/index.js');
  } catch (error) {
    throw new Error(
      'MCP SDK not found. Install with: npm install @modelcontextprotocol/sdk\n' +
      'Or use AG3NTIC without MCP integration.'
    );
  }
}

/**
 * MCP Client Configuration
 */
export interface MCPClientConfig {
  name: string;
  version: string;
  serverUrl?: string;
  transport?: 'stdio' | 'http' | 'sse';
  timeout?: number;
  maxRetries?: number;
}

/**
 * MCP Client for connecting AG3NTIC agents to external MCP servers
 * 
 * This allows AG3NTIC agents to:
 * - Connect to any MCP-compliant server
 * - Access external tools, resources, and prompts
 * - Use standardized MCP protocol for communication
 */
export class MCPClient {
  private config: MCPClientConfig;
  private client: any; // Will be the actual MCP SDK client
  private connected: boolean = false;
  private requestId: number = 1;

  constructor(config: MCPClientConfig) {
    this.config = {
      timeout: 30000,
      maxRetries: 3,
      transport: 'http',
      ...config
    };
  }

  /**
   * Connect to an MCP server
   */
  async connect(): Promise<void> {
    try {
      // Check if MCP SDK is available
      await checkMCPSDK();

      // Dynamic import to avoid requiring MCP SDK if not used
      const { Client } = await import('@modelcontextprotocol/sdk/client/index.js');
      
      let transport;
      
      if (this.config.transport === 'stdio') {
        const { StdioClientTransport } = await import('@modelcontextprotocol/sdk/client/stdio.js');
        transport = new StdioClientTransport({
          command: this.config.serverUrl || 'node',
          args: []
        });
      } else if (this.config.transport === 'http') {
        const { StreamableHTTPClientTransport } = await import('@modelcontextprotocol/sdk/client/streamableHttp.js');
        transport = new StreamableHTTPClientTransport(
          new URL(this.config.serverUrl || 'http://localhost:3000/mcp')
        );
      } else if (this.config.transport === 'sse') {
        const { SSEClientTransport } = await import('@modelcontextprotocol/sdk/client/sse.js');
        transport = new SSEClientTransport(
          new URL(this.config.serverUrl || 'http://localhost:3000/sse')
        );
      } else {
        throw new Error(`Unsupported transport: ${this.config.transport}`);
      }

      this.client = new Client({
        name: this.config.name,
        version: this.config.version
      });

      await this.client.connect(transport);
      this.connected = true;
      
      console.log(`🔗 MCP Client connected to ${this.config.serverUrl} via ${this.config.transport}`);
    } catch (error) {
      console.error('❌ Failed to connect to MCP server:', error);
      throw error;
    }
  }

  /**
   * Disconnect from the MCP server
   */
  async disconnect(): Promise<void> {
    if (this.client && this.connected) {
      await this.client.close();
      this.connected = false;
      console.log('🔌 MCP Client disconnected');
    }
  }

  /**
   * Check if client is connected
   */
  isConnected(): boolean {
    return this.connected;
  }

  /**
   * List available tools from the MCP server
   */
  async listTools(): Promise<MCPTool[]> {
    this.ensureConnected();
    
    try {
      const response = await this.client.listTools();
      return response.tools.map((tool: any) => ({
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema
      }));
    } catch (error) {
      console.error('❌ Failed to list tools:', error);
      throw error;
    }
  }

  /**
   * Call a tool on the MCP server
   */
  async callTool(name: string, arguments_: Record<string, unknown>): Promise<MCPToolResult> {
    this.ensureConnected();
    
    try {
      const response = await this.client.callTool({
        name,
        arguments: arguments_
      });
      
      return {
        content: response.content || [],
        isError: response.isError || false
      };
    } catch (error) {
      console.error(`❌ Failed to call tool ${name}:`, error);
      throw error;
    }
  }

  /**
   * List available resources from the MCP server
   */
  async listResources(): Promise<MCPResource[]> {
    this.ensureConnected();
    
    try {
      const response = await this.client.listResources();
      return response.resources.map((resource: any) => ({
        uri: resource.uri,
        name: resource.name,
        description: resource.description,
        mimeType: resource.mimeType
      }));
    } catch (error) {
      console.error('❌ Failed to list resources:', error);
      throw error;
    }
  }

  /**
   * Read a resource from the MCP server
   */
  async readResource(uri: string): Promise<MCPResourceContents> {
    this.ensureConnected();
    
    try {
      const response = await this.client.readResource({ uri });
      return {
        contents: response.contents || []
      };
    } catch (error) {
      console.error(`❌ Failed to read resource ${uri}:`, error);
      throw error;
    }
  }

  /**
   * List available prompts from the MCP server
   */
  async listPrompts(): Promise<MCPPrompt[]> {
    this.ensureConnected();
    
    try {
      const response = await this.client.listPrompts();
      return response.prompts.map((prompt: any) => ({
        name: prompt.name,
        description: prompt.description,
        arguments: prompt.arguments
      }));
    } catch (error) {
      console.error('❌ Failed to list prompts:', error);
      throw error;
    }
  }

  /**
   * Get a prompt from the MCP server
   */
  async getPrompt(name: string, arguments_?: Record<string, unknown>): Promise<MCPPromptResult> {
    this.ensureConnected();
    
    try {
      const response = await this.client.getPrompt({
        name,
        arguments: arguments_ || {}
      });
      
      return {
        description: response.description,
        messages: response.messages || []
      };
    } catch (error) {
      console.error(`❌ Failed to get prompt ${name}:`, error);
      throw error;
    }
  }

  /**
   * Send a raw MCP request
   */
  async sendRequest(method: string, params?: Record<string, unknown>): Promise<any> {
    this.ensureConnected();
    
    try {
      const request: MCPRequest = {
        jsonrpc: "2.0",
        id: this.requestId++,
        method,
        params
      };
      
      // Use the underlying client's request method
      return await this.client.request(request);
    } catch (error) {
      console.error(`❌ Failed to send request ${method}:`, error);
      throw error;
    }
  }

  /**
   * Create an MCP-compatible tool function for use with AG3NTIC agents
   */
  createToolFunction(toolName: string) {
    return async (args: Record<string, unknown>) => {
      const result = await this.callTool(toolName, args);
      
      // Convert MCP result to simple string for AG3NTIC compatibility
      if (result.content && result.content.length > 0) {
        const textContent = result.content
          .filter(c => c.type === 'text')
          .map(c => (c as any).text)
          .join('\n');
        
        return textContent || JSON.stringify(result.content);
      }
      
      return JSON.stringify(result);
    };
  }

  /**
   * Get all tools as AG3NTIC-compatible tool functions
   */
  async getToolFunctions(): Promise<Record<string, (args: any) => Promise<string>>> {
    const tools = await this.listTools();
    const toolFunctions: Record<string, (args: any) => Promise<string>> = {};
    
    for (const tool of tools) {
      toolFunctions[tool.name] = this.createToolFunction(tool.name);
    }
    
    return toolFunctions;
  }

  /**
   * Ensure the client is connected
   */
  private ensureConnected(): void {
    if (!this.connected) {
      throw new Error('MCP Client is not connected. Call connect() first.');
    }
  }
}

/**
 * Create an MCP client with automatic connection
 */
export async function createMCPClient(config: MCPClientConfig): Promise<MCPClient> {
  const client = new MCPClient(config);
  await client.connect();
  return client;
}

/**
 * Create MCP client with backwards compatibility fallback
 */
export async function createMCPClientWithFallback(
  httpUrl: string, 
  sseUrl?: string,
  config?: Partial<MCPClientConfig>
): Promise<MCPClient> {
  const baseConfig = {
    name: 'ag3ntic-client',
    version: '1.0.0',
    ...config
  };

  // Try Streamable HTTP first
  try {
    const client = new MCPClient({
      ...baseConfig,
      serverUrl: httpUrl,
      transport: 'http'
    });
    await client.connect();
    console.log('✅ Connected using Streamable HTTP transport');
    return client;
  } catch (error) {
    console.log('⚠️  Streamable HTTP failed, trying SSE fallback...');
    
    // Fall back to SSE if provided
    if (sseUrl) {
      const client = new MCPClient({
        ...baseConfig,
        serverUrl: sseUrl,
        transport: 'sse'
      });
      await client.connect();
      console.log('✅ Connected using SSE transport');
      return client;
    }
    
    throw new Error('Failed to connect with both HTTP and SSE transports');
  }
}
