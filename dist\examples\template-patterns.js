#!/usr/bin/env -S npm run tsn -T
"use strict";
// src/examples/template-patterns.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.runTemplateDemo = main;
const core_1 = require("../core");
const lib_1 = require("../lib");
/**
 * Template Patterns Demo
 *
 * This example demonstrates the three core AG3NTIC templates:
 * 1. ExecutorAgent - Executes tasks using tools
 * 2. PlannerAgent - Breaks down complex goals into steps
 * 3. OrchestratorAgent - Coordinates multiple workers
 */
// --- Mock Tools for Executor Agent ---
const mockTools = {
    getCurrentWeather: async (args) => {
        console.log(`🌤️  Getting weather for ${args.location}`);
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
        return {
            location: args.location,
            temperature: args.location.toLowerCase().includes('tokyo') ? '22°C' : '18°C',
            condition: 'Partly cloudy',
            humidity: '65%',
            unit: args.unit || 'celsius'
        };
    },
    sendEmail: async (args) => {
        console.log(`📧 Sending email to ${args.to}`);
        await new Promise(resolve => setTimeout(resolve, 300)); // Simulate sending
        return {
            messageId: 'msg_' + Math.random().toString(36).substr(2, 9),
            status: 'sent',
            timestamp: new Date().toISOString()
        };
    },
    calculateTip: async (args) => {
        console.log(`💰 Calculating ${args.percentage}% tip on $${args.amount}`);
        const tip = (args.amount * args.percentage) / 100;
        return {
            originalAmount: args.amount,
            tipPercentage: args.percentage,
            tipAmount: tip,
            totalAmount: args.amount + tip
        };
    },
    searchDatabase: async (args) => {
        console.log(`🔍 Searching database for: ${args.query}`);
        await new Promise(resolve => setTimeout(resolve, 400)); // Simulate DB query
        return {
            query: args.query,
            results: [
                { id: 1, title: `Result 1 for ${args.query}`, relevance: 0.95 },
                { id: 2, title: `Result 2 for ${args.query}`, relevance: 0.87 },
                { id: 3, title: `Result 3 for ${args.query}`, relevance: 0.73 }
            ],
            totalCount: 3,
            executionTime: '0.4s'
        };
    }
};
// --- Mock LLM Node Functions ---
const createMockLLMNode = (agentType) => {
    return async (state) => {
        const lastMessage = (0, lib_1.getLastMessage)(state);
        let response;
        let toolCalls;
        if (agentType === 'executor') {
            // Check if the last message is a tool result
            if (lastMessage?.role === 'tool') {
                // Respond to tool result without calling more tools
                const toolResult = JSON.parse(lastMessage.content);
                response = `Great! I've completed the task. Here are the results: ${JSON.stringify(toolResult, null, 2)}`;
            }
            else {
                // Executor decides which tools to use based on user input
                const content = lastMessage?.content || '';
                if (content.toLowerCase().includes('weather')) {
                    response = "I'll check the weather for you.";
                    toolCalls = [{
                            id: 'call_' + Math.random().toString(36).substr(2, 9),
                            type: 'function',
                            function: {
                                name: 'getCurrentWeather',
                                arguments: JSON.stringify({
                                    location: content.includes('Tokyo') ? 'Tokyo' : 'San Francisco',
                                    unit: 'celsius'
                                })
                            }
                        }];
                }
                else if (content.toLowerCase().includes('email')) {
                    response = "I'll send that email for you.";
                    toolCalls = [{
                            id: 'call_' + Math.random().toString(36).substr(2, 9),
                            type: 'function',
                            function: {
                                name: 'sendEmail',
                                arguments: JSON.stringify({
                                    to: '<EMAIL>',
                                    subject: 'Test Email',
                                    body: 'This is a test email sent by the AG3NTIC executor agent.'
                                })
                            }
                        }];
                }
                else if (content.toLowerCase().includes('tip')) {
                    response = "I'll calculate the tip for you.";
                    toolCalls = [{
                            id: 'call_' + Math.random().toString(36).substr(2, 9),
                            type: 'function',
                            function: {
                                name: 'calculateTip',
                                arguments: JSON.stringify({ amount: 50, percentage: 20 })
                            }
                        }];
                }
                else {
                    response = "I understand your request. Let me help you with that.";
                }
            }
        }
        else if (agentType === 'planner') {
            response = `I'll create a detailed plan for: "${lastMessage?.content}"\n\nHere's my strategic breakdown:\n1. Research and analysis phase\n2. Design and planning phase\n3. Implementation phase\n4. Testing and validation phase\n5. Deployment and monitoring phase`;
        }
        else {
            response = `As an ${agentType} agent, I'm processing: "${lastMessage?.content}"`;
        }
        const assistantMessage = {
            role: 'assistant',
            content: response,
            ...(toolCalls && { tool_calls: toolCalls })
        };
        return (0, lib_1.addMessage)(state, assistantMessage);
    };
};
// --- Demo Functions ---
async function demoExecutorAgent() {
    console.log("🔥 Demo 1: Executor Agent Pattern");
    console.log("=".repeat(50));
    const llmNode = createMockLLMNode('executor');
    const executorGraph = (0, lib_1.createExecutorAgent)(llmNode, mockTools);
    const executor = new core_1.Executor(executorGraph);
    const testCases = [
        "What's the weather like in Tokyo?",
        "Send an email to the team about the meeting",
        "Calculate a 20% tip on a $50 bill"
    ];
    for (const testCase of testCases) {
        console.log(`\n📝 User: ${testCase}`);
        const result = await executor.execute({
            messages: [{ role: 'user', content: testCase }]
        });
        const finalMessage = (0, lib_1.getLastMessage)(result);
        console.log(`🤖 Assistant: ${finalMessage?.content}`);
    }
    console.log("\n✅ Executor Agent Demo Complete!\n");
}
async function demoPlannerAgent() {
    console.log("🔥 Demo 2: Planner Agent Pattern");
    console.log("=".repeat(50));
    const llmNode = createMockLLMNode('planner');
    const plannerGraph = (0, lib_1.createPlannerAgent)(llmNode, {
        maxSteps: 8,
        strategy: 'sequential',
        includeTimeEstimates: true
    });
    const executor = new core_1.Executor(plannerGraph);
    const goals = [
        "Launch a new mobile app for food delivery",
        "Organize a company retreat for 100 employees",
        "Implement a new customer support system"
    ];
    for (const goal of goals) {
        console.log(`\n📝 Goal: ${goal}`);
        const result = await executor.execute({
            messages: [{ role: 'user', content: goal }]
        });
        console.log(`📋 Generated Plan:`);
        if (result.plan) {
            result.plan.forEach((step, index) => {
                const timeStr = step.estimatedTime ? ` (${step.estimatedTime}min)` : '';
                const depStr = step.dependencies ? ` [depends on: ${step.dependencies.join(', ')}]` : '';
                console.log(`   ${index + 1}. ${step.description}${timeStr}${depStr}`);
            });
        }
        console.log(`🎯 Strategy: ${result.planningStrategy}`);
        console.log(`📊 Total Steps: ${result.plan?.length || 0}`);
    }
    console.log("\n✅ Planner Agent Demo Complete!\n");
}
async function demoOrchestratorAgent() {
    console.log("🔥 Demo 3: Orchestrator Agent Pattern");
    console.log("=".repeat(50));
    // Create mock worker agents
    const workers = [
        {
            id: 'researcher',
            name: 'Research Specialist',
            description: 'Conducts research and gathers information',
            capabilities: ['research', 'analysis', 'data_gathering'],
            specializations: ['market_research', 'competitive_analysis'],
            graph: (0, lib_1.createExecutorAgent)(createMockLLMNode('researcher'), { searchDatabase: mockTools.searchDatabase })
        },
        {
            id: 'communicator',
            name: 'Communication Specialist',
            description: 'Handles all communication tasks',
            capabilities: ['communication', 'email', 'messaging'],
            specializations: ['email_marketing', 'customer_support'],
            graph: (0, lib_1.createExecutorAgent)(createMockLLMNode('communicator'), { sendEmail: mockTools.sendEmail })
        },
        {
            id: 'analyst',
            name: 'Data Analyst',
            description: 'Performs calculations and data analysis',
            capabilities: ['calculation', 'analysis', 'reporting'],
            specializations: ['financial_analysis', 'statistical_analysis'],
            graph: (0, lib_1.createExecutorAgent)(createMockLLMNode('analyst'), { calculateTip: mockTools.calculateTip })
        }
    ];
    const llmNode = createMockLLMNode('orchestrator');
    const orchestratorGraph = (0, lib_1.createOrchestratorAgent)(llmNode, workers, {
        maxConcurrentTasks: 2,
        retryFailedTasks: true,
        timeoutPerTask: 10000
    });
    // First, we need a plan to orchestrate
    const plannerGraph = (0, lib_1.createPlannerAgent)(createMockLLMNode('planner'), {
        maxSteps: 5,
        strategy: 'sequential'
    });
    console.log("📋 Step 1: Creating execution plan...");
    const planResult = await new core_1.Executor(plannerGraph).execute({
        messages: [{ role: 'user', content: 'Research market trends and send summary report to stakeholders' }]
    });
    console.log("🎭 Step 2: Orchestrating execution...");
    const orchestratorExecutor = new core_1.Executor(orchestratorGraph);
    if (!planResult.plan) {
        console.error("❌ No plan generated, cannot proceed with orchestration");
        return;
    }
    let stepCount = 0;
    const result = await orchestratorExecutor.executeWithCallback({
        messages: [{ role: 'user', content: 'Execute the research and communication plan' }],
        plan: planResult.plan
    }, (state) => {
        stepCount++;
        console.log(`   Step ${stepCount}: Processing...`);
        if (state.stepExecutions) {
            const completed = state.stepExecutions.filter(exec => exec.status === 'completed').length;
            const total = state.stepExecutions.length;
            console.log(`   Progress: ${completed}/${total} steps completed`);
        }
    });
    console.log("\n📊 Orchestration Results:");
    if (result.stepExecutions) {
        result.stepExecutions.forEach((execution, index) => {
            const status = execution.status === 'completed' ? '✅' :
                execution.status === 'failed' ? '❌' : '⏳';
            console.log(`   ${status} Step ${index + 1}: ${execution.stepId} (${execution.status})`);
            if (execution.assignedWorker) {
                const worker = workers.find(w => w.id === execution.assignedWorker);
                console.log(`      Assigned to: ${worker?.name || execution.assignedWorker}`);
            }
        });
    }
    console.log(`🎯 Final Status: ${result.orchestrationStatus}`);
    console.log("\n✅ Orchestrator Agent Demo Complete!\n");
}
// --- Main Demo Function ---
async function main() {
    console.log("🚀 AG3NTIC Template Patterns Demo");
    console.log("=".repeat(60));
    console.log("Demonstrating the three core agent templates:\n");
    try {
        await demoExecutorAgent();
        await demoPlannerAgent();
        await demoOrchestratorAgent();
        console.log("🎉 All Template Patterns Demonstrated Successfully!");
        console.log("\n💡 Key Takeaways:");
        console.log("   🔧 ExecutorAgent: Perfect for tool-based task execution");
        console.log("   📋 PlannerAgent: Ideal for breaking down complex goals");
        console.log("   🎭 OrchestratorAgent: Essential for coordinating multiple workers");
        console.log("\n🏗️  These templates can be combined to create sophisticated multi-agent systems!");
    }
    catch (error) {
        console.error("❌ Demo failed:", error);
    }
}
// Run the demo
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=template-patterns.js.map