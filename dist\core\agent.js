"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Agent = void 0;
exports.run = run;
const zod_1 = require("zod");
const memory_js_1 = require("./memory.js");
/**
 * Ultra-high-performance Agent implementation
 *
 * Features:
 * - ReAct (Thought > Action > Observation) loop
 * - Agent handoffs and orchestration
 * - Self-reflection and critique
 * - Role-based specialization
 * - Memory integration
 * - Performance optimizations
 */
class Agent {
    constructor(config) {
        this.config = {
            role: 'executor',
            tools: [],
            handoffs: [],
            memory: new memory_js_1.Memory(),
            modelSettings: {},
            maxIterations: 10,
            enableSelfReflection: false,
            ...config,
        };
        // Pre-compile tool map for O(1) lookup
        this.toolMap = new Map();
        this.config.tools.forEach(tool => {
            this.toolMap.set(tool.name, tool);
        });
        // Pre-compile handoff map
        this.handoffMap = new Map();
        this.config.handoffs.forEach(agent => {
            this.handoffMap.set(agent.name, agent);
        });
    }
    /**
     * Create agent with type safety for handoffs
     */
    static create(config) {
        return new Agent(config);
    }
    /**
     * Convert agent to tool for use by other agents
     */
    asTool() {
        return {
            name: `agent_${this.config.name.toLowerCase().replace(/\s+/g, '_')}`,
            description: `Delegate to ${this.config.name}: ${this.config.instructions}`,
            parameters: zod_1.z.object({
                input: zod_1.z.string().describe('Input to send to the agent'),
            }),
            execute: async ({ input }) => {
                const result = await this.run(input);
                return result.finalOutput;
            },
        };
    }
    /**
     * Execute ReAct loop with performance optimizations
     */
    async run(input, context) {
        const startTime = Date.now();
        const steps = [];
        let currentState;
        let iteration = 0;
        let toolCalls = 0;
        // Initialize state
        if (typeof input === 'string') {
            currentState = {
                messages: [{ role: 'user', content: input }],
                metadata: { agentName: this.config.name, role: this.config.role },
                ...context,
            };
        }
        else {
            currentState = { ...input };
        }
        // Load memory if available
        if (this.config.memory) {
            currentState = await this.config.memory.load(currentState);
        }
        // ReAct loop with optimized execution
        while (iteration < this.config.maxIterations) {
            iteration++;
            try {
                // THOUGHT: Generate reasoning
                const thoughtStep = await this.generateThought(currentState);
                steps.push({
                    step: 'thought',
                    content: thoughtStep.content,
                    timestamp: Date.now(),
                });
                // Check for handoff decision
                const handoffAgent = this.checkForHandoff(thoughtStep.content);
                if (handoffAgent) {
                    const handoffResult = await handoffAgent.run(currentState, context);
                    return {
                        ...handoffResult,
                        finalAgent: handoffAgent,
                        steps: [...steps, ...handoffResult.steps],
                    };
                }
                // ACTION: Execute tool if needed
                const actionStep = await this.executeAction(currentState, thoughtStep);
                if (actionStep) {
                    steps.push({
                        step: 'action',
                        content: actionStep.content,
                        timestamp: Date.now(),
                    });
                    toolCalls++;
                    // OBSERVATION: Process tool result
                    const observationStep = await this.processObservation(currentState, actionStep);
                    steps.push({
                        step: 'observation',
                        content: observationStep.content,
                        timestamp: Date.now(),
                    });
                    currentState = observationStep.state;
                }
                // Check for completion
                if (this.isComplete(currentState)) {
                    break;
                }
                // REFLECTION: Self-critique if enabled
                if (this.config.enableSelfReflection && iteration % 3 === 0) {
                    const reflectionStep = await this.generateReflection(currentState);
                    steps.push({
                        step: 'reflection',
                        content: reflectionStep.content,
                        timestamp: Date.now(),
                    });
                }
            }
            catch (error) {
                // Handle errors gracefully
                steps.push({
                    step: 'observation',
                    content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    timestamp: Date.now(),
                });
                break;
            }
        }
        // Save memory if available
        if (this.config.memory) {
            await this.config.memory.save(currentState);
        }
        const executionTime = Date.now() - startTime;
        const finalOutput = this.extractFinalOutput(currentState);
        return {
            finalOutput,
            finalState: currentState,
            finalAgent: this,
            steps,
            metrics: {
                totalSteps: steps.length,
                toolCalls,
                executionTime,
            },
        };
    }
    /**
     * Generate thought step with role-specific reasoning
     */
    async generateThought(state) {
        // Implementation would use LLM to generate reasoning
        // This is a simplified version
        return {
            content: `Analyzing the current situation and determining next steps...`,
            shouldUseTools: false,
        };
    }
    /**
     * Check if thought indicates handoff to another agent
     */
    checkForHandoff(thought) {
        // Simple keyword-based handoff detection
        // In production, this would use more sophisticated NLP
        for (const [name, agent] of this.handoffMap) {
            if (thought.toLowerCase().includes(name.toLowerCase())) {
                return agent;
            }
        }
        return null;
    }
    /**
     * Execute action step (tool usage)
     */
    async executeAction(state, thought) {
        if (!thought.shouldUseTools)
            return null;
        // Tool selection and execution logic
        // This would be implemented with LLM tool calling
        return null;
    }
    /**
     * Process observation from tool execution
     */
    async processObservation(state, action) {
        // Process tool result and update state
        return {
            content: `Tool ${action.toolName} executed successfully`,
            state,
        };
    }
    /**
     * Generate reflection step for self-critique
     */
    async generateReflection(state) {
        return {
            content: 'Reflecting on progress and adjusting strategy...',
        };
    }
    /**
     * Check if agent has completed its task
     */
    isComplete(state) {
        // Implementation would check for completion criteria
        return false;
    }
    /**
     * Extract final output from state
     */
    extractFinalOutput(state) {
        const lastMessage = state.messages[state.messages.length - 1];
        return lastMessage?.content || 'Task completed';
    }
    // Getters
    get name() {
        return this.config.name;
    }
    get role() {
        return this.config.role;
    }
    get tools() {
        return this.config.tools;
    }
    get handoffs() {
        return this.config.handoffs;
    }
}
exports.Agent = Agent;
/**
 * Utility function to run agent with simplified interface
 */
async function run(agent, input, context) {
    return agent.run(input, context);
}
//# sourceMappingURL=agent.js.map