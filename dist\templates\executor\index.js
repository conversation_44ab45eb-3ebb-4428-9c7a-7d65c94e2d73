"use strict";
// src/templates/executor/index.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.createExecutorAgent = createExecutorAgent;
exports.createSimpleExecutor = createSimpleExecutor;
exports.createEnhancedExecutor = createEnhancedExecutor;
const core_1 = require("../../core");
const lib_1 = require("../../lib");
/**
 * Create an Executor Agent - the fundamental "doer" agent
 *
 * This is the most basic but essential template. It receives a task,
 * uses available tools to complete it, and returns the result.
 *
 * @param options Configuration options
 * @returns Configured Graph ready for execution
 */
function createExecutorAgent(options) {
    const { tools, agentNode, enhancedTools = false, toolOptions = {} } = options;
    const systemMessage = options.systemMessage || "You are a helpful assistant that can use tools to complete tasks. When given a task, analyze what needs to be done and use the appropriate tools to accomplish it.";
    // Create the tool execution node
    const toolNode = enhancedTools
        ? require('../../lib').createEnhancedToolNode(tools, toolOptions)
        : (0, lib_1.createToolNode)(tools);
    // Create default agent node if none provided
    const defaultAgentNode = async (state) => {
        const { addMessage } = require('../../lib');
        // Use systemMessage for context (though in this mock implementation, it's mainly for reference)
        console.log(`Agent operating with system message: ${systemMessage.substring(0, 50)}...`);
        // Simple mock implementation - in real usage, this would be replaced
        // with an actual LLM integration from /integrations
        const lastMessage = state.messages[state.messages.length - 1];
        if (lastMessage?.role === 'user') {
            // Determine if we need to use tools based on the task
            const task = lastMessage.content || state.task || '';
            const toolNames = Object.keys(tools);
            // Simple heuristic to decide if we need tools
            const needsTools = toolNames.some(toolName => task.toLowerCase().includes(toolName.toLowerCase()) ||
                task.toLowerCase().includes('search') ||
                task.toLowerCase().includes('calculate') ||
                task.toLowerCase().includes('weather'));
            if (needsTools && toolNames.length > 0) {
                // Mock tool call - in real usage, LLM would decide this
                const toolToUse = toolNames[0]; // Simple selection
                const assistantMessage = {
                    role: 'assistant',
                    content: null,
                    tool_calls: [{
                            id: `call_${Date.now()}`,
                            type: 'function',
                            function: {
                                name: toolToUse,
                                arguments: JSON.stringify({}) // Mock arguments
                            }
                        }]
                };
                return addMessage(state, assistantMessage);
            }
            else {
                // Direct response without tools
                const assistantMessage = {
                    role: 'assistant',
                    content: `I understand you want me to: ${task}. I'll help you with that.`
                };
                return addMessage(state, assistantMessage);
            }
        }
        // Handle tool responses
        if (lastMessage?.role === 'tool') {
            const assistantMessage = {
                role: 'assistant',
                content: `Task completed. Result: ${lastMessage.content}`
            };
            return {
                ...addMessage(state, assistantMessage),
                status: 'completed',
                result: lastMessage.content
            };
        }
        return {};
    };
    // Use provided agent node or default
    const finalAgentNode = agentNode || defaultAgentNode;
    // Build and return the graph
    return new core_1.Graph()
        .addNode('agent', finalAgentNode)
        .addNode('tools', toolNode)
        .setEntryPoint('agent')
        .addConditionalEdge('agent', lib_1.shouldCallTools, {
        'tools': 'tools',
        '__end__': '__END__'
    })
        .addEdge('tools', 'agent');
}
/**
 * Create a simple executor agent with minimal configuration
 *
 * @param tools Map of available tools
 * @param systemMessage Optional system message
 * @returns Configured Graph
 */
function createSimpleExecutor(tools, systemMessage) {
    const options = { tools };
    if (systemMessage) {
        options.systemMessage = systemMessage;
    }
    return createExecutorAgent(options);
}
/**
 * Create an enhanced executor agent with retries and logging
 *
 * @param tools Map of available tools
 * @param options Enhanced execution options
 * @returns Configured Graph
 */
function createEnhancedExecutor(tools, options = {}) {
    const executorOptions = {
        tools,
        enhancedTools: true
    };
    if (options.systemMessage) {
        executorOptions.systemMessage = options.systemMessage;
    }
    const toolOptions = {};
    if (options.logExecution !== undefined)
        toolOptions.logExecution = options.logExecution;
    if (options.timeout !== undefined)
        toolOptions.timeout = options.timeout;
    if (options.retries !== undefined)
        toolOptions.retries = options.retries;
    if (Object.keys(toolOptions).length > 0) {
        executorOptions.toolOptions = toolOptions;
    }
    return createExecutorAgent(executorOptions);
}
//# sourceMappingURL=index.js.map