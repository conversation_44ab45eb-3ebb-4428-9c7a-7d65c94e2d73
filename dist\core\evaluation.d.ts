import { Agent } from './agent.js';
/**
 * Evaluation metrics
 */
export interface EvaluationMetrics {
    accuracy?: number;
    relevance?: number;
    completeness?: number;
    coherence?: number;
    factuality?: number;
    safety?: number;
    efficiency?: number;
    custom?: Record<string, number>;
}
/**
 * Evaluation result
 */
export interface EvaluationResult {
    score: number;
    metrics: EvaluationMetrics;
    feedback: string;
    suggestions?: string[];
    confidence: number;
    timestamp: number;
}
/**
 * Human feedback interface
 */
export interface HumanFeedback {
    approved: boolean;
    score?: number;
    comments?: string;
    corrections?: string;
    timestamp: number;
    userId?: string;
}
/**
 * Evaluation configuration
 */
export interface EvaluationConfig {
    enableSelfEvaluation: boolean;
    enablePeerReview: boolean;
    enableHumanReview: boolean;
    evaluationAgent?: Agent;
    humanReviewThreshold?: number;
    metrics: (keyof EvaluationMetrics)[];
    customEvaluators?: Map<string, EvaluatorFunction>;
}
/**
 * Evaluator function type
 */
export type EvaluatorFunction = (input: string, output: string, context?: Record<string, any>) => Promise<EvaluationResult> | EvaluationResult;
/**
 * Scorecard for tracking performance
 */
export interface Scorecard {
    agentName: string;
    totalEvaluations: number;
    averageScore: number;
    metrics: {
        [K in keyof EvaluationMetrics]: {
            average: number;
            count: number;
            trend: 'improving' | 'declining' | 'stable';
        };
    };
    recentEvaluations: EvaluationResult[];
    humanFeedback: HumanFeedback[];
    lastUpdated: number;
}
/**
 * Ultra-high-performance Evaluation system
 *
 * Features:
 * - Self-evaluation capabilities
 * - Peer review between agents
 * - Human-in-the-loop workflows
 * - Performance scorecards
 * - Custom evaluation metrics
 * - Trend analysis and improvement tracking
 */
export declare class EvaluationSystem {
    private readonly config;
    private readonly scorecards;
    private readonly pendingReviews;
    constructor(config: EvaluationConfig);
    /**
     * Evaluate agent output with multiple evaluation methods
     */
    evaluate(agentName: string, input: string, output: string, context?: Record<string, any>): Promise<EvaluationResult>;
    /**
     * Perform self-evaluation using the agent's own reasoning
     */
    private performSelfEvaluation;
    /**
     * Perform peer review using another agent
     */
    private performPeerReview;
    /**
     * Request human review for low-scoring outputs
     */
    private requestHumanReview;
    /**
     * Submit human feedback
     */
    submitHumanFeedback(reviewId: string, feedback: HumanFeedback): Promise<void>;
    /**
     * Combine multiple evaluation results
     */
    private combineEvaluations;
    /**
     * Update agent scorecard with new evaluation
     */
    private updateScorecard;
    /**
     * Get scorecard for an agent
     */
    getScorecard(agentName: string): Scorecard | undefined;
    /**
     * Get all scorecards
     */
    getAllScorecards(): Scorecard[];
    /**
     * Get pending human reviews
     */
    getPendingReviews(): Array<{
        id: string;
        input: string;
        output: string;
        context?: Record<string, any>;
        timestamp: number;
    }>;
}
/**
 * Create evaluation system with configuration
 */
export declare function createEvaluationSystem(config: EvaluationConfig): EvaluationSystem;
/**
 * Global evaluation system instance
 */
export declare const globalEvaluationSystem: EvaluationSystem;
//# sourceMappingURL=evaluation.d.ts.map