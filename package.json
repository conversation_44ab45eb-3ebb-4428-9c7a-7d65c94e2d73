{"name": "ag3ntic", "version": "1.0.0", "description": "A lightweight, TypeScript-native framework for building agentic workflows with radical simplicity", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "example:weather": "ts-node src/examples/weather-agent.ts", "example:advanced": "ts-node src/examples/advanced-agent.ts", "example:openrouter": "ts-node src/examples/openrouter-weather-agent.ts", "example:multi-agent": "ts-node src/examples/multi-agent-system.ts", "example:enhanced-llm": "ts-node src/examples/enhanced-llm-integrations.ts", "example:templates": "ts-node src/examples/template-patterns.ts", "example:real-llm": "ts-node src/examples/real-llm-templates.ts", "example:mcp": "ts-node src/examples/mcp-integration.ts", "test": "jest", "test:real": "node tests/run-real-tests.js", "test:demo": "node tests/run-real-tests.js --demo", "test:openrouter": "jest tests/openrouter-demo.test.ts --verbose", "test:agents": "jest tests/agents/ --verbose", "test:multi-agent": "jest tests/multi-agent/ --verbose", "test:integration": "jest tests/integration/ --verbose", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["ai", "agents", "llm", "typescript", "framework", "agentic", "workflow", "graph", "state-machine"], "author": "AG3NTIC Team", "license": "MIT", "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "prettier": "^3.0.0", "ts-jest": "^29.0.0", "ts-node": "^10.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"openai": "^4.0.0", "anthropic": "^0.30.0", "@modelcontextprotocol/sdk": "^1.0.0"}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/ag3ntic/ag3ntic.git"}, "bugs": {"url": "https://github.com/ag3ntic/ag3ntic/issues"}, "homepage": "https://github.com/ag3ntic/ag3ntic#readme"}