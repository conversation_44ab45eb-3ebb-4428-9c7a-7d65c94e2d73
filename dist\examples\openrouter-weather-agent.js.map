{"version": 3, "file": "openrouter-weather-agent.js", "sourceRoot": "", "sources": ["../../src/examples/openrouter-weather-agent.ts"], "names": [], "mappings": ";AAAA,2CAA2C;;;AAE3C,kCAAsD;AACtD,gCAAyE;AACzE,2DAA8E;AAO9E,0BAA0B;AAC1B;;;;GAIG;AACH,MAAM,iBAAiB,GAAG,KAAK,EAAE,IAA2D,EAAmB,EAAE;IAC/G,MAAM,EAAE,QAAQ,EAAE,IAAI,GAAG,YAAY,EAAE,GAAG,IAAI,CAAC;IAE/C,0BAA0B;IAC1B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAEvD,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;QACrD,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,QAAQ,EAAE,mBAAmB;YAC7B,WAAW,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;YACjD,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;IACL,CAAC;IAED,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,QAAQ,EAAE,cAAc;YACxB,WAAW,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;YACjD,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;IACL,CAAC;IAED,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC9C,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,QAAQ,EAAE,YAAY;YACtB,WAAW,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;YACjD,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;IACL,CAAC;IAED,OAAO,IAAI,CAAC,SAAS,CAAC;QACpB,QAAQ;QACR,WAAW,EAAE,SAAS;QACtB,SAAS,EAAE,SAAS;QACpB,KAAK,EAAE,wCAAwC;KAChD,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,oDAAoD;AACpD,MAAM,YAAY,GAAqB;IACrC;QACE,IAAI,EAAE,UAAU;QAChB,QAAQ,EAAE;YACR,IAAI,EAAE,mBAAmB;YACzB,WAAW,EAAE,4DAA4D;YACzE,UAAU,EAAE;gBACV,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,QAAQ,EAAE;wBACR,IAAI,EAAE,QAAQ;wBACd,WAAW,EAAE,yEAAyE;qBACvF;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;wBAC/B,WAAW,EAAE,6BAA6B;qBAC3C;iBACF;gBACD,QAAQ,EAAE,CAAC,UAAU,CAAC;aACvB;SACF;KACF;CACF,CAAC;AAEF,6BAA6B;AAC7B,MAAM,KAAK,GAAG;IACZ,iBAAiB;CAClB,CAAC;AAEF,yDAAyD;AACzD,MAAM,iBAAiB,GAAG,CAAC,gBAAkC,EAAuB,EAAE;IACpF,qCAAqC;IACrC,MAAM,SAAS,GAAG,gBAAgB,CAAC,eAAe,CAAe;QAC/D,KAAK,EAAE,6BAA6B;QACpC,WAAW,EAAE,GAAG;QAChB,aAAa,EAAE;;;;iDAI8B;QAC7C,KAAK,EAAE,YAAY;KACpB,CAAC,CAAC;IAEH,6BAA6B;IAC7B,MAAM,QAAQ,GAAG,IAAA,oBAAc,EAAe,KAAK,CAAC,CAAC;IAErD,OAAO,IAAI,YAAK,EAAgB;SAC7B,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC;SAC3B,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;SAC1B,aAAa,CAAC,OAAO,CAAC;SACtB,kBAAkB,CAAC,OAAO,EAAE,qBAAe,EAAE;QAC5C,OAAO,EAAE,OAAO;QAChB,SAAS,EAAE,SAAS;KACrB,CAAC;SACD,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC/B,CAAC,CAAC;AAwEiC,8CAAiB;AAtEpD,2BAA2B;AAC3B,MAAM,wBAAwB,GAAG,KAAK,IAAI,EAAE;IAC1C,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEvD,+BAA+B;IAC/B,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;IAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,CAAC,KAAK,CAAC,sDAAsD,CAAC,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QACpE,OAAO;IACT,CAAC;IAED,2BAA2B;IAC3B,MAAM,gBAAgB,GAAG,IAAI,6BAAgB,CAAC;QAC5C,MAAM;QACN,YAAY,EAAE,6BAA6B;KAC5C,CAAC,CAAC;IAEH,kBAAkB;IAClB,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACnD,MAAM,WAAW,GAAG,MAAM,gBAAgB,CAAC,cAAc,EAAE,CAAC;IAC5D,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QACvD,OAAO;IACT,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAE/C,cAAc;IACd,MAAM,KAAK,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;IAClD,MAAM,QAAQ,GAAG,IAAI,eAAQ,CAAC,KAAK,CAAC,CAAC;IAErC,MAAM,OAAO,GAAG;QACd,2CAA2C;QAC3C,oCAAoC;QACpC,oCAAoC;KACrC,CAAC;IAEF,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,OAAO,CAAC,CAAC;QAE3C,MAAM,YAAY,GAAiB;YACjC,KAAK,EAAE,SAAS;YAChB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;SAC7C,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,mBAAmB,CACnD,YAAY,EACZ,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;gBACd,MAAM,OAAO,GAAG,IAAA,oBAAc,EAAC,KAAK,CAAC,CAAC;gBACtC,IAAI,OAAO,EAAE,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;oBACxD,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,gCAAgC,CAAC,CAAC;gBAC5D,CAAC;qBAAM,IAAI,OAAO,EAAE,IAAI,KAAK,MAAM,EAAE,CAAC;oBACpC,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,qBAAqB,CAAC,CAAC;gBACjD,CAAC;qBAAM,IAAI,OAAO,EAAE,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;oBAC5D,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,2BAA2B,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC,CACF,CAAC;YAEF,MAAM,WAAW,GAAG,IAAA,oBAAc,EAAC,UAAU,CAAC,EAAE,OAAO,CAAC;YACxD,OAAO,CAAC,GAAG,CAAC,qBAAqB,WAAW,IAAI,CAAC,CAAC;QAEpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,YAAY,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAGO,4DAAwB;AAEjC,wCAAwC;AACxC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,wBAAwB,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAClD,CAAC"}