#!/usr/bin/env -S npm run tsn -T

// src/examples/mcp-integration.ts

import { Executor } from '../core';
import { 
  createExecutorAgent,
  createMCPExecutorAgent,
  createAutoMCPAgent,
  getLastMessage,
  addMessage,
  Tool
} from '../lib';
// MCP imports with graceful fallback
let MCPServer: any, createMCPClient: any;
let getMCPIntegrationStatus: any, createMCPCompatibleTool: any;

// Initialize MCP modules
async function initializeMCP() {
  // Provide fallback implementations first
  getMCPIntegrationStatus = async () => ({ available: false, version: null, features: {} });
  createMCPCompatibleTool = (name: string, desc: string, schema: any, handler: any) => ({
    toolFunction: handler,
    mcpDefinition: { name, description: desc, inputSchema: schema },
    mcpHandler: handler
  });

  // Try to load MCP module
  try {
    const mcpModule = await import('../integrations/mcp');
    MCPServer = mcpModule.MCPServer;
    createMCPClient = mcpModule.createMCPClient;
    getMCPIntegrationStatus = mcpModule.getMCPIntegrationStatus;
    createMCPCompatibleTool = mcpModule.createMCPCompatibleTool;

    // Create MCP compatible tools after initialization
    mcpCompatibleTools = {
      weatherTool: createMCPCompatibleTool(
        'getWeather',
        'Get current weather for a location',
        {
          type: 'object',
          properties: {
            location: { type: 'string', description: 'City name' },
            unit: { type: 'string', enum: ['celsius', 'fahrenheit'], default: 'celsius' }
          },
          required: ['location']
        },
        async (args: { location: string; unit?: string }) => {
          // Simulate weather API call
          return {
            location: args.location,
            temperature: args.location.toLowerCase().includes('tokyo') ? '22°C' : '18°C',
            condition: 'Partly cloudy',
            humidity: '65%',
            unit: args.unit || 'celsius'
          };
        }
      ),

      databaseTool: createMCPCompatibleTool(
        'queryDatabase',
        'Query a database for information',
        {
          type: 'object',
          properties: {
            query: { type: 'string', description: 'SQL-like query' },
            table: { type: 'string', description: 'Table name' }
          },
          required: ['query']
        },
        async (args: { query: string; table?: string }) => {
          // Simulate database query
          return {
            query: args.query,
            table: args.table || 'default',
            results: [
              { id: 1, name: 'Sample Record 1', value: 100 },
              { id: 2, name: 'Sample Record 2', value: 200 }
            ],
            count: 2
          };
        }
      )
    };
  } catch (error) {
    console.log('⚠️  MCP integration not available. Install with: npm install @modelcontextprotocol/sdk');

    // Create fallback tools
    mcpCompatibleTools = {
      weatherTool: {
        toolFunction: async (args: { location: string; unit?: string }) => {
          return {
            location: args.location,
            temperature: '22°C',
            condition: 'Partly cloudy',
            humidity: '65%',
            unit: args.unit || 'celsius'
          };
        }
      },
      databaseTool: {
        toolFunction: async (args: { query: string; table?: string }) => {
          return {
            query: args.query,
            table: args.table || 'default',
            results: [{ id: 1, name: 'Sample Record', value: 100 }],
            count: 1
          };
        }
      }
    };
  }
}

/**
 * Model Context Protocol (MCP) Integration Demo
 * 
 * This example demonstrates how AG3NTIC integrates with the Model Context Protocol:
 * 
 * 1. **MCP Client**: AG3NTIC agents connecting to external MCP servers
 * 2. **MCP Server**: AG3NTIC exposing its capabilities as an MCP server
 * 3. **Hybrid Agents**: Using both local tools and remote MCP tools
 * 4. **Real-world Integration**: Practical examples of MCP in action
 * 
 * Prerequisites:
 * - npm install @modelcontextprotocol/sdk
 * - MCP server running (we'll create one in this demo)
 */

// --- Sample Tools for MCP Demo ---

const localTools: Record<string, Tool> = {
  calculateSum: async (args: { numbers: number[] }) => {
    const sum = args.numbers.reduce((a, b) => a + b, 0);
    return { sum, count: args.numbers.length };
  },

  generateId: async (args: { prefix?: string }) => {
    const id = Math.random().toString(36).substr(2, 9);
    return { id: args.prefix ? `${args.prefix}_${id}` : id };
  },

  getCurrentTime: async () => {
    return {
      timestamp: new Date().toISOString(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      unix: Math.floor(Date.now() / 1000)
    };
  }
};

// MCP compatible tools will be created after initialization
let mcpCompatibleTools: any = {};

// --- Agent Node Creation ---

const createLLMNode = (systemMessage: string) => {
  return async (state: any) => {
    const lastMessage = getLastMessage(state);

    console.log(`🤖 LLM Processing (${systemMessage.substring(0, 30)}...): ${lastMessage?.content?.substring(0, 100)}...`);
    
    let response: string;
    let toolCalls: any[] | undefined;

    // Check if the last message is a tool result
    if (lastMessage?.role === 'tool') {
      const toolResult = JSON.parse(lastMessage.content);
      response = `Excellent! I've completed the task successfully. Here are the results:\n\n${JSON.stringify(toolResult, null, 2)}\n\nIs there anything else you'd like me to help you with?`;
    } else {
      // Analyze user request and decide which tools to use
      const content = lastMessage?.content || '';
      
      if (content.toLowerCase().includes('sum') || content.toLowerCase().includes('add')) {
        response = "I'll calculate the sum for you.";
        toolCalls = [{
          id: 'call_' + Math.random().toString(36).substr(2, 9),
          type: 'function',
          function: {
            name: 'calculateSum',
            arguments: JSON.stringify({ numbers: [10, 20, 30, 40] })
          }
        }];
      } else if (content.toLowerCase().includes('id') || content.toLowerCase().includes('generate')) {
        response = "I'll generate a unique ID for you.";
        toolCalls = [{
          id: 'call_' + Math.random().toString(36).substr(2, 9),
          type: 'function',
          function: {
            name: 'generateId',
            arguments: JSON.stringify({ prefix: 'demo' })
          }
        }];
      } else if (content.toLowerCase().includes('time') || content.toLowerCase().includes('date')) {
        response = "I'll get the current time for you.";
        toolCalls = [{
          id: 'call_' + Math.random().toString(36).substr(2, 9),
          type: 'function',
          function: {
            name: 'getCurrentTime',
            arguments: JSON.stringify({})
          }
        }];
      } else if (content.toLowerCase().includes('weather')) {
        response = "I'll check the weather for you using the MCP weather service.";
        toolCalls = [{
          id: 'call_' + Math.random().toString(36).substr(2, 9),
          type: 'function',
          function: {
            name: 'getWeather',
            arguments: JSON.stringify({ 
              location: content.includes('Tokyo') ? 'Tokyo' : 'San Francisco',
              unit: 'celsius'
            })
          }
        }];
      } else if (content.toLowerCase().includes('database') || content.toLowerCase().includes('query')) {
        response = "I'll query the database for you using the MCP database service.";
        toolCalls = [{
          id: 'call_' + Math.random().toString(36).substr(2, 9),
          type: 'function',
          function: {
            name: 'queryDatabase',
            arguments: JSON.stringify({ 
              query: 'SELECT * FROM users',
              table: 'users'
            })
          }
        }];
      } else {
        response = `I understand you want help with: "${content}". I have access to both local tools and MCP server tools to assist you.`;
      }
    }

    const assistantMessage = {
      role: 'assistant' as const,
      content: response,
      ...(toolCalls && { tool_calls: toolCalls })
    };

    return addMessage(state, assistantMessage);
  };
};

// --- Demo Functions ---

async function demoMCPAvailability() {
  console.log("🔍 Demo 1: MCP Availability Check");
  console.log("=" .repeat(50));

  const status = await getMCPIntegrationStatus();
  console.log("MCP Integration Status:", JSON.stringify(status, null, 2));

  if (!status.available) {
    console.log("⚠️  MCP SDK not available. Install with: npm install @modelcontextprotocol/sdk");
    console.log("📝 Continuing with simulation mode...\n");
    return false;
  }

  console.log("✅ MCP SDK is available and ready!\n");
  return true;
}

async function demoMCPServer() {
  console.log("🔥 Demo 2: AG3NTIC as MCP Server");
  console.log("=" .repeat(50));

  try {
    // Create MCP server
    const server = new MCPServer({
      name: 'ag3ntic-demo-server',
      version: '1.0.0',
      port: 3001
    });

    // Register our tools
    server.registerTool(
      'getWeather',
      mcpCompatibleTools.weatherTool.mcpDefinition,
      mcpCompatibleTools.weatherTool.mcpHandler
    );

    server.registerTool(
      'queryDatabase',
      mcpCompatibleTools.databaseTool.mcpDefinition,
      mcpCompatibleTools.databaseTool.mcpHandler
    );

    // Register AG3NTIC tools
    server.registerAG3NTICTools(localTools);

    console.log("🚀 Starting MCP HTTP server...");
    await server.startHTTP();

    console.log("📊 Server Stats:", server.getStats());
    console.log("🌐 Server available at: http://localhost:3001/mcp");
    console.log("✅ MCP Server demo complete!\n");

    return server;
  } catch (error) {
    console.log("⚠️  MCP Server demo failed (SDK not available):", error);
    console.log("📝 This would work with: npm install @modelcontextprotocol/sdk\n");
    return null;
  }
}

async function demoMCPClient(serverRunning: boolean = false) {
  console.log("🔥 Demo 3: AG3NTIC as MCP Client");
  console.log("=" .repeat(50));

  if (!serverRunning) {
    console.log("⚠️  No MCP server running, simulating client behavior...");
    
    // Simulate MCP client behavior
    const simulatedAgent = createExecutorAgent(
      createLLMNode('You are a helpful assistant with access to local and simulated MCP tools'),
      {
        ...localTools,
        // Simulate MCP tools
        getWeather: mcpCompatibleTools.weatherTool.toolFunction,
        queryDatabase: mcpCompatibleTools.databaseTool.toolFunction
      }
    );

    const executor = new Executor(simulatedAgent);
    
    const testCases = [
      "What's the weather in Tokyo?",
      "Query the database for user information",
      "Calculate the sum of some numbers"
    ];

    for (const testCase of testCases) {
      console.log(`\n📝 User: ${testCase}`);
      
      const result = await executor.execute({
        messages: [{ role: 'user', content: testCase }]
      });

      const finalMessage = getLastMessage(result);
      console.log(`🤖 Assistant: ${finalMessage?.content}`);
    }

    console.log("\n✅ Simulated MCP Client demo complete!\n");
    return;
  }

  try {
    // Create MCP client
    const client = await createMCPClient({
      name: 'ag3ntic-demo-client',
      version: '1.0.0',
      serverUrl: 'http://localhost:3001/mcp',
      transport: 'http'
    });

    console.log("🔗 Connected to MCP server!");

    // List available tools
    const tools = await client.listTools();
    console.log("🛠️  Available MCP tools:", tools.map((t: any) => t.name));

    // Create MCP-enabled agent
    const mcpAgent = createMCPExecutorAgent(
      createLLMNode('You are a helpful assistant with access to both local and MCP tools'),
      localTools,
      [client]
    );

    const executor = new Executor(mcpAgent);

    const testCases = [
      "What's the weather in Tokyo?",
      "Query the database for information",
      "Generate a unique ID",
      "What's the current time?"
    ];

    for (const testCase of testCases) {
      console.log(`\n📝 User: ${testCase}`);
      
      const result = await executor.execute({
        messages: [{ role: 'user', content: testCase }]
      });

      const finalMessage = getLastMessage(result);
      console.log(`🤖 Assistant: ${finalMessage?.content}`);
    }

    await client.disconnect();
    console.log("\n✅ MCP Client demo complete!\n");

  } catch (error) {
    console.log("⚠️  MCP Client demo failed:", error);
    console.log("📝 Make sure MCP server is running and SDK is installed\n");
  }
}

async function demoAutoMCPAgent() {
  console.log("🔥 Demo 4: Auto-MCP Agent");
  console.log("=" .repeat(50));

  try {
    // Create auto-connecting MCP agent
    const { graph, mcpClients, cleanup } = await createAutoMCPAgent(
      createLLMNode('You are an advanced assistant with automatic MCP integration'),
      localTools,
      [
        {
          name: 'demo-server',
          url: 'http://localhost:3001/mcp',
          transport: 'http'
        }
      ]
    );

    console.log(`🤖 Created agent with ${mcpClients.length} MCP connections`);

    const executor = new Executor(graph);
    
    const result = await executor.execute({
      messages: [{ role: 'user', content: 'Show me the weather and generate an ID' }]
    });

    const finalMessage = getLastMessage(result);
    console.log(`🤖 Assistant: ${finalMessage?.content}`);

    await cleanup();
    console.log("\n✅ Auto-MCP Agent demo complete!\n");

  } catch (error) {
    console.log("⚠️  Auto-MCP Agent demo failed:", error);
    console.log("📝 This requires MCP server to be running\n");
  }
}

// --- Main Demo Function ---

async function main() {
  console.log("🚀 AG3NTIC Model Context Protocol (MCP) Integration Demo");
  console.log("=" .repeat(70));
  console.log("Demonstrating comprehensive MCP integration:\n");

  try {
    // Initialize MCP modules
    await initializeMCP();

    // Check MCP availability
    await demoMCPAvailability();

    // Start MCP server
    const server = await demoMCPServer();
    const serverRunning = server !== null;

    // Demo MCP client
    await demoMCPClient(serverRunning);

    // Demo auto-MCP agent
    if (serverRunning) {
      await demoAutoMCPAgent();
    }

    // Cleanup
    if (server) {
      await server.stop();
      console.log("🛑 MCP Server stopped");
    }

    console.log("🎉 All MCP Integration Demos Complete!");
    console.log("\n💡 Key Benefits:");
    console.log("   🔌 Seamless integration with MCP ecosystem");
    console.log("   🛠️  Access to external tools and resources");
    console.log("   🌐 Standardized protocol for AI interoperability");
    console.log("   🔄 Both client and server capabilities");
    console.log("   🚀 Production-ready MCP implementation");

  } catch (error) {
    console.error("❌ Demo failed:", error);
  }
}

// Run the demo
if (require.main === module) {
  main().catch(console.error);
}

export { main as runMCPDemo };
