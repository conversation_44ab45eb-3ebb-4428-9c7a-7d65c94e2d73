import { Graph } from "./graph";
import { AgentState } from "./types";
/**
 * Executor class for running AG3NTIC graphs
 * Provides a simple interface for executing graphs from start to finish
 */
export declare class Executor<TState extends AgentState> {
    private graph;
    constructor(graph: Graph<TState>);
    /**
     * Execute the graph from start to finish and return the final state
     * @param initialState The starting state for the graph
     * @returns Promise that resolves to the final state
     */
    execute(initialState: TState): Promise<TState>;
    /**
     * Execute the graph and collect all intermediate states
     * @param initialState The starting state for the graph
     * @returns Promise that resolves to an array of all states during execution
     */
    executeWithHistory(initialState: TState): Promise<TState[]>;
    /**
     * Execute the graph with a callback for each state change
     * @param initialState The starting state for the graph
     * @param onStateChange Callback function called after each node execution
     * @returns Promise that resolves to the final state
     */
    executeWithCallback(initialState: TState, onStateChange: (state: TState, stepNumber: number) => void | Promise<void>): Promise<TState>;
    /**
     * Get the underlying graph instance
     * @returns The graph instance used by this executor
     */
    getGraph(): Graph<TState>;
}
//# sourceMappingURL=executor.d.ts.map