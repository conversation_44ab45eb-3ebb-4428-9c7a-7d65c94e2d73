import { Graph } from "./graph";
import { AgentState } from "./types";
/**
 * Ultra-lightweight Executor optimized for maximum performance
 *
 * Optimizations:
 * - Direct delegation to graph (zero overhead)
 * - Minimal method calls and object allocation
 * - Fast path for common operations
 */
export declare class Executor<TState extends AgentState> {
    private readonly graph;
    constructor(graph: Graph<TState>);
    /**
     * Execute graph (ultra-fast direct delegation)
     */
    execute(initialState: TState): Promise<TState>;
    /**
     * Execute with history collection (optimized)
     */
    executeWithHistory(initialState: TState): Promise<TState[]>;
    /**
     * Execute with callback (optimized for monitoring)
     */
    executeWithCallback(initialState: TState, onStateChange: (state: TState, stepNumber: number) => void | Promise<void>): Promise<TState>;
    /**
     * Stream execution (direct delegation)
     */
    stream(initialState: TState): AsyncGenerator<TState>;
    /**
     * Get graph instance
     */
    getGraph(): Graph<TState>;
    /**
     * Get performance metrics
     */
    getMetrics(): {
        nodeCount: number;
        pathCount: number;
        isCompiled: boolean;
        entryPoint: string | null;
        nodes: string[];
    };
    /**
     * Fast validation
     */
    validate(): void;
}
//# sourceMappingURL=executor.d.ts.map