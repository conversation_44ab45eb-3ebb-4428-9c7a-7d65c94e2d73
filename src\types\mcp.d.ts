// src/types/mcp.d.ts

/**
 * Type declarations for optional MCP SDK dependencies
 * These allow TypeScript compilation even when MCP SDK is not installed
 */

declare module '@modelcontextprotocol/sdk/client/index.js' {
  export class Client {
    constructor(config: { name: string; version: string });
    connect(transport: any): Promise<void>;
    close(): Promise<void>;
    listTools(): Promise<{ tools: any[] }>;
    callTool(params: { name: string; arguments: any }): Promise<any>;
    listResources(): Promise<{ resources: any[] }>;
    readResource(params: { uri: string }): Promise<any>;
    listPrompts(): Promise<{ prompts: any[] }>;
    getPrompt(params: { name: string; arguments?: any }): Promise<any>;
    request(request: any): Promise<any>;
  }
}

declare module '@modelcontextprotocol/sdk/client/stdio.js' {
  export class StdioClientTransport {
    constructor(config: { command: string; args: string[] });
  }
}

declare module '@modelcontextprotocol/sdk/client/streamableHttp.js' {
  export class StreamableHTTPClientTransport {
    constructor(url: URL);
  }
}

declare module '@modelcontextprotocol/sdk/client/sse.js' {
  export class SSEClientTransport {
    constructor(url: URL);
  }
}

declare module '@modelcontextprotocol/sdk/server/mcp.js' {
  export class McpServer {
    constructor(config: { name: string; version: string });
    connect(transport: any): Promise<void>;
    close(): Promise<void>;
    registerTool(name: string, definition: any, handler: any): void;
    registerResource(name: string, uri: string, definition: any, handler: any): void;
    registerPrompt(name: string, definition: any, handler: any): void;
  }
}

declare module '@modelcontextprotocol/sdk/server/stdio.js' {
  export class StdioServerTransport {
    constructor();
  }
}

declare module '@modelcontextprotocol/sdk/server/streamableHttp.js' {
  export class StreamableHTTPServerTransport {
    constructor(config: any);
    sessionId?: string;
    onclose?: () => void;
    handleRequest(req: any, res: any, body: any): Promise<void>;
  }
}

declare module '@modelcontextprotocol/sdk/package.json' {
  export const version: string;
}

declare module 'cors' {
  interface CorsOptions {
    origin?: string | string[] | boolean | ((origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => void);
    exposedHeaders?: string | string[];
    allowedHeaders?: string | string[];
    credentials?: boolean;
    maxAge?: number;
    preflightContinue?: boolean;
    optionsSuccessStatus?: number;
  }

  interface CorsRequest {
    method?: string;
    headers: Record<string, string>;
  }

  interface CorsResponse {
    statusCode?: number;
    setHeader(key: string, value: string): any;
    end(): any;
  }

  function cors(options?: CorsOptions): (req: CorsRequest, res: CorsResponse, next: () => void) => void;
  export = cors;
}

declare module 'express' {
  interface Request {
    headers: Record<string, string | string[] | undefined>;
    body: any;
  }

  interface Response {
    json(obj: any): Response;
    status(code: number): Response;
    send(body?: any): Response;
    setHeader(name: string, value: string | string[]): Response;
    end(chunk?: any): Response;
  }

  interface Application {
    use(...handlers: any[]): Application;
    all(path: string, ...handlers: any[]): Application;
    listen(port: number, hostname?: string, callback?: () => void): any;
  }

  function express(): Application;
  namespace express {
    function json(): any;
  }

  export = express;
}

declare module 'node:crypto' {
  export function randomUUID(): string;
}
