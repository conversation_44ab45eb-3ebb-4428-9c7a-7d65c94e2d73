// src/index.ts
// Main entry point for the AG3NTIC framework

// Export core framework (provider-agnostic)
export * from './core';

// Export helper libraries
export * from './lib';

// Export integrations
export * from './integrations';

// Export examples for reference
export * from './examples/weather-agent';
export * from './examples/advanced-agent';
export * from './examples/multi-agent-system';

// Framework version
export const VERSION = '1.0.0';

// Welcome message
console.log(`
🚀 AG3NTIC Framework v${VERSION}
   Powerful Simplicity for Agentic Workflows

   Architecture:
   - Core: Provider-agnostic graph execution engine
   - Lib: Reusable helpers and utilities
   - Integrations: Enhanced OpenRouter, OpenAI, Anthropic support with unified interface
   - Templates: Pre-built agent patterns (Executor, Planner, Orchestrator, Research)

   Get started:
   - npm run example:weather
   - npm run example:advanced
   - npm run example:templates
   - npm run example:multi-agent
   - npm run example:enhanced-llm

   Documentation: https://github.com/ag3ntic/ag3ntic
`);
