#!/usr/bin/env -S npm run tsn -T
"use strict";
// src/examples/enhanced-llm-integrations.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.runEnhancedLLMDemo = main;
const client_1 = require("../integrations/openai/client");
const client_2 = require("../integrations/anthropic/client");
/**
 * Enhanced LLM Integrations Demo
 *
 * This example demonstrates the improved LLM integrations with:
 * - Proper TypeScript types
 * - Enhanced error handling
 * - Streaming support
 * - Tool calling
 * - Unified interface
 * - Timeout configuration
 */
// Mock tools for demonstration (commented out for now)
// const mockTools = {
//   getCurrentWeather: async (args: { location: string }) => {
//     console.log(`🌤️  Getting weather for ${args.location}`);
//     return JSON.stringify({
//       location: args.location,
//       temperature: '72°F',
//       condition: 'Sunny',
//       humidity: '45%'
//     });
//   },
//
//   calculateTip: async (args: { amount: number; percentage: number }) => {
//     console.log(`💰 Calculating tip: ${args.percentage}% of $${args.amount}`);
//     const tip = (args.amount * args.percentage) / 100;
//     return JSON.stringify({
//       amount: args.amount,
//       tipPercentage: args.percentage,
//       tipAmount: tip,
//       total: args.amount + tip
//     });
//   }
// };
/**
 * Demo 1: Enhanced OpenAI Integration
 */
async function demoEnhancedOpenAI() {
    console.log("🔥 Demo 1: Enhanced OpenAI Integration");
    console.log("=".repeat(50));
    try {
        // This would require: npm install openai
        console.log("Note: This demo requires 'npm install openai' and OPENAI_API_KEY");
        // Example of how to use the enhanced OpenAI client
        const mockOpenAI = {
            chat: {
                completions: {
                    create: async (params) => {
                        console.log("📡 Mock OpenAI API call:", params.model);
                        return {
                            choices: [{
                                    message: {
                                        role: 'assistant',
                                        content: `Mock response from ${params.model}: I understand you want to test the enhanced OpenAI integration. The weather in San Francisco is sunny and 72°F.`,
                                        tool_calls: params.tools ? [{
                                                id: 'call_123',
                                                type: 'function',
                                                function: {
                                                    name: 'getCurrentWeather',
                                                    arguments: '{"location": "San Francisco"}'
                                                }
                                            }] : undefined
                                    }
                                }],
                            usage: { prompt_tokens: 20, completion_tokens: 30, total_tokens: 50 }
                        };
                    }
                }
            }
        };
        const openaiClient = new client_1.OpenAIClient(mockOpenAI, {
            timeout: 30000,
            maxRetries: 3
        });
        // Test basic completion
        const response = await openaiClient.createChatCompletion([
            { role: 'user', content: 'What\'s the weather like in San Francisco?' }
        ], {
            model: 'gpt-4o',
            temperature: 0.7,
            maxTokens: 150,
            tools: [{
                    type: 'function',
                    function: {
                        name: 'getCurrentWeather',
                        description: 'Get current weather for a location',
                        parameters: {
                            type: 'object',
                            properties: {
                                location: { type: 'string', description: 'City name' }
                            },
                            required: ['location']
                        }
                    }
                }]
        });
        console.log("✅ OpenAI Response:", response.content);
        if (response.tool_calls) {
            console.log("🔧 Tool calls:", response.tool_calls.length);
        }
    }
    catch (error) {
        console.log("⚠️  OpenAI demo skipped:", error instanceof Error ? error.message : String(error));
    }
    console.log();
}
/**
 * Demo 2: Enhanced Anthropic Integration
 */
async function demoEnhancedAnthropic() {
    console.log("🔥 Demo 2: Enhanced Anthropic Integration");
    console.log("=".repeat(50));
    try {
        // This would require: npm install @anthropic-ai/sdk
        console.log("Note: This demo requires 'npm install @anthropic-ai/sdk' and ANTHROPIC_API_KEY");
        // Example of how to use the enhanced Anthropic client
        const mockAnthropic = {
            messages: {
                create: async (params) => {
                    console.log("📡 Mock Anthropic API call:", params.model);
                    return {
                        id: 'msg_123',
                        type: 'message',
                        role: 'assistant',
                        content: [{
                                type: 'text',
                                text: `Mock response from ${params.model}: I'd be happy to help you calculate a tip! For a $50 bill with 20% tip, that would be $10, making the total $60.`
                            }, {
                                type: 'tool_use',
                                id: 'tool_123',
                                name: 'calculateTip',
                                input: { amount: 50, percentage: 20 }
                            }],
                        model: params.model,
                        stop_reason: 'tool_use',
                        usage: { input_tokens: 25, output_tokens: 35 }
                    };
                },
                stream: async (params) => {
                    console.log("📡 Mock Anthropic streaming call:", params.model);
                    return {
                        on: (event, callback) => {
                            if (event === 'text') {
                                setTimeout(() => callback('Mock '), 100);
                                setTimeout(() => callback('streaming '), 200);
                                setTimeout(() => callback('response!'), 300);
                            }
                        },
                        finalMessage: async () => ({
                            content: [{ type: 'text', text: 'Mock streaming response!' }]
                        })
                    };
                }
            }
        };
        const anthropicClient = new client_2.AnthropicClient(mockAnthropic, {
            timeout: 30000,
            maxRetries: 3
        });
        // Test basic completion
        const response = await anthropicClient.createMessage([
            { role: 'user', content: 'Calculate a 20% tip on a $50 bill' }
        ], {
            model: 'claude-3-5-sonnet-20241022',
            temperature: 0.7,
            maxTokens: 150,
            systemMessage: 'You are a helpful assistant that can calculate tips.',
            tools: [{
                    name: 'calculateTip',
                    description: 'Calculate tip amount and total',
                    input_schema: {
                        type: 'object',
                        properties: {
                            amount: { type: 'number', description: 'Bill amount' },
                            percentage: { type: 'number', description: 'Tip percentage' }
                        },
                        required: ['amount', 'percentage']
                    }
                }]
        });
        console.log("✅ Anthropic Response:", response.content);
        if (response.tool_calls) {
            console.log("🔧 Tool calls:", response.tool_calls.length);
        }
        // Test streaming
        console.log("\n🌊 Testing streaming...");
        let streamedText = '';
        await anthropicClient.createStreamingMessage([
            { role: 'user', content: 'Tell me a short joke' }
        ], {
            model: 'claude-3-5-sonnet-20241022'
        }, (chunk) => {
            streamedText += chunk;
            process.stdout.write(chunk);
        });
        console.log("\n✅ Streaming completed!");
    }
    catch (error) {
        console.log("⚠️  Anthropic demo skipped:", error instanceof Error ? error.message : String(error));
    }
    console.log();
}
/**
 * Demo 3: Unified Interface
 */
async function demoUnifiedInterface() {
    console.log("🔥 Demo 3: Unified Interface");
    console.log("=".repeat(50));
    try {
        console.log("Note: This demo shows the unified interface concept");
        // Mock unified client factory
        const createMockUnifiedClient = async (provider) => {
            return {
                createCompletion: async (_messages, options = {}) => {
                    console.log(`📡 Mock ${provider} unified call:`, options.systemMessage?.substring(0, 30) + '...');
                    return {
                        role: 'assistant',
                        content: `Mock unified response from ${provider}: I understand your request and I'm ready to help!`
                    };
                },
                createStreamingCompletion: async (_messages, _options = {}, onChunk) => {
                    console.log(`🌊 Mock ${provider} streaming call`);
                    const text = `Streaming from ${provider}!`;
                    for (const char of text) {
                        onChunk?.(char);
                        await new Promise(resolve => setTimeout(resolve, 50));
                    }
                    return {
                        role: 'assistant',
                        content: text
                    };
                },
                getProvider: () => provider,
                testConnection: async () => true,
                getAvailableModels: async () => provider === 'openai'
                    ? ['gpt-4o', 'gpt-4', 'gpt-3.5-turbo']
                    : ['claude-3-5-sonnet-20241022', 'claude-3-opus-20240229']
            };
        };
        // Test with both providers using the same interface
        for (const provider of ['openai', 'anthropic']) {
            console.log(`\n🔄 Testing ${provider.toUpperCase()} via unified interface:`);
            const client = await createMockUnifiedClient(provider);
            // Test basic completion
            const response = await client.createCompletion([
                { role: 'user', content: 'Hello, how are you?' }
            ], {
                systemMessage: 'You are a helpful and friendly assistant.',
                temperature: 0.7,
                maxTokens: 100
            });
            console.log(`✅ ${provider} response:`, response.content);
            // Test streaming
            console.log(`🌊 ${provider} streaming: `);
            await client.createStreamingCompletion([
                { role: 'user', content: 'Count to 5' }
            ], {}, (chunk) => process.stdout.write(chunk));
            console.log();
            // Test connection and models
            const isConnected = await client.testConnection();
            const models = await client.getAvailableModels();
            console.log(`🔗 Connection: ${isConnected ? '✅' : '❌'}`);
            console.log(`🤖 Available models: ${models.slice(0, 2).join(', ')}...`);
        }
    }
    catch (error) {
        console.log("⚠️  Unified interface demo error:", error instanceof Error ? error.message : String(error));
    }
    console.log();
}
/**
 * Demo 4: Error Handling and Configuration
 */
async function demoErrorHandlingAndConfig() {
    console.log("🔥 Demo 4: Error Handling & Configuration");
    console.log("=".repeat(50));
    // Mock client with error scenarios
    const mockClientWithErrors = {
        chat: {
            completions: {
                create: async (params) => {
                    if (params.model === 'invalid-model') {
                        const error = new Error('Model not found');
                        error.status = 404;
                        error.type = 'invalid_request_error';
                        throw error;
                    }
                    if (params.max_tokens > 10000) {
                        const error = new Error('Token limit exceeded');
                        error.status = 400;
                        error.type = 'invalid_request_error';
                        throw error;
                    }
                    return {
                        choices: [{
                                message: {
                                    role: 'assistant',
                                    content: 'Success with proper error handling!'
                                }
                            }]
                    };
                }
            }
        }
    };
    const client = new client_1.OpenAIClient(mockClientWithErrors, {
        timeout: 5000,
        maxRetries: 2
    });
    // Test error scenarios
    const testCases = [
        {
            name: 'Invalid Model',
            options: { model: 'invalid-model' },
            expectError: true
        },
        {
            name: 'Token Limit Exceeded',
            options: { maxTokens: 15000 },
            expectError: true
        },
        {
            name: 'Valid Request',
            options: { model: 'gpt-4o', maxTokens: 100 },
            expectError: false
        }
    ];
    for (const testCase of testCases) {
        try {
            console.log(`\n🧪 Testing: ${testCase.name}`);
            const response = await client.createChatCompletion([
                { role: 'user', content: 'Hello' }
            ], testCase.options);
            if (testCase.expectError) {
                console.log("❌ Expected error but got success");
            }
            else {
                console.log("✅ Success:", response.content);
            }
        }
        catch (error) {
            if (testCase.expectError) {
                console.log("✅ Expected error caught:", error instanceof Error ? error.message : String(error));
            }
            else {
                console.log("❌ Unexpected error:", error instanceof Error ? error.message : String(error));
            }
        }
    }
    console.log();
}
/**
 * Main demo function
 */
async function main() {
    console.log("🚀 AG3NTIC Enhanced LLM Integrations Demo");
    console.log("=".repeat(60));
    console.log();
    await demoEnhancedOpenAI();
    await demoEnhancedAnthropic();
    await demoUnifiedInterface();
    await demoErrorHandlingAndConfig();
    console.log("🎉 All demos completed!");
    console.log("\n💡 Key improvements:");
    console.log("   ✅ Proper TypeScript types");
    console.log("   ✅ Enhanced error handling");
    console.log("   ✅ Streaming support");
    console.log("   ✅ Modern tool calling patterns");
    console.log("   ✅ Unified interface");
    console.log("   ✅ Timeout & retry configuration");
    console.log("   ✅ Optional SDK dependencies");
}
// Run the demo
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=enhanced-llm-integrations.js.map