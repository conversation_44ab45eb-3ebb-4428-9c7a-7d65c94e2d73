import { Graph, AgentState, NodeFunction } from '../../core';
import { ToolMap } from '../../lib';
/**
 * Configuration options for the Executor Agent
 */
export interface ExecutorAgentOptions {
    /** Map of available tools */
    tools: ToolMap;
    /** System message for the agent */
    systemMessage?: string;
    /** Custom agent node function (if you want to override the default) */
    agentNode?: NodeFunction<any>;
    /** Whether to enable enhanced tool execution with retries and logging */
    enhancedTools?: boolean;
    /** Tool execution options */
    toolOptions?: {
        logExecution?: boolean;
        timeout?: number;
        retries?: number;
    };
}
/**
 * State interface for Executor Agent
 */
export interface ExecutorAgentState extends AgentState {
    /** The task to execute */
    task?: string;
    /** Available tools (for reference) */
    availableTools?: string[];
    /** Execution status */
    status?: 'pending' | 'executing' | 'completed' | 'failed';
    /** Final result */
    result?: string;
}
/**
 * Create an Executor Agent - the fundamental "doer" agent
 *
 * This is the most basic but essential template. It receives a task,
 * uses available tools to complete it, and returns the result.
 *
 * @param options Configuration options
 * @returns Configured Graph ready for execution
 */
export declare function createExecutorAgent<TState extends ExecutorAgentState = ExecutorAgentState>(options: ExecutorAgentOptions): Graph<TState>;
/**
 * Create a simple executor agent with minimal configuration
 *
 * @param tools Map of available tools
 * @param systemMessage Optional system message
 * @returns Configured Graph
 */
export declare function createSimpleExecutor<TState extends ExecutorAgentState = ExecutorAgentState>(tools: ToolMap, systemMessage?: string): Graph<TState>;
/**
 * Create an enhanced executor agent with retries and logging
 *
 * @param tools Map of available tools
 * @param options Enhanced execution options
 * @returns Configured Graph
 */
export declare function createEnhancedExecutor<TState extends ExecutorAgentState = ExecutorAgentState>(tools: ToolMap, options?: {
    systemMessage?: string;
    logExecution?: boolean;
    timeout?: number;
    retries?: number;
}): Graph<TState>;
//# sourceMappingURL=index.d.ts.map