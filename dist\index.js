"use strict";
// src/index.ts
// Main entry point for the AG3NTIC framework
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VERSION = void 0;
// Export core framework (provider-agnostic)
__exportStar(require("./core"), exports);
// Export helper libraries
__exportStar(require("./lib"), exports);
// Export integrations
__exportStar(require("./integrations"), exports);
// Export agent templates
__exportStar(require("./templates"), exports);
// Export examples for reference
__exportStar(require("./examples/weather-agent"), exports);
__exportStar(require("./examples/advanced-agent"), exports);
__exportStar(require("./examples/multi-agent-system"), exports);
// Framework version
exports.VERSION = '1.0.0';
// Welcome message
console.log(`
🚀 AG3NTIC Framework v${exports.VERSION}
   Powerful Simplicity for Agentic Workflows

   Architecture:
   - Core: Provider-agnostic graph execution engine
   - Lib: Reusable helpers and utilities
   - Integrations: Enhanced OpenRouter, OpenAI, Anthropic support with unified interface
   - Templates: Pre-built agent patterns (Executor, Planner, Orchestrator, Research)

   Get started:
   - npm run example:weather
   - npm run example:advanced
   - npm run example:multi-agent
   - npm run example:enhanced-llm

   Documentation: https://github.com/ag3ntic/ag3ntic
`);
//# sourceMappingURL=index.js.map