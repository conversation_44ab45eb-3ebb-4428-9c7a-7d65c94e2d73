// src/integrations/openrouter/client.ts

import { 
  AgentState, 
  MCPMessage, 
  MCPAssistantMessage,
  NodeFunction
} from "../../core/types";
import { addMessage } from "../../lib/agent-helpers";

/**
 * OpenRouter API configuration
 */
export interface OpenRouterConfig {
  apiKey: string;
  baseUrl?: string;
  defaultModel?: string;
  defaultTemperature?: number;
  timeout?: number;
}

/**
 * Tool definition for OpenRouter API
 */
export interface OpenRouterTool {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: {
      type: 'object';
      properties: Record<string, any>;
      required?: string[];
    };
  };
}

/**
 * Options for creating an OpenRouter agent node
 */
export interface OpenRouterAgentOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  systemMessage?: string;
  tools?: OpenRouterTool[];
}

/**
 * OpenRouter API client
 */
export class OpenRouterClient {
  private config: Required<OpenRouterConfig>;

  constructor(config: OpenRouterConfig) {
    this.config = {
      baseUrl: 'https://openrouter.ai/api/v1',
      defaultModel: 'anthropic/claude-3.5-sonnet',
      defaultTemperature: 0.7,
      timeout: 30000,
      ...config
    };
  }

  /**
   * Make a chat completion request to OpenRouter
   */
  async createChatCompletion(
    messages: MCPMessage[],
    options: OpenRouterAgentOptions = {}
  ): Promise<MCPAssistantMessage> {
    const {
      model = this.config.defaultModel,
      temperature = this.config.defaultTemperature,
      maxTokens = 4096,
      tools
    } = options;

    // Convert MCP messages to OpenRouter format
    const openRouterMessages = messages
      .filter(msg => msg.role !== 'system' || msg.content) // Filter out empty system messages
      .map(msg => ({
        role: msg.role,
        content: msg.content || (msg.role === 'tool' ? msg.content : null)
      }));

    const requestBody: any = {
      model,
      messages: openRouterMessages,
      temperature,
      max_tokens: maxTokens
    };

    // Add tools if provided
    if (tools && tools.length > 0) {
      requestBody.tools = tools;
      requestBody.tool_choice = 'auto';
    }

    try {
      const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://ag3ntic.dev', // Optional: for OpenRouter analytics
          'X-Title': 'AG3NTIC Framework' // Optional: for OpenRouter analytics
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(this.config.timeout)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const data = await response.json() as any;
      const choice = data.choices?.[0];

      if (!choice) {
        throw new Error('No response choice returned from OpenRouter API');
      }

      // Convert OpenRouter response to MCP format
      const assistantMessage: MCPAssistantMessage = {
        role: 'assistant',
        content: choice.message.content,
        tool_calls: choice.message.tool_calls?.map((tc: any) => ({
          id: tc.id,
          type: 'function',
          function: {
            name: tc.function.name,
            arguments: tc.function.arguments
          }
        }))
      };

      return assistantMessage;

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error(`OpenRouter API request timeout after ${this.config.timeout}ms`);
      }
      throw error;
    }
  }

  /**
   * Create an agent node that uses OpenRouter for LLM calls
   */
  createAgentNode<TState extends AgentState>(
    options: OpenRouterAgentOptions = {}
  ): NodeFunction<TState> {
    return async (state: TState): Promise<Partial<TState>> => {
      const { systemMessage } = options;
      
      // Prepare messages for the LLM
      let messages = [...state.messages];
      
      // Add system message if provided and not already present
      if (systemMessage && (messages.length === 0 || messages[0].role !== 'system')) {
        messages.unshift({
          role: 'system',
          content: systemMessage
        });
      }

      try {
        const assistantMessage = await this.createChatCompletion(messages, options);
        return addMessage(state, assistantMessage);
        
      } catch (error) {
        throw new Error(`OpenRouter agent error: ${error instanceof Error ? error.message : String(error)}`);
      }
    };
  }

  /**
   * Test the connection to OpenRouter
   */
  async testConnection(): Promise<boolean> {
    try {
      const testMessage: MCPMessage = {
        role: 'user',
        content: 'Hello, this is a connection test.'
      };

      await this.createChatCompletion([testMessage], {
        model: this.config.defaultModel,
        maxTokens: 10
      });

      return true;
    } catch (error) {
      console.error('OpenRouter connection test failed:', error);
      return false;
    }
  }

  /**
   * Get available models from OpenRouter
   */
  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await fetch(`${this.config.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch models: ${response.statusText}`);
      }

      const data = await response.json() as any;
      return data.data?.map((model: any) => model.id) || [];
    } catch (error) {
      console.error('Failed to get available models:', error);
      return [];
    }
  }
}
