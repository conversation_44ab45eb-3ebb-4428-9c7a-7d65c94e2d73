"use strict";
// src/templates/orchestrator/index.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.createOrchestratorAgent = createOrchestratorAgent;
const core_1 = require("../../core");
const lib_1 = require("../../lib");
/**
 * Create an Orchestrator Agent - manages and coordinates multiple worker agents
 *
 * This is the "team lead" that takes a plan and delegates tasks to specialized
 * worker agents, managing their execution and synthesizing the final result.
 *
 * @param options Configuration options
 * @returns Configured Graph ready for execution
 */
function createOrchestratorAgent(options) {
    const { workers, orchestratorNode, enableParallelExecution = false } = options;
    const systemMessage = options.systemMessage || `You are an expert orchestrator managing a team of specialized agents. Your responsibilities:

1. Analyze the plan and assign tasks to the most suitable workers
2. Monitor execution progress and handle any issues
3. Coordinate dependencies between tasks
4. Synthesize results from multiple workers into a coherent final output
5. Ensure quality and completeness of the overall deliverable

Available workers: ${workers.map(w => `${w.name} (${w.capabilities.join(', ')})`).join(', ')}`;
    const maxIterations = options.maxIterations || 50;
    console.log(`Orchestrator initialized with ${workers.length} workers, max ${maxIterations} iterations`);
    console.log(`System message: ${systemMessage.substring(0, 50)}...`);
    // Create worker execution nodes
    const workerNodes = {};
    workers.forEach(worker => {
        workerNodes[`worker_${worker.id}`] = async (state) => {
            // Execute the worker's graph with the current task
            const currentStep = getCurrentStep(state);
            if (!currentStep) {
                throw new Error('No current step to execute');
            }
            try {
                // Create initial state for the worker
                const workerInitialState = {
                    messages: [
                        { role: 'user', content: currentStep.step.description }
                    ]
                };
                // Execute the worker's graph
                const executor = new core_1.Executor(worker.graph);
                const workerResult = await executor.execute(workerInitialState);
                // Extract the result
                const lastMessage = (0, lib_1.getLastMessage)(workerResult);
                const result = lastMessage?.content || 'Task completed';
                // Update step execution status
                const updatedExecutions = state.stepExecutions?.map(exec => exec.step.id === currentStep.step.id
                    ? { ...exec, status: 'completed', result, endTime: new Date() }
                    : exec) || [];
                return {
                    stepExecutions: updatedExecutions,
                    scratchpad: {
                        ...state.scratchpad,
                        [currentStep.step.id]: result
                    }
                };
            }
            catch (error) {
                // Handle worker execution error
                const updatedExecutions = state.stepExecutions?.map(exec => exec.step.id === currentStep.step.id
                    ? {
                        ...exec,
                        status: 'failed',
                        error: error instanceof Error ? error.message : String(error),
                        endTime: new Date()
                    }
                    : exec) || [];
                return { stepExecutions: updatedExecutions };
            }
        };
    });
    // Create default orchestrator node
    const defaultOrchestratorNode = async (state) => {
        // Initialize orchestration if needed
        if (!state.stepExecutions && state.plan) {
            const stepExecutions = state.plan.map(step => ({
                step,
                status: 'pending'
            }));
            return {
                stepExecutions,
                orchestrationStatus: 'executing',
                currentStepIndex: 0,
                scratchpad: {},
                availableWorkers: workers.map(w => w.id)
            };
        }
        // Check if orchestration is complete
        if (state.stepExecutions?.every(exec => exec.status === 'completed' || exec.status === 'skipped')) {
            const finalResult = synthesizeResults(state.stepExecutions, state.scratchpad || {});
            const assistantMessage = {
                role: 'assistant',
                content: `Orchestration completed successfully!\n\nFinal Result:\n${finalResult}`
            };
            return {
                ...(0, lib_1.addMessage)(state, assistantMessage),
                orchestrationStatus: 'completed',
                finalResult
            };
        }
        // Find next step to execute
        const nextStep = findNextExecutableStep(state.stepExecutions || [], enableParallelExecution);
        if (!nextStep) {
            // No more steps to execute, but not all completed - check for failures
            const failedSteps = state.stepExecutions?.filter(exec => exec.status === 'failed') || [];
            if (failedSteps.length > 0) {
                const assistantMessage = {
                    role: 'assistant',
                    content: `Orchestration failed. Failed steps: ${failedSteps.map(s => s.step.description).join(', ')}`
                };
                return {
                    ...(0, lib_1.addMessage)(state, assistantMessage),
                    orchestrationStatus: 'failed'
                };
            }
            return {}; // Wait for current executions to complete
        }
        // Assign worker to the next step
        const assignedWorker = assignWorkerToStep(nextStep.step, workers);
        if (!assignedWorker) {
            // No suitable worker found - skip this step
            const updatedExecutions = state.stepExecutions?.map(exec => exec.step.id === nextStep.step.id
                ? { ...exec, status: 'skipped', error: 'No suitable worker found' }
                : exec) || [];
            return { stepExecutions: updatedExecutions };
        }
        // Update step status to assigned
        const updatedExecutions = state.stepExecutions?.map(exec => exec.step.id === nextStep.step.id
            ? {
                ...exec,
                status: 'assigned',
                assignedWorker: assignedWorker.id,
                startTime: new Date()
            }
            : exec) || [];
        const assistantMessage = {
            role: 'assistant',
            content: `Assigning task "${nextStep.step.description}" to ${assignedWorker.name}`
        };
        return {
            ...(0, lib_1.addMessage)(state, assistantMessage),
            stepExecutions: updatedExecutions
        };
    };
    // Use provided orchestrator node or default
    const finalOrchestratorNode = orchestratorNode || defaultOrchestratorNode;
    // Build the graph with orchestrator and worker nodes
    const graph = new core_1.Graph()
        .addNode('orchestrator', finalOrchestratorNode)
        .setEntryPoint('orchestrator');
    // Add worker nodes
    workers.forEach(worker => {
        graph.addNode(`worker_${worker.id}`, workerNodes[`worker_${worker.id}`]);
    });
    // Add routing logic
    graph.addConditionalEdge('orchestrator', (state) => {
        const currentStep = getCurrentStep(state);
        if (currentStep?.assignedWorker) {
            return `worker_${currentStep.assignedWorker}`;
        }
        // Check if orchestration is complete
        if (state.orchestrationStatus === 'completed' || state.orchestrationStatus === 'failed') {
            return '__end__';
        }
        return 'continue';
    }, {
        ...workers.reduce((acc, worker) => {
            acc[`worker_${worker.id}`] = `worker_${worker.id}`;
            return acc;
        }, {}),
        'continue': 'orchestrator',
        '__end__': '__END__'
    });
    // Add edges from workers back to orchestrator
    workers.forEach(worker => {
        graph.addEdge(`worker_${worker.id}`, 'orchestrator');
    });
    return graph;
}
/**
 * Helper functions
 */
function getCurrentStep(state) {
    return state.stepExecutions?.find(exec => exec.status === 'assigned');
}
function findNextExecutableStep(executions, enableParallel) {
    console.log(`Finding next step (parallel: ${enableParallel})`);
    // Find steps that are ready to execute (dependencies met)
    const readySteps = executions.filter(exec => {
        if (exec.status !== 'pending')
            return false;
        // Check if all dependencies are completed
        const dependencies = exec.step.dependencies || [];
        return dependencies.every(depId => executions.find(e => e.step.id === depId)?.status === 'completed');
    });
    return readySteps[0]; // For now, just return the first ready step
}
function assignWorkerToStep(step, workers) {
    // Find the best worker for this step based on required capabilities
    const requiredCapabilities = step.requiredCapabilities || [];
    // Score workers based on capability match
    const scoredWorkers = workers.map(worker => {
        const matchingCapabilities = requiredCapabilities.filter(cap => worker.capabilities.includes(cap));
        const score = matchingCapabilities.length / Math.max(requiredCapabilities.length, 1);
        return { worker, score };
    });
    // Sort by score and return the best match
    scoredWorkers.sort((a, b) => b.score - a.score);
    return scoredWorkers[0]?.score > 0 ? scoredWorkers[0].worker : workers[0]; // Fallback to first worker
}
function synthesizeResults(executions, scratchpad) {
    console.log(`Synthesizing results from ${Object.keys(scratchpad).length} scratchpad entries`);
    const completedSteps = executions.filter(exec => exec.status === 'completed');
    if (completedSteps.length === 0) {
        return 'No steps were completed successfully.';
    }
    const results = completedSteps.map(exec => `${exec.step.description}: ${exec.result || 'Completed'}`).join('\n');
    return `Completed ${completedSteps.length} steps:\n\n${results}`;
}
//# sourceMappingURL=index.js.map