// src/integrations/anthropic/client.ts

import {
  AgentState,
  MCPMessage,
  MCPAssistantMessage,
  NodeFunction
} from "../../core/types";
import { addMessage } from "../../lib/agent-helpers";

/**
 * Anthropic-compatible types (mirrors official SDK without requiring it)
 */
export interface AnthropicTool {
  name: string;
  description: string;
  input_schema: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

export interface AnthropicToolUse {
  type: 'tool_use';
  id: string;
  name: string;
  input: Record<string, any>;
}

export interface AnthropicTextBlock {
  type: 'text';
  text: string;
}

export interface AnthropicContentBlock {
  type: 'text' | 'tool_use' | 'tool_result';
  text?: string;
  id?: string;
  name?: string;
  input?: Record<string, any>;
  tool_use_id?: string;
  content?: string;
}

export interface AnthropicMessage {
  role: 'user' | 'assistant';
  content: string | AnthropicContentBlock[];
}

export interface AnthropicUsage {
  input_tokens: number;
  output_tokens: number;
}

export interface AnthropicResponse {
  id: string;
  type: 'message';
  role: 'assistant';
  content: AnthropicContentBlock[];
  model: string;
  stop_reason: 'end_turn' | 'max_tokens' | 'stop_sequence' | 'tool_use' | null;
  stop_sequence: string | null;
  usage: AnthropicUsage;
}

export interface AnthropicError extends Error {
  status?: number;
  type?: string;
}

/**
 * Configuration options for Anthropic client
 */
export interface AnthropicClientConfig {
  apiKey?: string;
  baseURL?: string;
  timeout?: number;
  maxRetries?: number;
  defaultHeaders?: Record<string, string>;
}

/**
 * Options for creating an Anthropic agent node
 */
export interface AnthropicAgentOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  systemMessage?: string;
  tools?: AnthropicTool[];
  toolChoice?: { type: 'auto' | 'any' | 'tool'; name?: string };
  stream?: boolean;
  timeout?: number;
}

/**
 * Anthropic client wrapper for AG3NTIC
 * This assumes you have the official Anthropic SDK installed: npm install @anthropic-ai/sdk
 *
 * Features:
 * - Proper TypeScript types
 * - Enhanced error handling
 * - Streaming support with events
 * - Tool calling with proper message format
 * - Timeout configuration
 * - Batch processing support
 */
export class AnthropicClient {
  private anthropic: any; // Type as 'any' to avoid requiring Anthropic SDK as dependency
  private config: AnthropicClientConfig;

  constructor(anthropicInstance: any, config: AnthropicClientConfig = {}) {
    this.anthropic = anthropicInstance;
    this.config = config;

    // Validate that we have a proper Anthropic instance
    if (!anthropicInstance?.messages?.create) {
      throw new Error(
        'Invalid Anthropic instance. Please install the Anthropic SDK: npm install @anthropic-ai/sdk'
      );
    }
  }

  /**
   * Create a message using Anthropic Claude
   */
  async createMessage(
    messages: MCPMessage[],
    options: AnthropicAgentOptions = {}
  ): Promise<MCPAssistantMessage> {
    const {
      model = 'claude-3-5-sonnet-20241022',
      temperature = 0.7,
      maxTokens = 4096,
      systemMessage,
      tools
    } = options;

    // Separate system message from conversation messages
    const conversationMessages = messages.filter(msg => msg.role !== 'system');
    const systemContent = systemMessage || messages.find(msg => msg.role === 'system')?.content;

    // Convert MCP messages to Anthropic format
    const anthropicMessages = conversationMessages.map(msg => {
      if (msg.role === 'tool') {
        return {
          role: 'user',
          content: [{
            type: 'tool_result',
            tool_use_id: msg.tool_call_id,
            content: msg.content
          }]
        };
      }
      
      if (msg.role === 'assistant' && msg.tool_calls) {
        const content = [];
        
        // Add text content if present
        if (msg.content) {
          content.push({
            type: 'text',
            text: msg.content
          });
        }
        
        // Add tool use blocks
        msg.tool_calls.forEach(tc => {
          content.push({
            type: 'tool_use',
            id: tc.id,
            name: tc.function.name,
            input: JSON.parse(tc.function.arguments)
          });
        });
        
        return {
          role: 'assistant',
          content
        };
      }
      
      return {
        role: msg.role === 'user' ? 'user' : 'assistant',
        content: msg.content || ''
      };
    });

    const requestParams: any = {
      model,
      messages: anthropicMessages,
      temperature,
      max_tokens: maxTokens
    };

    // Add system message if provided
    if (systemContent) {
      requestParams.system = systemContent;
    }

    // Add tools if provided
    if (tools && tools.length > 0) {
      requestParams.tools = tools;
    }

    try {
      const response = await this.anthropic.messages.create(requestParams);

      // Convert Anthropic response to MCP format
      let content: string | null = null;
      let tool_calls: any[] | undefined = undefined;

      if (response.content) {
        const textBlocks = response.content.filter((block: any) => block.type === 'text');
        const toolBlocks = response.content.filter((block: any) => block.type === 'tool_use');

        // Combine text content
        if (textBlocks.length > 0) {
          content = textBlocks.map((block: any) => block.text).join('\n');
        }

        // Convert tool use blocks to MCP format
        if (toolBlocks.length > 0) {
          tool_calls = toolBlocks.map((block: any) => ({
            id: block.id,
            type: 'function',
            function: {
              name: block.name,
              arguments: JSON.stringify(block.input)
            }
          }));
        }
      }

      const assistantMessage: MCPAssistantMessage = {
        role: 'assistant',
        content,
        ...(tool_calls && { tool_calls })
      };

      return assistantMessage;

    } catch (error) {
      throw this.handleAnthropicError(error);
    }
  }

  /**
   * Convert MCP messages to Anthropic format
   */
  private convertMCPToAnthropic(messages: MCPMessage[]): AnthropicMessage[] {
    const conversationMessages = messages.filter(msg => msg.role !== 'system');

    return conversationMessages.map(msg => {
      if (msg.role === 'tool') {
        return {
          role: 'user',
          content: [{
            type: 'tool_result',
            tool_use_id: msg.tool_call_id,
            content: msg.content
          }]
        };
      }

      if (msg.role === 'assistant' && msg.tool_calls) {
        const content: AnthropicContentBlock[] = [];

        // Add text content if present
        if (msg.content) {
          content.push({
            type: 'text',
            text: msg.content
          });
        }

        // Add tool use blocks
        msg.tool_calls.forEach(tc => {
          content.push({
            type: 'tool_use',
            id: tc.id,
            name: tc.function.name,
            input: JSON.parse(tc.function.arguments)
          });
        });

        return {
          role: 'assistant',
          content
        };
      }

      return {
        role: msg.role === 'user' ? 'user' : 'assistant',
        content: msg.content || ''
      };
    });
  }

  /**
   * Convert Anthropic response to MCP format
   */
  private convertAnthropicToMCP(response: AnthropicResponse): MCPAssistantMessage {
    let content: string | null = null;
    let tool_calls: any[] | undefined = undefined;

    if (response.content) {
      const textBlocks = response.content.filter(block => block.type === 'text');
      const toolBlocks = response.content.filter(block => block.type === 'tool_use');

      // Combine text content
      if (textBlocks.length > 0) {
        content = textBlocks.map(block => block.text).join('\n');
      }

      // Convert tool use blocks to MCP format
      if (toolBlocks.length > 0) {
        tool_calls = toolBlocks.map(block => ({
          id: block.id!,
          type: 'function',
          function: {
            name: block.name!,
            arguments: JSON.stringify(block.input)
          }
        }));
      }
    }

    const assistantMessage: MCPAssistantMessage = {
      role: 'assistant',
      content,
      ...(tool_calls && { tool_calls })
    };

    return assistantMessage;
  }

  /**
   * Handle Anthropic API errors with proper typing
   */
  private handleAnthropicError(error: any): Error {
    if (error?.status) {
      // This is likely an Anthropic API error
      const anthropicError = error as AnthropicError;
      return new Error(
        `Anthropic API error (${anthropicError.status}): ${anthropicError.message || 'Unknown error'}`
      );
    }

    if (error instanceof Error) {
      return new Error(`Anthropic error: ${error.message}`);
    }

    return new Error(`Anthropic error: ${String(error)}`);
  }

  /**
   * Create a streaming message using Anthropic Claude
   */
  async createStreamingMessage(
    messages: MCPMessage[],
    options: AnthropicAgentOptions = {},
    onChunk?: (chunk: string) => void,
    onEvent?: (event: any) => void
  ): Promise<MCPAssistantMessage> {
    const {
      model = 'claude-3-5-sonnet-20241022',
      temperature = 0.7,
      maxTokens = 4096,
      systemMessage,
      tools,
      toolChoice,
      timeout
    } = options;

    const anthropicMessages = this.convertMCPToAnthropic(messages);

    const requestParams: any = {
      model,
      messages: anthropicMessages,
      max_tokens: maxTokens,
      temperature,
      stream: true
    };

    if (systemMessage) {
      requestParams.system = systemMessage;
    }

    if (tools && tools.length > 0) {
      requestParams.tools = tools;
      if (toolChoice) {
        requestParams.tool_choice = toolChoice;
      }
    }

    const requestOptions: any = {};
    if (timeout || this.config.timeout) {
      requestOptions.timeout = timeout || this.config.timeout;
    }

    try {
      const stream = await this.anthropic.messages.stream(requestParams, requestOptions);

      let content = '';
      let toolUses: any[] = [];

      // Handle streaming events
      stream.on('text', (text: string) => {
        content += text;
        onChunk?.(text);
      });

      stream.on('streamEvent', (event: any) => {
        onEvent?.(event);

        // Handle tool use events
        if (event.type === 'content_block_start' && event.content_block?.type === 'tool_use') {
          toolUses.push({
            id: event.content_block.id,
            name: event.content_block.name,
            input: {}
          });
        }

        if (event.type === 'content_block_delta' && event.delta?.type === 'input_json_delta') {
          // Accumulate tool input
          const lastTool = toolUses[toolUses.length - 1];
          if (lastTool) {
            lastTool.input = { ...lastTool.input, ...event.delta.partial_json };
          }
        }
      });

      // Wait for stream completion
      const finalMessage = await stream.finalMessage();

      // Convert to MCP format
      return this.convertAnthropicToMCP(finalMessage);

    } catch (error) {
      throw this.handleAnthropicError(error);
    }
  }

  /**
   * Create an agent node that uses Anthropic for LLM calls
   */
  createAgentNode<TState extends AgentState>(
    options: AnthropicAgentOptions = {}
  ): NodeFunction<TState> {
    return async (state: TState): Promise<Partial<TState>> => {
      try {
        const assistantMessage = await this.createMessage(state.messages, options);
        return addMessage(state, assistantMessage);
        
      } catch (error) {
        throw new Error(`Anthropic agent error: ${error instanceof Error ? error.message : String(error)}`);
      }
    };
  }

  /**
   * Test the connection to Anthropic
   */
  async testConnection(): Promise<boolean> {
    try {
      const testMessage: MCPMessage = {
        role: 'user',
        content: 'Hello, this is a connection test.'
      };

      await this.createMessage([testMessage], {
        model: 'claude-3-haiku-20240307',
        maxTokens: 10
      });

      return true;
    } catch (error) {
      console.error('Anthropic connection test failed:', error);
      return false;
    }
  }

  /**
   * Get available models
   */
  getAvailableModels(): string[] {
    return [
      'claude-3-5-sonnet-20241022',
      'claude-3-5-haiku-20241022',
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307'
    ];
  }
}

/**
 * Factory function to create Anthropic client from API key
 * This requires the Anthropic SDK to be installed
 */
export function createAnthropicClient(apiKey: string): AnthropicClient {
  try {
    // Dynamic import to avoid requiring Anthropic SDK as dependency
    const Anthropic = require('@anthropic-ai/sdk');
    const anthropic = new Anthropic({ apiKey });
    return new AnthropicClient(anthropic);
  } catch (error) {
    throw new Error('Anthropic SDK not found. Please install it with: npm install @anthropic-ai/sdk');
  }
}
