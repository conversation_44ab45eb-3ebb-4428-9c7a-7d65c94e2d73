{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../../src/integrations/openai/client.ts"], "names": [], "mappings": ";AAAA,oCAAoC;;;AA+LpC,gDASC;AAhMD,2DAAqD;AA6BrD;;;GAGG;AACH,MAAa,YAAY;IAGvB,YAAY,cAAmB;QAC7B,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,QAAsB,EACtB,UAA8B,EAAE;QAEhC,MAAM,EACJ,KAAK,GAAG,OAAO,EACf,WAAW,GAAG,GAAG,EACjB,SAAS,GAAG,IAAI,EAChB,KAAK,EACN,GAAG,OAAO,CAAC;QAEZ,wCAAwC;QACxC,MAAM,cAAc,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACxC,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBACxB,OAAO;oBACL,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,YAAY,EAAE,GAAG,CAAC,YAAY;iBAC/B,CAAC;YACJ,CAAC;YACD,OAAO;gBACL,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAQ;YACzB,KAAK;YACL,QAAQ,EAAE,cAAc;YACxB,WAAW;YACX,UAAU,EAAE,SAAS;SACtB,CAAC;QAEF,wBAAwB;QACxB,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC;YAC5B,aAAa,CAAC,WAAW,GAAG,MAAM,CAAC;QACrC,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC1E,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAEnC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;YACjE,CAAC;YAED,wCAAwC;YACxC,MAAM,gBAAgB,GAAwB;gBAC5C,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;gBAC/B,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,CAAC;oBACvD,EAAE,EAAE,EAAE,CAAC,EAAE;oBACT,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE;wBACR,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;wBACtB,SAAS,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS;qBACjC;iBACF,CAAC,CAAC;aACJ,CAAC;YAEF,OAAO,gBAAgB,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CACb,UAA8B,EAAE;QAEhC,OAAO,KAAK,EAAE,KAAa,EAA4B,EAAE;YACvD,MAAM,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;YAElC,+BAA+B;YAC/B,IAAI,QAAQ,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;YAEnC,yDAAyD;YACzD,IAAI,aAAa,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC;gBAC9E,QAAQ,CAAC,OAAO,CAAC;oBACf,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,aAAa;iBACvB,CAAC,CAAC;YACL,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAC5E,OAAO,IAAA,0BAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAE7C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACnG,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,WAAW,GAAe;gBAC9B,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,mCAAmC;aAC7C,CAAC;YAEF,MAAM,IAAI,CAAC,oBAAoB,CAAC,CAAC,WAAW,CAAC,EAAE;gBAC7C,KAAK,EAAE,eAAe;gBACtB,SAAS,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAC/C,OAAO,MAAM,CAAC,IAAI;iBACf,MAAM,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;iBAChD,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,4BAA4B;QACjE,CAAC;IACH,CAAC;CACF;AAhJD,oCAgJC;AAED;;;GAGG;AACH,SAAgB,kBAAkB,CAAC,MAAc;IAC/C,IAAI,CAAC;QACH,6DAA6D;QAC7D,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QACtC,OAAO,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;IACtF,CAAC;AACH,CAAC"}