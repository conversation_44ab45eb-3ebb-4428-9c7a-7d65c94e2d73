{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../../src/integrations/openai/client.ts"], "names": [], "mappings": ";AAAA,oCAAoC;;;AAsbpC,gDASC;AAvbD,2DAAqD;AAyFrD;;;;;;;;;;;GAWG;AACH,MAAa,YAAY;IAIvB,YAAY,cAAmB,EAAE,SAA6B,EAAE;QAC9D,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,iDAAiD;QACjD,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;YAC/C,MAAM,IAAI,KAAK,CACb,4EAA4E,CAC7E,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,QAAsB,EACtB,UAA8B,EAAE;QAEhC,MAAM,EACJ,KAAK,GAAG,QAAQ,EAChB,WAAW,GAAG,GAAG,EACjB,SAAS,GAAG,IAAI,EAChB,KAAK,EACL,UAAU,GAAG,MAAM,EACnB,OAAO,EACP,cAAc,EACf,GAAG,OAAO,CAAC;QAEZ,wCAAwC;QACxC,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;QAEhF,MAAM,aAAa,GAAQ;YACzB,KAAK;YACL,QAAQ,EAAE,cAAc;YACxB,WAAW;YACX,UAAU,EAAE,SAAS;SACtB,CAAC;QAEF,wBAAwB;QACxB,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC;YAC5B,aAAa,CAAC,WAAW,GAAG,UAAU,CAAC;QACzC,CAAC;QAED,mCAAmC;QACnC,IAAI,cAAc,EAAE,CAAC;YACnB,aAAa,CAAC,eAAe,GAAG,cAAc,CAAC;QACjD,CAAC;QAED,oBAAoB;QACpB,MAAM,cAAc,GAAQ,EAAE,CAAC;QAC/B,IAAI,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACnC,cAAc,CAAC,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAyB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAC9E,aAAa,EACb,cAAc,CACf,CAAC;YAEF,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAE3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,QAAsB,EAAE,aAAsB;QACvE,MAAM,cAAc,GAAoB,EAAE,CAAC;QAE3C,iCAAiC;QACjC,IAAI,aAAa,EAAE,CAAC;YAClB,cAAc,CAAC,IAAI,CAAC;gBAClB,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,aAAa;aACvB,CAAC,CAAC;QACL,CAAC;QAED,uBAAuB;QACvB,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;YAC3B,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,aAAa,EAAE,CAAC;gBAC3C,+CAA+C;gBAC/C,SAAS;YACX,CAAC;YAED,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBACxB,cAAc,CAAC,IAAI,CAAC;oBAClB,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,YAAY,EAAE,GAAG,CAAC,YAAY;iBAC/B,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,MAAM,aAAa,GAAkB;oBACnC,IAAI,EAAE,GAAG,CAAC,IAAuC;oBACjD,OAAO,EAAE,GAAG,CAAC,OAAO;iBACrB,CAAC;gBAEF,0DAA0D;gBAC1D,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,IAAI,YAAY,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;oBACtE,aAAa,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,CAAC;wBAC1D,EAAE,EAAE,EAAE,CAAC,EAAE;wBACT,IAAI,EAAE,UAAmB;wBACzB,QAAQ,EAAE;4BACR,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;4BACtB,SAAS,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS;yBACjC;qBACF,CAAC,CAAC,CAAC;gBACN,CAAC;gBAED,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,QAA8B;QACvD,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEnC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,gBAAgB,GAAwB;YAC5C,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;SAChC,CAAC;QAEF,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YAC9B,gBAAgB,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACjE,EAAE,EAAE,EAAE,CAAC,EAAE;gBACT,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE;oBACR,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;oBACtB,SAAS,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS;iBACjC;aACF,CAAC,CAAC,CAAC;QACN,CAAC;QAED,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,KAAU;QAClC,IAAI,KAAK,EAAE,MAAM,EAAE,CAAC;YAClB,qCAAqC;YACrC,MAAM,WAAW,GAAG,KAAoB,CAAC;YACzC,OAAO,IAAI,KAAK,CACd,qBAAqB,WAAW,CAAC,MAAM,MAAM,WAAW,CAAC,OAAO,IAAI,eAAe,EAAE,CACtF,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YAC3B,OAAO,IAAI,KAAK,CAAC,iBAAiB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,IAAI,KAAK,CAAC,iBAAiB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC7B,QAAsB,EACtB,UAA8B,EAAE,EAChC,OAAiC;QAEjC,MAAM,EACJ,KAAK,GAAG,QAAQ,EAChB,WAAW,GAAG,GAAG,EACjB,SAAS,GAAG,IAAI,EAChB,KAAK,EACL,UAAU,GAAG,MAAM,EACnB,OAAO,EACR,GAAG,OAAO,CAAC;QAEZ,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;QAEhF,MAAM,aAAa,GAAQ;YACzB,KAAK;YACL,QAAQ,EAAE,cAAc;YACxB,WAAW;YACX,UAAU,EAAE,SAAS;YACrB,MAAM,EAAE,IAAI;SACb,CAAC;QAEF,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC;YAC5B,aAAa,CAAC,WAAW,GAAG,UAAU,CAAC;QACzC,CAAC;QAED,MAAM,cAAc,GAAQ,EAAE,CAAC;QAC/B,IAAI,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YACnC,cAAc,CAAC,OAAO,GAAG,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;YAExF,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,SAAS,GAAqB,EAAE,CAAC;YAErC,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;gBAEtC,IAAI,KAAK,EAAE,OAAO,EAAE,CAAC;oBACnB,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;oBACzB,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC3B,CAAC;gBAED,IAAI,KAAK,EAAE,UAAU,EAAE,CAAC;oBACtB,iCAAiC;oBACjC,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;wBACxC,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;4BACjC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gCAC/B,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG;oCAC1B,EAAE,EAAE,QAAQ,CAAC,EAAE,IAAI,EAAE;oCACrB,IAAI,EAAE,UAAU;oCAChB,QAAQ,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE;iCACtC,CAAC;4BACJ,CAAC;4BAED,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;gCAC5B,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;4BACpE,CAAC;4BACD,IAAI,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC;gCACjC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;4BAC9E,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YAED,MAAM,gBAAgB,GAAwB;gBAC5C,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,OAAO,IAAI,IAAI;aACzB,CAAC;YAEF,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,gBAAgB,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACjD,EAAE,EAAE,EAAE,CAAC,EAAE;oBACT,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE;wBACR,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;wBACtB,SAAS,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS;qBACjC;iBACF,CAAC,CAAC,CAAC;YACN,CAAC;YAED,OAAO,gBAAgB,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CACb,UAA8B,EAAE;QAEhC,OAAO,KAAK,EAAE,KAAa,EAA4B,EAAE;YACvD,IAAI,CAAC;gBACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAClF,OAAO,IAAA,0BAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAE7C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACnG,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,WAAW,GAAe;gBAC9B,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,mCAAmC;aAC7C,CAAC;YAEF,MAAM,IAAI,CAAC,oBAAoB,CAAC,CAAC,WAAW,CAAC,EAAE;gBAC7C,KAAK,EAAE,eAAe;gBACtB,SAAS,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAC/C,OAAO,MAAM,CAAC,IAAI;iBACf,MAAM,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;iBAChD,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC,4BAA4B;QACjE,CAAC;IACH,CAAC;CACF;AAnUD,oCAmUC;AAED;;;GAGG;AACH,SAAgB,kBAAkB,CAAC,MAAc;IAC/C,IAAI,CAAC;QACH,6DAA6D;QAC7D,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QACjC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QACtC,OAAO,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;IACtF,CAAC;AACH,CAAC"}