import { describe, test, expect, beforeEach } from '@jest/globals';
import { z } from 'zod';
import { 
  Agent, 
  Tool, 
  Memory, 
  createTestState, 
  assertAgentResult, 
  assertPerformance,
  globalPerformanceMeasurer,
  testData,
  testConfig
} from '../setup.js';

/**
 * Test Suite 3: Analyst Agent
 * 
 * Tests the analyst agent functionality including:
 * - Data analysis and pattern recognition
 * - Statistical computations and insights
 * - Report generation and visualization
 * - Trend analysis and forecasting
 * - Data validation and quality assessment
 */

describe('Analyst Agent Tests', () => {
  let analystAgent: Agent;
  let analysisTools: Tool[];
  let memory: Memory;

  beforeEach(() => {
    // Create specialized analysis tools
    analysisTools = [
      new Tool(
        {
          name: 'data_processor',
          description: 'Process and analyze datasets',
          parameters: z.object({
            data: z.array(z.number()).describe('Numerical data to analyze'),
            analysisType: z.enum(['descriptive', 'correlation', 'trend', 'outlier']),
          }),
        },
        async ({ data, analysisType }) => {
          const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
          const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
          const stdDev = Math.sqrt(variance);
          const min = Math.min(...data);
          const max = Math.max(...data);
          
          switch (analysisType) {
            case 'descriptive':
              return `Descriptive Statistics:\nMean: ${mean.toFixed(2)}\nStd Dev: ${stdDev.toFixed(2)}\nMin: ${min}\nMax: ${max}\nCount: ${data.length}`;
            case 'correlation':
              return `Correlation Analysis:\nData shows ${stdDev > mean * 0.3 ? 'high' : 'low'} variability\nRange: ${max - min}`;
            case 'trend':
              const trend = data[data.length - 1] > data[0] ? 'increasing' : 'decreasing';
              return `Trend Analysis:\nOverall trend: ${trend}\nChange: ${((data[data.length - 1] - data[0]) / data[0] * 100).toFixed(2)}%`;
            case 'outlier':
              const outliers = data.filter(val => Math.abs(val - mean) > 2 * stdDev);
              return `Outlier Detection:\nOutliers found: ${outliers.length}\nValues: [${outliers.join(', ')}]`;
            default:
              return 'Analysis completed';
          }
        }
      ),
      new Tool(
        {
          name: 'statistical_calculator',
          description: 'Perform statistical calculations',
          parameters: z.object({
            operation: z.enum(['mean', 'median', 'mode', 'percentile', 'zscore']),
            values: z.array(z.number()),
            parameter: z.number().optional().describe('Additional parameter for percentile or zscore'),
          }),
        },
        async ({ operation, values, parameter }) => {
          const sorted = [...values].sort((a, b) => a - b);
          
          switch (operation) {
            case 'mean':
              const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
              return `Mean: ${mean.toFixed(4)}`;
            case 'median':
              const mid = Math.floor(sorted.length / 2);
              const median = sorted.length % 2 === 0 
                ? (sorted[mid - 1] + sorted[mid]) / 2 
                : sorted[mid];
              return `Median: ${median}`;
            case 'mode':
              const frequency = new Map();
              values.forEach(val => frequency.set(val, (frequency.get(val) || 0) + 1));
              const maxFreq = Math.max(...frequency.values());
              const modes = Array.from(frequency.entries())
                .filter(([_, freq]) => freq === maxFreq)
                .map(([val, _]) => val);
              return `Mode: [${modes.join(', ')}] (frequency: ${maxFreq})`;
            case 'percentile':
              if (parameter === undefined) return 'Percentile requires parameter';
              const index = (parameter / 100) * (sorted.length - 1);
              const percentile = sorted[Math.round(index)];
              return `${parameter}th Percentile: ${percentile}`;
            case 'zscore':
              if (parameter === undefined) return 'Z-score requires parameter';
              const mean_z = values.reduce((sum, val) => sum + val, 0) / values.length;
              const variance_z = values.reduce((sum, val) => sum + Math.pow(val - mean_z, 2), 0) / values.length;
              const stdDev_z = Math.sqrt(variance_z);
              const zscore = (parameter - mean_z) / stdDev_z;
              return `Z-score for ${parameter}: ${zscore.toFixed(4)}`;
            default:
              return 'Unknown operation';
          }
        }
      ),
      new Tool(
        {
          name: 'pattern_detector',
          description: 'Detect patterns and anomalies in data',
          parameters: z.object({
            timeSeries: z.array(z.number()).describe('Time series data'),
            patternType: z.enum(['seasonal', 'cyclical', 'linear', 'exponential']),
          }),
        },
        async ({ timeSeries, patternType }) => {
          const length = timeSeries.length;
          
          switch (patternType) {
            case 'seasonal':
              // Simple seasonal detection
              const period = Math.floor(length / 4);
              const seasonality = period > 0 ? 'detected' : 'not detected';
              return `Seasonal Pattern: ${seasonality} (estimated period: ${period})`;
            case 'cyclical':
              const peaks = timeSeries.filter((val, i) => 
                i > 0 && i < length - 1 && 
                val > timeSeries[i - 1] && val > timeSeries[i + 1]
              ).length;
              return `Cyclical Pattern: ${peaks} peaks detected`;
            case 'linear':
              const firstHalf = timeSeries.slice(0, Math.floor(length / 2));
              const secondHalf = timeSeries.slice(Math.floor(length / 2));
              const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
              const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
              const trend = secondAvg > firstAvg ? 'increasing' : 'decreasing';
              return `Linear Trend: ${trend} (${((secondAvg - firstAvg) / firstAvg * 100).toFixed(2)}% change)`;
            case 'exponential':
              const growth = timeSeries[length - 1] / timeSeries[0];
              const isExponential = growth > 2 && timeSeries.every((val, i) => i === 0 || val >= timeSeries[i - 1]);
              return `Exponential Growth: ${isExponential ? 'detected' : 'not detected'} (growth factor: ${growth.toFixed(2)})`;
            default:
              return 'Pattern analysis completed';
          }
        }
      ),
      new Tool(
        {
          name: 'report_generator',
          description: 'Generate analytical reports',
          parameters: z.object({
            title: z.string(),
            sections: z.array(z.string()),
            data: z.record(z.any()).optional(),
            format: z.enum(['summary', 'detailed', 'executive']).default('summary'),
          }),
        },
        async ({ title, sections, data, format }) => {
          const timestamp = new Date().toISOString().split('T')[0];
          
          let report = `# ${title}\n\nGenerated: ${timestamp}\nFormat: ${format}\n\n`;
          
          sections.forEach((section, index) => {
            report += `## ${index + 1}. ${section}\n\n`;
            
            if (format === 'detailed') {
              report += `Detailed analysis for ${section}:\n`;
              report += `- Key findings and insights\n`;
              report += `- Supporting data and evidence\n`;
              report += `- Recommendations and next steps\n\n`;
            } else if (format === 'executive') {
              report += `Executive summary for ${section}:\n`;
              report += `- High-level overview\n`;
              report += `- Strategic implications\n\n`;
            } else {
              report += `Summary of ${section} analysis.\n\n`;
            }
          });
          
          if (data) {
            report += `## Data Summary\n\n`;
            Object.entries(data).forEach(([key, value]) => {
              report += `- ${key}: ${value}\n`;
            });
          }
          
          return report;
        }
      ),
      new Tool(
        {
          name: 'forecast_engine',
          description: 'Generate forecasts and predictions',
          parameters: z.object({
            historicalData: z.array(z.number()),
            periods: z.number().describe('Number of periods to forecast'),
            method: z.enum(['linear', 'exponential', 'moving_average']).default('linear'),
          }),
        },
        async ({ historicalData, periods, method }) => {
          const length = historicalData.length;
          let forecast: number[] = [];
          
          switch (method) {
            case 'linear':
              // Simple linear regression
              const sumX = Array.from({ length }, (_, i) => i).reduce((sum, val) => sum + val, 0);
              const sumY = historicalData.reduce((sum, val) => sum + val, 0);
              const sumXY = historicalData.reduce((sum, val, i) => sum + val * i, 0);
              const sumX2 = Array.from({ length }, (_, i) => i * i).reduce((sum, val) => sum + val, 0);
              
              const slope = (length * sumXY - sumX * sumY) / (length * sumX2 - sumX * sumX);
              const intercept = (sumY - slope * sumX) / length;
              
              forecast = Array.from({ length: periods }, (_, i) => 
                slope * (length + i) + intercept
              );
              break;
              
            case 'exponential':
              const growthRate = historicalData[length - 1] / historicalData[0];
              const avgGrowth = Math.pow(growthRate, 1 / (length - 1));
              
              forecast = Array.from({ length: periods }, (_, i) => 
                historicalData[length - 1] * Math.pow(avgGrowth, i + 1)
              );
              break;
              
            case 'moving_average':
              const windowSize = Math.min(3, length);
              const lastValues = historicalData.slice(-windowSize);
              const average = lastValues.reduce((sum, val) => sum + val, 0) / windowSize;
              
              forecast = Array.from({ length: periods }, () => average);
              break;
          }
          
          return `Forecast (${method} method):\n${forecast.map((val, i) => 
            `Period ${i + 1}: ${val.toFixed(2)}`
          ).join('\n')}`;
        }
      )
    ];

    memory = new Memory({
      type: 'vector',
      maxMessages: 25,
    });

    analystAgent = new Agent({
      name: 'Data Analyst',
      instructions: `You are a data analysis expert. Your role is to:
        1. Process and analyze numerical data to extract meaningful insights
        2. Perform statistical calculations and identify patterns
        3. Generate comprehensive analytical reports
        4. Create forecasts and predictions based on historical data
        5. Detect anomalies and outliers in datasets
        6. Provide data-driven recommendations
        
        Always use appropriate analytical tools and provide detailed explanations of your findings.`,
      role: 'analyst',
      tools: analysisTools,
      memory,
      maxIterations: 6,
      enableSelfReflection: true,
    });
  });

  test('should create analyst agent with correct configuration', () => {
    expect(analystAgent.name).toBe('Data Analyst');
    expect(analystAgent.role).toBe('analyst');
    expect(analystAgent.tools).toHaveLength(5);
    expect(analystAgent.tools.map(t => t.name)).toEqual([
      'data_processor', 'statistical_calculator', 'pattern_detector', 'report_generator', 'forecast_engine'
    ]);
  });

  test('should perform descriptive statistical analysis', async () => {
    globalPerformanceMeasurer.start();
    
    const result = await analystAgent.run(
      'Analyze this sales data and provide descriptive statistics: [100, 120, 95, 140, 110, 130, 105, 125, 115, 135]'
    );
    
    const duration = globalPerformanceMeasurer.measure('descriptive_analysis');
    
    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 2);
    
    expect(result.metrics.toolCalls).toBeGreaterThan(0);
    expect(result.finalOutput.toLowerCase()).toContain('mean');
    expect(result.finalOutput.toLowerCase()).toContain('statistics');
  });

  test('should detect patterns in time series data', async () => {
    const result = await analystAgent.run(
      'Analyze this time series data for patterns: [10, 15, 12, 18, 14, 20, 16, 22, 18, 25, 20, 28]. Look for trends and seasonal patterns.'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput.toLowerCase()).toContain('pattern');
    expect(result.finalOutput.toLowerCase()).toContain('trend');
    expect(result.metrics.toolCalls).toBeGreaterThan(0);
  });

  test('should generate comprehensive analytical reports', async () => {
    const result = await analystAgent.run(`
      Generate a detailed analytical report for Q3 performance with the following sections:
      - Revenue Analysis
      - Customer Growth
      - Market Trends
      - Competitive Analysis
      - Recommendations
      
      Use executive format for stakeholder presentation.
    `);
    
    assertAgentResult(result);
    
    expect(result.finalOutput).toContain('Revenue Analysis');
    expect(result.finalOutput).toContain('Recommendations');
    expect(result.finalOutput.toLowerCase()).toContain('executive');
    expect(result.finalOutput.length).toBeGreaterThan(500);
  });

  test('should create accurate forecasts', async () => {
    const result = await analystAgent.run(
      'Based on this historical revenue data [1000, 1100, 1200, 1350, 1500, 1650], create a 3-period forecast using linear regression'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput.toLowerCase()).toContain('forecast');
    expect(result.finalOutput.toLowerCase()).toContain('period');
    expect(result.finalOutput).toMatch(/\d+\.\d+/); // Should contain decimal numbers
  });

  test('should identify outliers and anomalies', async () => {
    const result = await analystAgent.run(
      'Analyze this dataset for outliers: [50, 52, 48, 51, 49, 200, 53, 47, 50, 51]. Identify any anomalous values.'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput.toLowerCase()).toContain('outlier');
    expect(result.finalOutput).toContain('200'); // Should identify the obvious outlier
  });

  test('should perform correlation analysis', async () => {
    const result = await analystAgent.run(
      'Analyze the correlation and variability in this dataset: [10, 25, 15, 30, 20, 35, 25, 40]. Provide insights about the data distribution.'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput.toLowerCase()).toContain('correlation');
    expect(result.finalOutput.toLowerCase()).toContain('variability');
  });

  test('should calculate statistical measures accurately', async () => {
    globalPerformanceMeasurer.start();
    
    const result = await analystAgent.run(
      'Calculate the mean, median, and mode for this dataset: [1, 2, 2, 3, 4, 4, 4, 5, 6]. Also find the 75th percentile.'
    );
    
    const duration = globalPerformanceMeasurer.measure('statistical_calculations');
    
    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime);
    
    expect(result.finalOutput.toLowerCase()).toContain('mean');
    expect(result.finalOutput.toLowerCase()).toContain('median');
    expect(result.finalOutput.toLowerCase()).toContain('mode');
    expect(result.finalOutput.toLowerCase()).toContain('percentile');
  });

  test('should handle multiple analysis types in sequence', async () => {
    const result = await analystAgent.run(`
      Perform a comprehensive analysis of this quarterly sales data: [850, 920, 1100, 1250, 1180, 1350, 1420, 1600, 1550, 1700, 1800, 1950].
      
      Please:
      1. Calculate descriptive statistics
      2. Identify any trends or patterns
      3. Detect outliers
      4. Generate a 2-period forecast
      5. Create a summary report
    `);
    
    assertAgentResult(result);
    
    expect(result.metrics.toolCalls).toBeGreaterThanOrEqual(4);
    expect(result.finalOutput.toLowerCase()).toContain('statistics');
    expect(result.finalOutput.toLowerCase()).toContain('trend');
    expect(result.finalOutput.toLowerCase()).toContain('forecast');
  });

  test('should provide data-driven insights and recommendations', async () => {
    const result = await analystAgent.run(
      'Analyze this customer satisfaction data over 6 months [3.2, 3.4, 3.8, 4.1, 4.3, 4.5] and provide actionable insights and recommendations for improvement'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput.toLowerCase()).toContain('insight');
    expect(result.finalOutput.toLowerCase()).toContain('recommendation');
    expect(result.finalOutput.toLowerCase()).toContain('improvement');
  });

  test('should handle complex multi-dimensional analysis', async () => {
    globalPerformanceMeasurer.start();
    
    const result = await analystAgent.run(`
      Analyze the performance of our marketing campaigns:
      
      Campaign A results: [120, 135, 150, 165, 180]
      Campaign B results: [100, 110, 125, 140, 160]
      Campaign C results: [80, 95, 110, 130, 155]
      
      Compare their performance, identify the best performing campaign, and forecast next period results for each.
    `);
    
    const duration = globalPerformanceMeasurer.measure('multi_dimensional_analysis');
    
    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 3);
    
    expect(result.finalOutput.toLowerCase()).toContain('campaign');
    expect(result.finalOutput.toLowerCase()).toContain('performance');
    expect(result.finalOutput.toLowerCase()).toContain('forecast');
    expect(result.metrics.toolCalls).toBeGreaterThan(2);
  });

  test('should maintain analytical context across conversations', async () => {
    // First analysis
    const analysis1 = await analystAgent.run(
      'Analyze this baseline data: [100, 105, 110, 115, 120]. Calculate basic statistics.'
    );
    
    assertAgentResult(analysis1);
    
    // Follow-up analysis referencing previous work
    const analysis2 = await analystAgent.run(
      'Now compare this new data [125, 130, 135, 140, 145] with the baseline we analyzed earlier. What insights can you provide?'
    );
    
    assertAgentResult(analysis2);
    
    expect(analysis2.finalOutput.toLowerCase()).toContain('baseline');
    expect(analysis2.finalOutput.toLowerCase()).toContain('compare');
    expect(analysis2.finalState.messages.length).toBeGreaterThan(analysis1.finalState.messages.length);
  });

  test('should handle edge cases and invalid data gracefully', async () => {
    const result = await analystAgent.run(
      'Analyze this dataset that might have issues: []. Also try to analyze this single value: [42].'
    );
    
    assertAgentResult(result);
    
    // Should handle empty arrays and single values without crashing
    expect(result.finalOutput).toBeDefined();
    expect(result.finalOutput.length).toBeGreaterThan(0);
  });

  test('should provide confidence levels and uncertainty measures', async () => {
    const result = await analystAgent.run(
      'Create a forecast for this volatile data [10, 25, 15, 30, 12, 35, 18, 40] and discuss the confidence level and uncertainty in your predictions'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput.toLowerCase()).toContain('confidence');
    expect(result.finalOutput.toLowerCase()).toContain('uncertainty');
    expect(result.finalOutput.toLowerCase()).toContain('volatile');
  });
});
