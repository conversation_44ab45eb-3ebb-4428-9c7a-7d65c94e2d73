"use strict";
// src/integrations/mcp/client.ts
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPClient = void 0;
exports.createMCPClient = createMCPClient;
exports.createMCPClientWithFallback = createMCPClientWithFallback;
/**
 * Check if MCP SDK is available
 */
async function checkMCPSDK() {
    try {
        await Promise.resolve().then(() => __importStar(require('@modelcontextprotocol/sdk/client/index.js')));
    }
    catch (error) {
        throw new Error('MCP SDK not found. Install with: npm install @modelcontextprotocol/sdk\n' +
            'Or use AG3NTIC without MCP integration.');
    }
}
/**
 * MCP Client for connecting AG3NTIC agents to external MCP servers
 *
 * This allows AG3NTIC agents to:
 * - Connect to any MCP-compliant server
 * - Access external tools, resources, and prompts
 * - Use standardized MCP protocol for communication
 */
class MCPClient {
    constructor(config) {
        this.connected = false;
        this.requestId = 1;
        this.config = {
            timeout: 30000,
            maxRetries: 3,
            transport: 'http',
            ...config
        };
    }
    /**
     * Connect to an MCP server
     */
    async connect() {
        try {
            // Check if MCP SDK is available
            await checkMCPSDK();
            // Dynamic import to avoid requiring MCP SDK if not used
            const { Client } = await Promise.resolve().then(() => __importStar(require('@modelcontextprotocol/sdk/client/index.js')));
            let transport;
            if (this.config.transport === 'stdio') {
                const { StdioClientTransport } = await Promise.resolve().then(() => __importStar(require('@modelcontextprotocol/sdk/client/stdio.js')));
                transport = new StdioClientTransport({
                    command: this.config.serverUrl || 'node',
                    args: []
                });
            }
            else if (this.config.transport === 'http') {
                const { StreamableHTTPClientTransport } = await Promise.resolve().then(() => __importStar(require('@modelcontextprotocol/sdk/client/streamableHttp.js')));
                transport = new StreamableHTTPClientTransport(new URL(this.config.serverUrl || 'http://localhost:3000/mcp'));
            }
            else if (this.config.transport === 'sse') {
                const { SSEClientTransport } = await Promise.resolve().then(() => __importStar(require('@modelcontextprotocol/sdk/client/sse.js')));
                transport = new SSEClientTransport(new URL(this.config.serverUrl || 'http://localhost:3000/sse'));
            }
            else {
                throw new Error(`Unsupported transport: ${this.config.transport}`);
            }
            this.client = new Client({
                name: this.config.name,
                version: this.config.version
            });
            await this.client.connect(transport);
            this.connected = true;
            console.log(`🔗 MCP Client connected to ${this.config.serverUrl} via ${this.config.transport}`);
        }
        catch (error) {
            console.error('❌ Failed to connect to MCP server:', error);
            throw error;
        }
    }
    /**
     * Disconnect from the MCP server
     */
    async disconnect() {
        if (this.client && this.connected) {
            await this.client.close();
            this.connected = false;
            console.log('🔌 MCP Client disconnected');
        }
    }
    /**
     * Check if client is connected
     */
    isConnected() {
        return this.connected;
    }
    /**
     * List available tools from the MCP server
     */
    async listTools() {
        this.ensureConnected();
        try {
            const response = await this.client.listTools();
            return response.tools.map((tool) => ({
                name: tool.name,
                description: tool.description,
                inputSchema: tool.inputSchema
            }));
        }
        catch (error) {
            console.error('❌ Failed to list tools:', error);
            throw error;
        }
    }
    /**
     * Call a tool on the MCP server
     */
    async callTool(name, arguments_) {
        this.ensureConnected();
        try {
            const response = await this.client.callTool({
                name,
                arguments: arguments_
            });
            return {
                content: response.content || [],
                isError: response.isError || false
            };
        }
        catch (error) {
            console.error(`❌ Failed to call tool ${name}:`, error);
            throw error;
        }
    }
    /**
     * List available resources from the MCP server
     */
    async listResources() {
        this.ensureConnected();
        try {
            const response = await this.client.listResources();
            return response.resources.map((resource) => ({
                uri: resource.uri,
                name: resource.name,
                description: resource.description,
                mimeType: resource.mimeType
            }));
        }
        catch (error) {
            console.error('❌ Failed to list resources:', error);
            throw error;
        }
    }
    /**
     * Read a resource from the MCP server
     */
    async readResource(uri) {
        this.ensureConnected();
        try {
            const response = await this.client.readResource({ uri });
            return {
                contents: response.contents || []
            };
        }
        catch (error) {
            console.error(`❌ Failed to read resource ${uri}:`, error);
            throw error;
        }
    }
    /**
     * List available prompts from the MCP server
     */
    async listPrompts() {
        this.ensureConnected();
        try {
            const response = await this.client.listPrompts();
            return response.prompts.map((prompt) => ({
                name: prompt.name,
                description: prompt.description,
                arguments: prompt.arguments
            }));
        }
        catch (error) {
            console.error('❌ Failed to list prompts:', error);
            throw error;
        }
    }
    /**
     * Get a prompt from the MCP server
     */
    async getPrompt(name, arguments_) {
        this.ensureConnected();
        try {
            const response = await this.client.getPrompt({
                name,
                arguments: arguments_ || {}
            });
            return {
                description: response.description,
                messages: response.messages || []
            };
        }
        catch (error) {
            console.error(`❌ Failed to get prompt ${name}:`, error);
            throw error;
        }
    }
    /**
     * Send a raw MCP request
     */
    async sendRequest(method, params) {
        this.ensureConnected();
        try {
            const request = {
                jsonrpc: "2.0",
                id: this.requestId++,
                method,
                params
            };
            // Use the underlying client's request method
            return await this.client.request(request);
        }
        catch (error) {
            console.error(`❌ Failed to send request ${method}:`, error);
            throw error;
        }
    }
    /**
     * Create an MCP-compatible tool function for use with AG3NTIC agents
     */
    createToolFunction(toolName) {
        return async (args) => {
            const result = await this.callTool(toolName, args);
            // Convert MCP result to simple string for AG3NTIC compatibility
            if (result.content && result.content.length > 0) {
                const textContent = result.content
                    .filter(c => c.type === 'text')
                    .map(c => c.text)
                    .join('\n');
                return textContent || JSON.stringify(result.content);
            }
            return JSON.stringify(result);
        };
    }
    /**
     * Get all tools as AG3NTIC-compatible tool functions
     */
    async getToolFunctions() {
        const tools = await this.listTools();
        const toolFunctions = {};
        for (const tool of tools) {
            toolFunctions[tool.name] = this.createToolFunction(tool.name);
        }
        return toolFunctions;
    }
    /**
     * Ensure the client is connected
     */
    ensureConnected() {
        if (!this.connected) {
            throw new Error('MCP Client is not connected. Call connect() first.');
        }
    }
}
exports.MCPClient = MCPClient;
/**
 * Create an MCP client with automatic connection
 */
async function createMCPClient(config) {
    const client = new MCPClient(config);
    await client.connect();
    return client;
}
/**
 * Create MCP client with backwards compatibility fallback
 */
async function createMCPClientWithFallback(httpUrl, sseUrl, config) {
    const baseConfig = {
        name: 'ag3ntic-client',
        version: '1.0.0',
        ...config
    };
    // Try Streamable HTTP first
    try {
        const client = new MCPClient({
            ...baseConfig,
            serverUrl: httpUrl,
            transport: 'http'
        });
        await client.connect();
        console.log('✅ Connected using Streamable HTTP transport');
        return client;
    }
    catch (error) {
        console.log('⚠️  Streamable HTTP failed, trying SSE fallback...');
        // Fall back to SSE if provided
        if (sseUrl) {
            const client = new MCPClient({
                ...baseConfig,
                serverUrl: sseUrl,
                transport: 'sse'
            });
            await client.connect();
            console.log('✅ Connected using SSE transport');
            return client;
        }
        throw new Error('Failed to connect with both HTTP and SSE transports');
    }
}
//# sourceMappingURL=client.js.map