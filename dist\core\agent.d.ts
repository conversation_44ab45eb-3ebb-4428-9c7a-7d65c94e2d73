import { AgentState } from './types.js';
import { Tool } from './tools.js';
import { Memory } from './memory.js';
/**
 * Agent role types for specialized behaviors
 */
export type AgentRole = 'executor' | 'planner' | 'orchestrator' | 'analyst' | 'critic';
/**
 * Agent configuration interface
 */
export interface AgentConfig<TState extends AgentState = AgentState> {
    name: string;
    instructions: string;
    role?: AgentRole;
    tools?: Tool[];
    handoffs?: Agent[];
    memory?: Memory<TState>;
    modelSettings?: {
        model?: string;
        temperature?: number;
        tool_choice?: 'auto' | 'required' | 'none' | string;
    };
    maxIterations?: number;
    enableSelfReflection?: boolean;
}
/**
 * ReAct loop step types
 */
export type ReActStep = 'thought' | 'action' | 'observation' | 'reflection';
/**
 * Agent execution result
 */
export interface AgentResult<TState extends AgentState = AgentState> {
    finalOutput: string;
    finalState: TState;
    finalAgent?: Agent;
    steps: Array<{
        step: ReActStep;
        content: string;
        timestamp: number;
    }>;
    metrics: {
        totalSteps: number;
        toolCalls: number;
        executionTime: number;
        tokensUsed?: number;
    };
}
/**
 * Ultra-high-performance Agent implementation
 *
 * Features:
 * - ReAct (Thought > Action > Observation) loop
 * - Agent handoffs and orchestration
 * - Self-reflection and critique
 * - Role-based specialization
 * - Memory integration
 * - Performance optimizations
 */
export declare class Agent<TState extends AgentState = AgentState> {
    private readonly config;
    private readonly toolMap;
    private readonly handoffMap;
    constructor(config: AgentConfig<TState>);
    /**
     * Create agent with type safety for handoffs
     */
    static create<TState extends AgentState = AgentState>(config: AgentConfig<TState>): Agent<TState>;
    /**
     * Convert agent to tool for use by other agents
     */
    asTool(): Tool;
    /**
     * Execute ReAct loop with performance optimizations
     */
    run(input: string | TState, context?: Record<string, any>): Promise<AgentResult<TState>>;
    /**
     * Generate thought step with role-specific reasoning
     */
    private generateThought;
    /**
     * Check if thought indicates handoff to another agent
     */
    private checkForHandoff;
    /**
     * Execute action step (tool usage)
     */
    private executeAction;
    /**
     * Process observation from tool execution
     */
    private processObservation;
    /**
     * Generate reflection step for self-critique
     */
    private generateReflection;
    /**
     * Check if agent has completed its task
     */
    private isComplete;
    /**
     * Extract final output from state
     */
    private extractFinalOutput;
    get name(): string;
    get role(): AgentRole;
    get tools(): Tool[];
    get handoffs(): Agent[];
}
/**
 * Utility function to run agent with simplified interface
 */
export declare function run<TState extends AgentState = AgentState>(agent: Agent<TState>, input: string | TState, context?: Record<string, any>): Promise<AgentResult<TState>>;
//# sourceMappingURL=agent.d.ts.map