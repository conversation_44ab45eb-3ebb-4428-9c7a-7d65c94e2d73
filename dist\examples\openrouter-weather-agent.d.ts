import { Graph, AgentState } from '../core';
import { OpenRouterClient } from '../integrations/openrouter';
interface WeatherState extends AgentState {
    topic: string;
}
declare const buildWeatherGraph: (openRouterClient: OpenRouterClient) => Graph<WeatherState>;
declare const runOpenRouterWeatherDemo: () => Promise<void>;
export { runOpenRouterWeatherDemo, buildWeatherGraph, WeatherState };
//# sourceMappingURL=openrouter-weather-agent.d.ts.map