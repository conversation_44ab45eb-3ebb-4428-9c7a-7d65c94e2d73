/**
 * Base JSON-RPC 2.0 message structure
 */
export interface MCPBaseMessage {
    jsonrpc: "2.0";
}
/**
 * JSON-RPC 2.0 Request message
 */
export interface MCPRequest extends MCPBaseMessage {
    id: string | number;
    method: string;
    params?: {
        [key: string]: unknown;
    } | undefined;
}
/**
 * JSON-RPC 2.0 Response message
 */
export interface MCPResponse extends MCPBaseMessage {
    id: string | number;
    result?: {
        [key: string]: unknown;
    };
    error?: {
        code: number;
        message: string;
        data?: unknown;
    };
}
/**
 * JSON-RPC 2.0 Notification message (no response expected)
 */
export interface MCPNotification extends MCPBaseMessage {
    method: string;
    params?: {
        [key: string]: unknown;
    } | undefined;
}
/**
 * Union type for all MCP protocol messages
 */
export type MCPProtocolMessage = MCPRequest | MCPResponse | MCPNotification;
export interface MCPTextContent {
    type: "text";
    text: string;
}
export interface MCPImageContent {
    type: "image";
    data: string;
    mimeType: string;
}
export interface MCPResourceContent {
    type: "resource";
    resource: {
        uri: string;
        text?: string;
        blob?: string;
        mimeType?: string;
    };
}
export type MCPContent = MCPTextContent | MCPImageContent | MCPResourceContent;
export interface MCPTool {
    name: string;
    description?: string;
    inputSchema: {
        type: "object";
        properties?: {
            [key: string]: unknown;
        };
        required?: string[];
    };
}
export interface MCPToolResult {
    content: MCPContent[];
    isError?: boolean;
}
export interface MCPResource {
    uri: string;
    name: string;
    description?: string;
    mimeType?: string;
}
export interface MCPResourceContents {
    contents: Array<{
        uri: string;
        mimeType?: string;
        text?: string;
        blob?: string;
    }>;
}
export interface MCPPrompt {
    name: string;
    description?: string;
    arguments?: Array<{
        name: string;
        description?: string;
        required?: boolean;
    }>;
}
export interface MCPPromptMessage {
    role: "user" | "assistant" | "system";
    content: MCPContent;
}
export interface MCPPromptResult {
    description?: string;
    messages: MCPPromptMessage[];
}
export interface MCPToolCallFunction {
    name: string;
    arguments: string;
}
export interface MCPToolCall {
    id: string;
    type: 'function';
    function: MCPToolCallFunction;
}
export interface MCPSystemMessage {
    role: 'system';
    content: string;
}
export interface MCPUserMessage {
    role: 'user';
    content: string;
}
export interface MCPAssistantMessage {
    role: 'assistant';
    content: string | null;
    tool_calls?: MCPToolCall[];
}
export interface MCPToolMessage {
    role: 'tool';
    tool_call_id: string;
    content: string;
}
export type MCPMessage = MCPSystemMessage | MCPUserMessage | MCPAssistantMessage | MCPToolMessage;
/**
 * Optimized base agent state for maximum performance
 */
export interface BaseAgentState {
    readonly messages: MCPMessage[];
    [key: string]: any;
}
export type AgentState = BaseAgentState;
/**
 * High-performance node function type
 * Returns partial state for efficient merging
 */
export type NodeFunction<TState extends AgentState> = (state: TState) => Promise<Partial<TState>>;
/**
 * Graph termination constant
 */
export declare const END: "__END__";
/**
 * Fast conditional function type
 */
export type ConditionalFunction<TState extends AgentState> = (state: TState) => string;
/**
 * Optimized edge target mapping
 */
export type EdgeTargetMap = Record<string, string>;
//# sourceMappingURL=types.d.ts.map