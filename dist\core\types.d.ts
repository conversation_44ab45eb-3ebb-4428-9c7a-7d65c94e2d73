export interface MCPToolCallFunction {
    name: string;
    arguments: string;
}
export interface MCPToolCall {
    id: string;
    type: 'function';
    function: MCPToolCallFunction;
}
export interface MCPSystemMessage {
    role: 'system';
    content: string;
}
export interface MCPUserMessage {
    role: 'user';
    content: string;
}
export interface MCPAssistantMessage {
    role: 'assistant';
    content: string | null;
    tool_calls?: MCPToolCall[];
}
export interface MCPToolMessage {
    role: 'tool';
    tool_call_id: string;
    content: string;
}
export type MCPMessage = MCPSystemMessage | MCPUserMessage | MCPAssistantMessage | MCPToolMessage;
export interface BaseAgentState {
    messages: MCPMessage[];
    [key: string]: any;
}
export type AgentState = BaseAgentState;
export type NodeFunction<TState extends AgentState> = (state: TState) => Promise<Partial<TState>>;
export declare const END = "__END__";
export interface ToolDefinition {
    name: string;
    description: string;
    parameters: {
        type: 'object';
        properties: Record<string, any>;
        required?: string[];
    };
}
export type ToolMap = Record<string, (...args: any[]) => any>;
export interface CreateAgentNodeOptions {
    tools?: ToolDefinition[];
    model?: string;
    systemMessage?: string;
    temperature?: number;
}
export type ConditionalFunction<TState extends AgentState> = (state: TState) => string;
export type EdgeTargetMap = Record<string, string>;
//# sourceMappingURL=types.d.ts.map