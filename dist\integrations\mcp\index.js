"use strict";
// src/integrations/mcp/index.ts
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMCPHTTPServer = exports.createMCPServer = exports.MCPServer = exports.createMCPClientWithFallback = exports.createMCPClient = exports.MCPClient = void 0;
exports.isMCPAvailable = isMCPAvailable;
exports.getMCPVersion = getMCPVersion;
exports.createMCPCompatibleTool = createMCPCompatibleTool;
exports.convertAG3NTICToolsToMCP = convertAG3NTICToolsToMCP;
exports.createMCPEnabledAgent = createMCPEnabledAgent;
exports.getMCPIntegrationStatus = getMCPIntegrationStatus;
/**
 * Model Context Protocol (MCP) Integration for AG3NTIC
 *
 * This module provides comprehensive MCP integration, allowing AG3NTIC to:
 *
 * 1. **Act as an MCP Client**: Connect to external MCP servers to access tools, resources, and prompts
 * 2. **Act as an MCP Server**: Expose AG3NTIC capabilities via the standardized MCP protocol
 * 3. **Seamless Integration**: Work with the entire MCP ecosystem while maintaining AG3NTIC's simplicity
 *
 * Key Features:
 * - Full JSON-RPC 2.0 compliance
 * - Support for Streamable HTTP and SSE transports
 * - Backwards compatibility with legacy MCP servers
 * - Type-safe integration with AG3NTIC agent templates
 * - Automatic tool function conversion
 * - Resource and prompt management
 *
 * Usage Examples:
 *
 * ```typescript
 * // As MCP Client (connect to external servers)
 * const client = await createMCPClient({
 *   name: 'my-agent',
 *   version: '1.0.0',
 *   serverUrl: 'http://localhost:3000/mcp'
 * });
 *
 * const tools = await client.getToolFunctions();
 * const agent = createExecutorAgent(llmNode, tools);
 *
 * // As MCP Server (expose AG3NTIC capabilities)
 * const server = new MCPServer({
 *   name: 'ag3ntic-server',
 *   version: '1.0.0'
 * });
 *
 * server.registerAG3NTICTools(myTools);
 * await server.startHTTP();
 * ```
 */
// Export client functionality
var client_1 = require("./client");
Object.defineProperty(exports, "MCPClient", { enumerable: true, get: function () { return client_1.MCPClient; } });
Object.defineProperty(exports, "createMCPClient", { enumerable: true, get: function () { return client_1.createMCPClient; } });
Object.defineProperty(exports, "createMCPClientWithFallback", { enumerable: true, get: function () { return client_1.createMCPClientWithFallback; } });
// Export server functionality
var server_1 = require("./server");
Object.defineProperty(exports, "MCPServer", { enumerable: true, get: function () { return server_1.MCPServer; } });
Object.defineProperty(exports, "createMCPServer", { enumerable: true, get: function () { return server_1.createMCPServer; } });
Object.defineProperty(exports, "createMCPHTTPServer", { enumerable: true, get: function () { return server_1.createMCPHTTPServer; } });
/**
 * MCP Integration Utilities
 */
/**
 * Check if MCP SDK is available
 */
async function isMCPAvailable() {
    try {
        await Promise.resolve().then(() => __importStar(require('@modelcontextprotocol/sdk/client/index.js')));
        return true;
    }
    catch {
        return false;
    }
}
/**
 * Get MCP SDK version if available
 */
async function getMCPVersion() {
    try {
        const pkg = await Promise.resolve().then(() => __importStar(require('@modelcontextprotocol/sdk/package.json')));
        return pkg.version;
    }
    catch {
        return null;
    }
}
/**
 * Create an MCP-enabled tool function that can work with both
 * AG3NTIC's tool system and MCP protocol
 */
function createMCPCompatibleTool(name, description, inputSchema, handler) {
    return {
        // AG3NTIC tool function
        toolFunction: handler,
        // MCP tool definition
        mcpDefinition: {
            name,
            description,
            inputSchema
        },
        // MCP tool handler
        mcpHandler: async (args) => {
            try {
                const result = await handler(args);
                return {
                    content: [{
                            type: "text",
                            text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
                        }]
                };
            }
            catch (error) {
                return {
                    content: [{
                            type: "text",
                            text: `Error: ${error instanceof Error ? error.message : String(error)}`
                        }],
                    isError: true
                };
            }
        }
    };
}
/**
 * Convert AG3NTIC tools to MCP format
 */
function convertAG3NTICToolsToMCP(tools) {
    const mcpTools = [];
    for (const [name, toolFunction] of Object.entries(tools)) {
        mcpTools.push({
            name,
            definition: {
                name,
                description: `AG3NTIC tool: ${name}`,
                inputSchema: {
                    type: "object",
                    properties: {},
                    required: []
                }
            },
            handler: async (args) => {
                try {
                    const result = await Promise.resolve(toolFunction(args));
                    return {
                        content: [{
                                type: "text",
                                text: typeof result === 'string' ? result : JSON.stringify(result, null, 2)
                            }]
                    };
                }
                catch (error) {
                    return {
                        content: [{
                                type: "text",
                                text: `Error: ${error instanceof Error ? error.message : String(error)}`
                            }],
                        isError: true
                    };
                }
            }
        });
    }
    return mcpTools;
}
/**
 * Create a unified MCP + AG3NTIC agent that can use both local tools
 * and remote MCP server tools
 */
async function createMCPEnabledAgent(config) {
    const allTools = { ...config.localTools };
    const mcpClients = [];
    // Connect to MCP servers and get their tools
    if (config.mcpServers) {
        for (const serverConfig of config.mcpServers) {
            try {
                // Dynamic import to avoid requiring MCP SDK if not used
                const { createMCPClient } = await Promise.resolve().then(() => __importStar(require('./client')));
                const client = await createMCPClient({
                    name: config.agentConfig.name,
                    version: config.agentConfig.version,
                    serverUrl: serverConfig.url,
                    transport: serverConfig.transport || 'http'
                });
                const serverTools = await client.getToolFunctions();
                // Prefix server tools with server name to avoid conflicts
                for (const [toolName, toolFunction] of Object.entries(serverTools)) {
                    const prefixedName = `${serverConfig.name}_${toolName}`;
                    allTools[prefixedName] = toolFunction;
                }
                mcpClients.push(client);
                console.log(`✅ Connected to MCP server: ${serverConfig.name}`);
            }
            catch (error) {
                console.warn(`⚠️  Failed to connect to MCP server ${serverConfig.name}:`, error);
            }
        }
    }
    return {
        tools: allTools,
        mcpClients,
        async cleanup() {
            for (const client of mcpClients) {
                await client.disconnect();
            }
        }
    };
}
/**
 * MCP Integration Status
 */
async function getMCPIntegrationStatus() {
    const available = await isMCPAvailable();
    const version = await getMCPVersion();
    return {
        available,
        version,
        features: {
            client: available,
            server: available,
            streamableHttp: available,
            sse: available
        }
    };
}
//# sourceMappingURL=index.js.map