// src/templates/executor/index.ts

import { Graph, AgentState, NodeFunction } from '../../core';
import { createToolNode, shouldCallTools, ToolMap } from '../../lib';

/**
 * Configuration options for the Executor Agent
 */
export interface ExecutorAgentOptions {
  /** Map of available tools */
  tools: ToolMap;
  /** System message for the agent */
  systemMessage?: string;
  /** Custom agent node function (if you want to override the default) */
  agentNode?: NodeFunction<any>;
  /** Whether to enable enhanced tool execution with retries and logging */
  enhancedTools?: boolean;
  /** Tool execution options */
  toolOptions?: {
    logExecution?: boolean;
    timeout?: number;
    retries?: number;
  };
}

/**
 * State interface for Executor Agent
 */
export interface ExecutorAgentState extends AgentState {
  /** The task to execute */
  task?: string;
  /** Available tools (for reference) */
  availableTools?: string[];
  /** Execution status */
  status?: 'pending' | 'executing' | 'completed' | 'failed';
  /** Final result */
  result?: string;
}

/**
 * Create an Executor Agent - the fundamental "doer" agent
 * 
 * This is the most basic but essential template. It receives a task,
 * uses available tools to complete it, and returns the result.
 * 
 * @param options Configuration options
 * @returns Configured Graph ready for execution
 */
export function createExecutorAgent<TState extends ExecutorAgentState = ExecutorAgentState>(
  options: ExecutorAgentOptions
): Graph<TState> {
  const {
    tools,
    agentNode,
    enhancedTools = false,
    toolOptions = {}
  } = options;

  const systemMessage = options.systemMessage || "You are a helpful assistant that can use tools to complete tasks. When given a task, analyze what needs to be done and use the appropriate tools to accomplish it.";

  // Create the tool execution node
  const toolNode = enhancedTools 
    ? require('../../lib').createEnhancedToolNode(tools, toolOptions)
    : createToolNode<TState>(tools);

  // Create default agent node if none provided
  const defaultAgentNode: NodeFunction<TState> = async (state) => {
    const { addMessage } = require('../../lib');

    // Use systemMessage for context (though in this mock implementation, it's mainly for reference)
    console.log(`Agent operating with system message: ${systemMessage.substring(0, 50)}...`);
    
    // Simple mock implementation - in real usage, this would be replaced
    // with an actual LLM integration from /integrations
    const lastMessage = state.messages[state.messages.length - 1];
    
    if (lastMessage?.role === 'user') {
      // Determine if we need to use tools based on the task
      const task = lastMessage.content || state.task || '';
      const toolNames = Object.keys(tools);
      
      // Simple heuristic to decide if we need tools
      const needsTools = toolNames.some(toolName => 
        task.toLowerCase().includes(toolName.toLowerCase()) ||
        task.toLowerCase().includes('search') ||
        task.toLowerCase().includes('calculate') ||
        task.toLowerCase().includes('weather')
      );
      
      if (needsTools && toolNames.length > 0) {
        // Mock tool call - in real usage, LLM would decide this
        const toolToUse = toolNames[0]; // Simple selection
        const assistantMessage = {
          role: 'assistant' as const,
          content: null,
          tool_calls: [{
            id: `call_${Date.now()}`,
            type: 'function' as const,
            function: {
              name: toolToUse,
              arguments: JSON.stringify({}) // Mock arguments
            }
          }]
        };
        return addMessage(state, assistantMessage);
      } else {
        // Direct response without tools
        const assistantMessage = {
          role: 'assistant' as const,
          content: `I understand you want me to: ${task}. I'll help you with that.`
        };
        return addMessage(state, assistantMessage);
      }
    }
    
    // Handle tool responses
    if (lastMessage?.role === 'tool') {
      const assistantMessage = {
        role: 'assistant' as const,
        content: `Task completed. Result: ${lastMessage.content}`
      };
      return { 
        ...addMessage(state, assistantMessage),
        status: 'completed' as const,
        result: lastMessage.content
      };
    }
    
    return {};
  };

  // Use provided agent node or default
  const finalAgentNode = agentNode || defaultAgentNode;

  // Build and return the graph
  return new Graph<TState>()
    .addNode('agent', finalAgentNode)
    .addNode('tools', toolNode)
    .setEntryPoint('agent')
    .addConditionalEdge('agent', shouldCallTools, {
      'tools': 'tools',
      '__end__': '__END__'
    })
    .addEdge('tools', 'agent');
}

/**
 * Create a simple executor agent with minimal configuration
 * 
 * @param tools Map of available tools
 * @param systemMessage Optional system message
 * @returns Configured Graph
 */
export function createSimpleExecutor<TState extends ExecutorAgentState = ExecutorAgentState>(
  tools: ToolMap,
  systemMessage?: string
): Graph<TState> {
  const options: ExecutorAgentOptions = { tools };
  if (systemMessage) {
    options.systemMessage = systemMessage;
  }
  return createExecutorAgent<TState>(options);
}

/**
 * Create an enhanced executor agent with retries and logging
 * 
 * @param tools Map of available tools
 * @param options Enhanced execution options
 * @returns Configured Graph
 */
export function createEnhancedExecutor<TState extends ExecutorAgentState = ExecutorAgentState>(
  tools: ToolMap,
  options: {
    systemMessage?: string;
    logExecution?: boolean;
    timeout?: number;
    retries?: number;
  } = {}
): Graph<TState> {
  const executorOptions: ExecutorAgentOptions = {
    tools,
    enhancedTools: true
  };

  if (options.systemMessage) {
    executorOptions.systemMessage = options.systemMessage;
  }

  const toolOptions: { logExecution?: boolean; timeout?: number; retries?: number } = {};
  if (options.logExecution !== undefined) toolOptions.logExecution = options.logExecution;
  if (options.timeout !== undefined) toolOptions.timeout = options.timeout;
  if (options.retries !== undefined) toolOptions.retries = options.retries;

  if (Object.keys(toolOptions).length > 0) {
    executorOptions.toolOptions = toolOptions;
  }

  return createExecutorAgent<TState>(executorOptions);
}
