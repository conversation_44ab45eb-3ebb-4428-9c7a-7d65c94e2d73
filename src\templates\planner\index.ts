// src/templates/planner/index.ts

import { Graph, AgentState, NodeFunction } from '../../core';
import { addMessage } from '../../lib';

/**
 * Configuration options for the Planner Agent
 */
export interface PlannerAgentOptions {
  /** System message for planning */
  systemMessage?: string;
  /** Custom planner node function */
  plannerNode?: NodeFunction<any>;
  /** Maximum number of steps in a plan */
  maxSteps?: number;
  /** Planning strategy */
  strategy?: 'sequential' | 'parallel' | 'hierarchical';
}

/**
 * Represents a single step in a plan
 */
export interface PlanStep {
  /** Unique identifier for the step */
  id: string;
  /** Description of what needs to be done */
  description: string;
  /** Type of step (task, decision, etc.) */
  type: 'task' | 'decision' | 'review' | 'synthesis';
  /** Dependencies (step IDs that must complete first) */
  dependencies?: string[];
  /** Estimated effort/complexity (1-5) */
  complexity?: number;
  /** Required tools or capabilities */
  requiredCapabilities?: string[];
  /** Expected output format */
  expectedOutput?: string;
}

/**
 * State interface for Planner Agent
 */
export interface PlannerAgentState extends AgentState {
  /** The high-level goal to plan for */
  goal?: string;
  /** Generated plan */
  plan?: PlanStep[];
  /** Planning status */
  planningStatus?: 'analyzing' | 'planning' | 'completed' | 'failed';
  /** Planning metadata */
  planningMetadata?: {
    strategy: string;
    estimatedDuration?: string;
    complexity: number;
    requiredCapabilities: string[];
  };
}

/**
 * Create a Planner Agent - breaks down complex goals into actionable steps
 * 
 * This agent specializes in taking high-level objectives and creating
 * detailed, step-by-step plans that other agents can execute.
 * 
 * @param options Configuration options
 * @returns Configured Graph ready for execution
 */
export function createPlannerAgent<TState extends PlannerAgentState = PlannerAgentState>(
  options: PlannerAgentOptions = {}
): Graph<TState> {
  const {
    plannerNode,
    maxSteps = 20,
    strategy = 'sequential'
  } = options;

  const systemMessage = options.systemMessage || `You are an expert planning assistant. Your job is to break down complex goals into clear, actionable steps.

When given a goal, analyze it carefully and create a detailed plan with the following considerations:
1. Break the goal into logical, sequential steps
2. Identify dependencies between steps
3. Estimate complexity and required capabilities
4. Ensure each step is specific and actionable
5. Consider potential risks and alternatives

Format your response as a structured plan with clear steps.`;

  // Create default planner node if none provided
  const defaultPlannerNode: NodeFunction<TState> = async (state) => {
    const lastMessage = state.messages[state.messages.length - 1];

    // Use systemMessage for context
    console.log(`Planner operating with system message: ${systemMessage.substring(0, 50)}...`);
    
    if (lastMessage?.role === 'user') {
      const goal = lastMessage.content || state.goal || '';
      
      // Mock planning logic - in real usage, this would use an LLM
      const plan = generateMockPlan(goal, strategy, maxSteps);
      
      const planningMetadata = {
        strategy,
        estimatedDuration: estimateDuration(plan),
        complexity: calculateComplexity(plan),
        requiredCapabilities: extractCapabilities(plan)
      };
      
      const assistantMessage = {
        role: 'assistant' as const,
        content: `I've analyzed your goal: "${goal}" and created a ${plan.length}-step plan using a ${strategy} approach.\n\n` +
                 `Plan Overview:\n${plan.map((step, i) => `${i + 1}. ${step.description}`).join('\n')}\n\n` +
                 `Estimated complexity: ${planningMetadata.complexity}/5\n` +
                 `Required capabilities: ${planningMetadata.requiredCapabilities.join(', ')}`
      };
      
      return {
        ...addMessage(state, assistantMessage),
        plan,
        planningStatus: 'completed' as const,
        planningMetadata,
        goal
      };
    }
    
    return {};
  };

  // Use provided planner node or default
  const finalPlannerNode = plannerNode || defaultPlannerNode;

  // Build and return the graph (single node for planning)
  return new Graph<TState>()
    .addNode('planner', finalPlannerNode)
    .setEntryPoint('planner')
    .addEdge('planner', '__END__');
}

/**
 * Generate a mock plan for demonstration purposes
 */
function generateMockPlan(goal: string, strategy: string, maxSteps: number): PlanStep[] {
  const steps: PlanStep[] = [];

  // Simple heuristic-based planning (strategy influences approach)
  console.log(`Generating ${strategy} plan for: ${goal}`);
  if (goal.toLowerCase().includes('research')) {
    steps.push(
      {
        id: 'step_1',
        description: 'Define research scope and key questions',
        type: 'task',
        complexity: 2,
        requiredCapabilities: ['analysis'],
        expectedOutput: 'Research scope document'
      },
      {
        id: 'step_2',
        description: 'Gather information from multiple sources',
        type: 'task',
        dependencies: ['step_1'],
        complexity: 3,
        requiredCapabilities: ['web_search', 'data_collection'],
        expectedOutput: 'Raw research data'
      },
      {
        id: 'step_3',
        description: 'Analyze and synthesize findings',
        type: 'synthesis',
        dependencies: ['step_2'],
        complexity: 4,
        requiredCapabilities: ['analysis', 'synthesis'],
        expectedOutput: 'Research report'
      }
    );
  } else if (goal.toLowerCase().includes('write') || goal.toLowerCase().includes('create')) {
    steps.push(
      {
        id: 'step_1',
        description: 'Outline the structure and key points',
        type: 'task',
        complexity: 2,
        requiredCapabilities: ['planning', 'writing'],
        expectedOutput: 'Content outline'
      },
      {
        id: 'step_2',
        description: 'Draft the main content',
        type: 'task',
        dependencies: ['step_1'],
        complexity: 3,
        requiredCapabilities: ['writing', 'creativity'],
        expectedOutput: 'First draft'
      },
      {
        id: 'step_3',
        description: 'Review and refine the content',
        type: 'review',
        dependencies: ['step_2'],
        complexity: 2,
        requiredCapabilities: ['editing', 'quality_assurance'],
        expectedOutput: 'Final content'
      }
    );
  } else {
    // Generic plan
    steps.push(
      {
        id: 'step_1',
        description: 'Analyze the requirements and constraints',
        type: 'task',
        complexity: 2,
        requiredCapabilities: ['analysis'],
        expectedOutput: 'Requirements analysis'
      },
      {
        id: 'step_2',
        description: 'Execute the main task',
        type: 'task',
        dependencies: ['step_1'],
        complexity: 3,
        requiredCapabilities: ['execution'],
        expectedOutput: 'Task completion'
      },
      {
        id: 'step_3',
        description: 'Verify results and finalize',
        type: 'review',
        dependencies: ['step_2'],
        complexity: 1,
        requiredCapabilities: ['quality_assurance'],
        expectedOutput: 'Final result'
      }
    );
  }
  
  return steps.slice(0, maxSteps);
}

/**
 * Estimate duration based on plan complexity
 */
function estimateDuration(plan: PlanStep[]): string {
  const totalComplexity = plan.reduce((sum, step) => sum + (step.complexity || 1), 0);
  
  if (totalComplexity <= 5) return '15-30 minutes';
  if (totalComplexity <= 10) return '1-2 hours';
  if (totalComplexity <= 15) return '2-4 hours';
  return '4+ hours';
}

/**
 * Calculate overall plan complexity
 */
function calculateComplexity(plan: PlanStep[]): number {
  const avgComplexity = plan.reduce((sum, step) => sum + (step.complexity || 1), 0) / plan.length;
  return Math.round(avgComplexity);
}

/**
 * Extract required capabilities from plan
 */
function extractCapabilities(plan: PlanStep[]): string[] {
  const capabilities = new Set<string>();
  plan.forEach(step => {
    step.requiredCapabilities?.forEach(cap => capabilities.add(cap));
  });
  return Array.from(capabilities);
}

/**
 * Create a simple planner with minimal configuration
 */
export function createSimplePlanner<TState extends PlannerAgentState = PlannerAgentState>(
  systemMessage?: string
): Graph<TState> {
  const options: PlannerAgentOptions = {};
  if (systemMessage) {
    options.systemMessage = systemMessage;
  }
  return createPlannerAgent<TState>(options);
}

/**
 * Create a hierarchical planner for complex, multi-level planning
 */
export function createHierarchicalPlanner<TState extends PlannerAgentState = PlannerAgentState>(
  maxSteps: number = 30
): Graph<TState> {
  return createPlannerAgent<TState>({
    strategy: 'hierarchical',
    maxSteps,
    systemMessage: `You are an expert strategic planner specializing in hierarchical planning. Break down complex goals into multiple levels of detail, creating both high-level phases and detailed sub-tasks.`
  });
}
