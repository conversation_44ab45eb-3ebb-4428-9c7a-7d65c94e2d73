// src/integrations/unified/interface.ts

import { MCPMessage, MCPAssistantMessage, AgentState, NodeFunction } from "../../core/types";

/**
 * Unified tool definition that works across providers
 */
export interface UnifiedTool {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

/**
 * Unified configuration for LLM providers
 */
export interface UnifiedLLMConfig {
  provider: 'openai' | 'anthropic';
  model?: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
  apiKey?: string;
  baseURL?: string;
}

/**
 * Options for LLM requests
 */
export interface UnifiedLLMOptions {
  systemMessage?: string;
  tools?: UnifiedTool[];
  toolChoice?: 'auto' | 'none' | { name: string };
  stream?: boolean;
  responseFormat?: 'text' | 'json';
}

/**
 * Streaming callback types
 */
export type StreamChunkCallback = (chunk: string) => void;
export type StreamEventCallback = (event: any) => void;

/**
 * Unified LLM client interface
 */
export interface UnifiedLLMClient {
  /**
   * Create a completion
   */
  createCompletion(
    messages: MCPMessage[],
    options?: UnifiedLLMOptions
  ): Promise<MCPAssistantMessage>;

  /**
   * Create a streaming completion
   */
  createStreamingCompletion(
    messages: MCPMessage[],
    options?: UnifiedLLMOptions,
    onChunk?: StreamChunkCallback,
    onEvent?: StreamEventCallback
  ): Promise<MCPAssistantMessage>;

  /**
   * Create an agent node
   */
  createAgentNode<TState extends AgentState>(
    options?: UnifiedLLMOptions
  ): NodeFunction<TState>;

  /**
   * Test connection
   */
  testConnection(): Promise<boolean>;

  /**
   * Get provider name
   */
  getProvider(): string;

  /**
   * Get available models
   */
  getAvailableModels(): Promise<string[]>;
}

/**
 * Factory function to create unified LLM clients
 */
export async function createUnifiedLLMClient(config: UnifiedLLMConfig): Promise<UnifiedLLMClient> {
  const { provider, apiKey, ...options } = config;

  switch (provider) {
    case 'openai': {
      const { OpenAIClient } = await import('../openai/client');
      
      try {
        // Dynamic import to avoid requiring OpenAI SDK as dependency
        const OpenAI = require('openai');
        const openai = new OpenAI({ 
          apiKey: apiKey || process.env.OPENAI_API_KEY,
          baseURL: options.baseURL
        });
        
        const client = new OpenAIClient(openai, options);
        return new UnifiedOpenAIAdapter(client, options);
      } catch (error) {
        throw new Error('OpenAI SDK not found. Please install it with: npm install openai');
      }
    }

    case 'anthropic': {
      const { AnthropicClient } = await import('../anthropic/client');
      
      try {
        // Dynamic import to avoid requiring Anthropic SDK as dependency
        const Anthropic = require('@anthropic-ai/sdk');
        const anthropic = new Anthropic({ 
          apiKey: apiKey || process.env.ANTHROPIC_API_KEY,
          baseURL: options.baseURL
        });
        
        const client = new AnthropicClient(anthropic, options);
        return new UnifiedAnthropicAdapter(client, options);
      } catch (error) {
        throw new Error('Anthropic SDK not found. Please install it with: npm install @anthropic-ai/sdk');
      }
    }

    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }
}

/**
 * OpenAI adapter for unified interface
 */
class UnifiedOpenAIAdapter implements UnifiedLLMClient {
  constructor(
    private client: any, // OpenAIClient
    private config: Partial<UnifiedLLMConfig>
  ) {}

  async createCompletion(
    messages: MCPMessage[],
    options: UnifiedLLMOptions = {}
  ): Promise<MCPAssistantMessage> {
    const openaiOptions = this.convertToOpenAIOptions(options);
    return this.client.createChatCompletion(messages, openaiOptions);
  }

  async createStreamingCompletion(
    messages: MCPMessage[],
    options: UnifiedLLMOptions = {},
    onChunk?: StreamChunkCallback,
    onEvent?: StreamEventCallback
  ): Promise<MCPAssistantMessage> {
    const openaiOptions = this.convertToOpenAIOptions(options);
    // Note: OpenAI doesn't have events like Anthropic, so onEvent is unused here
    if (onEvent) {
      console.log('OpenAI streaming events not supported, onEvent callback ignored');
    }
    return this.client.createStreamingCompletion(messages, openaiOptions, onChunk);
  }

  createAgentNode<TState extends AgentState>(
    options: UnifiedLLMOptions = {}
  ): NodeFunction<TState> {
    const openaiOptions = this.convertToOpenAIOptions(options);
    return this.client.createAgentNode(openaiOptions);
  }

  async testConnection(): Promise<boolean> {
    return this.client.testConnection();
  }

  getProvider(): string {
    return 'openai';
  }

  async getAvailableModels(): Promise<string[]> {
    return this.client.getAvailableModels();
  }

  private convertToOpenAIOptions(options: UnifiedLLMOptions): any {
    const { tools, toolChoice, responseFormat, ...rest } = options;
    
    const openaiOptions: any = {
      ...rest,
      model: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      timeout: this.config.timeout
    };

    if (tools) {
      openaiOptions.tools = tools.map(tool => ({
        type: 'function',
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.parameters
        }
      }));
    }

    if (toolChoice) {
      if (typeof toolChoice === 'string') {
        openaiOptions.toolChoice = toolChoice;
      } else {
        openaiOptions.toolChoice = {
          type: 'function',
          function: { name: toolChoice.name }
        };
      }
    }

    if (responseFormat === 'json') {
      openaiOptions.responseFormat = { type: 'json_object' };
    }

    return openaiOptions;
  }
}

/**
 * Anthropic adapter for unified interface
 */
class UnifiedAnthropicAdapter implements UnifiedLLMClient {
  constructor(
    private client: any, // AnthropicClient
    private config: Partial<UnifiedLLMConfig>
  ) {}

  async createCompletion(
    messages: MCPMessage[],
    options: UnifiedLLMOptions = {}
  ): Promise<MCPAssistantMessage> {
    const anthropicOptions = this.convertToAnthropicOptions(options);
    return this.client.createMessage(messages, anthropicOptions);
  }

  async createStreamingCompletion(
    messages: MCPMessage[],
    options: UnifiedLLMOptions = {},
    onChunk?: StreamChunkCallback,
    onEvent?: StreamEventCallback
  ): Promise<MCPAssistantMessage> {
    const anthropicOptions = this.convertToAnthropicOptions(options);
    return this.client.createStreamingMessage(messages, anthropicOptions, onChunk, onEvent);
  }

  createAgentNode<TState extends AgentState>(
    options: UnifiedLLMOptions = {}
  ): NodeFunction<TState> {
    const anthropicOptions = this.convertToAnthropicOptions(options);
    return this.client.createAgentNode(anthropicOptions);
  }

  async testConnection(): Promise<boolean> {
    return this.client.testConnection();
  }

  getProvider(): string {
    return 'anthropic';
  }

  async getAvailableModels(): Promise<string[]> {
    // Anthropic doesn't have a models endpoint, return common models
    return [
      'claude-3-5-sonnet-20241022',
      'claude-3-5-haiku-20241022',
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307'
    ];
  }

  private convertToAnthropicOptions(options: UnifiedLLMOptions): any {
    const { tools, toolChoice, ...rest } = options;
    
    const anthropicOptions: any = {
      ...rest,
      model: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens,
      timeout: this.config.timeout
    };

    if (tools) {
      anthropicOptions.tools = tools.map(tool => ({
        name: tool.name,
        description: tool.description,
        input_schema: tool.parameters
      }));
    }

    if (toolChoice) {
      if (typeof toolChoice === 'string') {
        anthropicOptions.toolChoice = { type: toolChoice };
      } else {
        anthropicOptions.toolChoice = {
          type: 'tool',
          name: toolChoice.name
        };
      }
    }

    return anthropicOptions;
  }
}

/**
 * Helper function to create a simple unified client
 */
export async function createSimpleLLMClient(
  provider: 'openai' | 'anthropic',
  apiKey?: string,
  model?: string
): Promise<UnifiedLLMClient> {
  const config: UnifiedLLMConfig = {
    provider,
    model: model || (provider === 'openai' ? 'gpt-4o' : 'claude-3-5-sonnet-20241022'),
    temperature: 0.7,
    maxTokens: 4096
  };

  if (apiKey) {
    config.apiKey = apiKey;
  }

  return createUnifiedLLMClient(config);
}
