import { AgentState, MCPMessage, MCPAssistantMessage, NodeFunction } from "../../core/types";
/**
 * Anthropic tool definition
 */
export interface AnthropicTool {
    name: string;
    description: string;
    input_schema: {
        type: 'object';
        properties: Record<string, any>;
        required?: string[];
    };
}
/**
 * Options for creating an Anthropic agent node
 */
export interface AnthropicAgentOptions {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemMessage?: string;
    tools?: AnthropicTool[];
}
/**
 * Anthropic client wrapper for AG3NTIC
 * This assumes you have the official Anthropic SDK installed: npm install @anthropic-ai/sdk
 */
export declare class AnthropicClient {
    private anthropic;
    constructor(anthropicInstance: any);
    /**
     * Create a message using Anthropic Claude
     */
    createMessage(messages: MCPMessage[], options?: AnthropicAgentOptions): Promise<MCPAssistantMessage>;
    /**
     * Create an agent node that uses Anthropic for LLM calls
     */
    createAgentNode<TState extends AgentState>(options?: AnthropicAgentOptions): NodeFunction<TState>;
    /**
     * Test the connection to Anthropic
     */
    testConnection(): Promise<boolean>;
    /**
     * Get available models
     */
    getAvailableModels(): string[];
}
/**
 * Factory function to create Anthropic client from API key
 * This requires the Anthropic SDK to be installed
 */
export declare function createAnthropicClient(apiKey: string): AnthropicClient;
//# sourceMappingURL=client.d.ts.map