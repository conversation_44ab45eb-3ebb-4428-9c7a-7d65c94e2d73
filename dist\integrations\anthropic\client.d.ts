import { AgentState, MCPMessage, MCPAssistantMessage, NodeFunction } from "../../core/types";
/**
 * Anthropic-compatible types (mirrors official SDK without requiring it)
 */
export interface AnthropicTool {
    name: string;
    description: string;
    input_schema: {
        type: 'object';
        properties: Record<string, any>;
        required?: string[];
    };
}
export interface AnthropicToolUse {
    type: 'tool_use';
    id: string;
    name: string;
    input: Record<string, any>;
}
export interface AnthropicTextBlock {
    type: 'text';
    text: string;
}
export interface AnthropicContentBlock {
    type: 'text' | 'tool_use' | 'tool_result';
    text?: string;
    id?: string;
    name?: string;
    input?: Record<string, any>;
    tool_use_id?: string;
    content?: string;
}
export interface AnthropicMessage {
    role: 'user' | 'assistant';
    content: string | AnthropicContentBlock[];
}
export interface AnthropicUsage {
    input_tokens: number;
    output_tokens: number;
}
export interface AnthropicResponse {
    id: string;
    type: 'message';
    role: 'assistant';
    content: AnthropicContentBlock[];
    model: string;
    stop_reason: 'end_turn' | 'max_tokens' | 'stop_sequence' | 'tool_use' | null;
    stop_sequence: string | null;
    usage: AnthropicUsage;
}
export interface AnthropicError extends Error {
    status?: number;
    type?: string;
}
/**
 * Configuration options for Anthropic client
 */
export interface AnthropicClientConfig {
    apiKey?: string;
    baseURL?: string;
    timeout?: number;
    maxRetries?: number;
    defaultHeaders?: Record<string, string>;
}
/**
 * Options for creating an Anthropic agent node
 */
export interface AnthropicAgentOptions {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemMessage?: string;
    tools?: AnthropicTool[];
    toolChoice?: {
        type: 'auto' | 'any' | 'tool';
        name?: string;
    };
    stream?: boolean;
    timeout?: number;
}
/**
 * Anthropic client wrapper for AG3NTIC
 * This assumes you have the official Anthropic SDK installed: npm install @anthropic-ai/sdk
 *
 * Features:
 * - Proper TypeScript types
 * - Enhanced error handling
 * - Streaming support with events
 * - Tool calling with proper message format
 * - Timeout configuration
 * - Batch processing support
 */
export declare class AnthropicClient {
    private anthropic;
    private config;
    constructor(anthropicInstance: any, config?: AnthropicClientConfig);
    /**
     * Create a message using Anthropic Claude
     */
    createMessage(messages: MCPMessage[], options?: AnthropicAgentOptions): Promise<MCPAssistantMessage>;
    /**
     * Convert MCP messages to Anthropic format
     */
    private convertMCPToAnthropic;
    /**
     * Convert Anthropic response to MCP format
     */
    private convertAnthropicToMCP;
    /**
     * Handle Anthropic API errors with proper typing
     */
    private handleAnthropicError;
    /**
     * Create a streaming message using Anthropic Claude
     */
    createStreamingMessage(messages: MCPMessage[], options?: AnthropicAgentOptions, onChunk?: (chunk: string) => void, onEvent?: (event: any) => void): Promise<MCPAssistantMessage>;
    /**
     * Create an agent node that uses Anthropic for LLM calls
     */
    createAgentNode<TState extends AgentState>(options?: AnthropicAgentOptions): NodeFunction<TState>;
    /**
     * Test the connection to Anthropic
     */
    testConnection(): Promise<boolean>;
    /**
     * Get available models
     */
    getAvailableModels(): string[];
}
/**
 * Factory function to create Anthropic client from API key
 * This requires the Anthropic SDK to be installed
 */
export declare function createAnthropicClient(apiKey: string): AnthropicClient;
//# sourceMappingURL=client.d.ts.map