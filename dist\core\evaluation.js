"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.globalEvaluationSystem = exports.EvaluationSystem = void 0;
exports.createEvaluationSystem = createEvaluationSystem;
/**
 * Ultra-high-performance Evaluation system
 *
 * Features:
 * - Self-evaluation capabilities
 * - Peer review between agents
 * - Human-in-the-loop workflows
 * - Performance scorecards
 * - Custom evaluation metrics
 * - Trend analysis and improvement tracking
 */
class EvaluationSystem {
    constructor(config) {
        this.scorecards = new Map();
        this.pendingReviews = new Map();
        this.config = {
            enableSelfEvaluation: true,
            enablePeerReview: false,
            enableHumanReview: false,
            humanReviewThreshold: 0.7,
            metrics: ['accuracy', 'relevance', 'completeness'],
            customEvaluators: new Map(),
            ...config,
        };
    }
    /**
     * Evaluate agent output with multiple evaluation methods
     */
    async evaluate(agentName, input, output, context) {
        const evaluations = [];
        // Self-evaluation
        if (this.config.enableSelfEvaluation) {
            const selfEval = await this.performSelfEvaluation(input, output, context);
            evaluations.push(selfEval);
        }
        // Peer review
        if (this.config.enablePeerReview && this.config.evaluationAgent) {
            const peerEval = await this.performPeerReview(input, output, context);
            evaluations.push(peerEval);
        }
        // Custom evaluators
        for (const [name, evaluator] of this.config.customEvaluators) {
            try {
                const customEval = await evaluator(input, output, context);
                evaluations.push(customEval);
            }
            catch (error) {
                console.warn(`Custom evaluator ${name} failed:`, error);
            }
        }
        // Combine evaluations
        const combinedResult = this.combineEvaluations(evaluations);
        // Update scorecard
        this.updateScorecard(agentName, combinedResult);
        // Check if human review is needed
        if (this.config.enableHumanReview &&
            combinedResult.score < this.config.humanReviewThreshold) {
            await this.requestHumanReview(agentName, input, output, context);
        }
        return combinedResult;
    }
    /**
     * Perform self-evaluation using the agent's own reasoning
     */
    async performSelfEvaluation(input, output, context) {
        // This would use an LLM to evaluate its own output
        // For now, return a basic evaluation
        const metrics = {};
        // Simple heuristics for demonstration
        metrics.completeness = output.length > 50 ? 0.8 : 0.5;
        metrics.relevance = output.toLowerCase().includes(input.toLowerCase().split(' ')[0]) ? 0.9 : 0.6;
        const score = Object.values(metrics).reduce((sum, val) => sum + (val || 0), 0) /
            Object.keys(metrics).length;
        return {
            score,
            metrics,
            feedback: 'Self-evaluation completed',
            confidence: 0.7,
            timestamp: Date.now(),
        };
    }
    /**
     * Perform peer review using another agent
     */
    async performPeerReview(input, output, context) {
        if (!this.config.evaluationAgent) {
            throw new Error('No evaluation agent configured for peer review');
        }
        const evaluationPrompt = `
Please evaluate the following response:

Input: ${input}
Output: ${output}

Rate the response on a scale of 0-1 for:
- Accuracy: How factually correct is the response?
- Relevance: How well does it address the input?
- Completeness: How thorough is the response?
- Coherence: How well-structured and logical is it?

Provide specific feedback and suggestions for improvement.
`;
        try {
            const result = await this.config.evaluationAgent.run(evaluationPrompt);
            // Parse the evaluation result (this would be more sophisticated in practice)
            return {
                score: 0.8, // Would parse from agent response
                metrics: {
                    accuracy: 0.8,
                    relevance: 0.9,
                    completeness: 0.7,
                    coherence: 0.8,
                },
                feedback: result.finalOutput,
                confidence: 0.9,
                timestamp: Date.now(),
            };
        }
        catch (error) {
            console.warn('Peer review failed:', error);
            return {
                score: 0.5,
                metrics: {},
                feedback: 'Peer review failed',
                confidence: 0.1,
                timestamp: Date.now(),
            };
        }
    }
    /**
     * Request human review for low-scoring outputs
     */
    async requestHumanReview(agentName, input, output, context) {
        const reviewId = `${agentName}_${Date.now()}`;
        this.pendingReviews.set(reviewId, {
            input,
            output,
            context,
            timestamp: Date.now(),
        });
        // In a real implementation, this would trigger a notification
        // to human reviewers through a UI, email, or webhook
        console.log(`Human review requested for agent ${agentName}: ${reviewId}`);
    }
    /**
     * Submit human feedback
     */
    async submitHumanFeedback(reviewId, feedback) {
        const review = this.pendingReviews.get(reviewId);
        if (!review) {
            throw new Error(`Review ${reviewId} not found`);
        }
        // Process human feedback and update scorecards
        // This would integrate with the evaluation system
        console.log('Human feedback received:', feedback);
        this.pendingReviews.delete(reviewId);
    }
    /**
     * Combine multiple evaluation results
     */
    combineEvaluations(evaluations) {
        if (evaluations.length === 0) {
            return {
                score: 0.5,
                metrics: {},
                feedback: 'No evaluations performed',
                confidence: 0.1,
                timestamp: Date.now(),
            };
        }
        // Weighted average based on confidence
        const totalWeight = evaluations.reduce((sum, eval) => sum + eval.confidence, 0);
        const weightedScore = evaluations.reduce((sum, eval) => sum + (eval.score * eval.confidence), 0) / totalWeight;
        // Combine metrics
        const combinedMetrics = {};
        for (const evaluation of evaluations) {
            for (const [metric, value] of Object.entries(evaluation.metrics)) {
                if (value !== undefined) {
                    combinedMetrics[metric] =
                        (combinedMetrics[metric] || 0) + value;
                }
            }
        }
        // Average the metrics
        for (const metric in combinedMetrics) {
            combinedMetrics[metric] =
                combinedMetrics[metric] / evaluations.length;
        }
        return {
            score: weightedScore,
            metrics: combinedMetrics,
            feedback: evaluations.map(e => e.feedback).join('; '),
            suggestions: evaluations.flatMap(e => e.suggestions || []),
            confidence: totalWeight / evaluations.length,
            timestamp: Date.now(),
        };
    }
    /**
     * Update agent scorecard with new evaluation
     */
    updateScorecard(agentName, evaluation) {
        let scorecard = this.scorecards.get(agentName);
        if (!scorecard) {
            scorecard = {
                agentName,
                totalEvaluations: 0,
                averageScore: 0,
                metrics: {},
                recentEvaluations: [],
                humanFeedback: [],
                lastUpdated: Date.now(),
            };
            this.scorecards.set(agentName, scorecard);
        }
        // Update totals
        scorecard.totalEvaluations++;
        scorecard.averageScore = ((scorecard.averageScore * (scorecard.totalEvaluations - 1)) + evaluation.score) / scorecard.totalEvaluations;
        // Update metrics
        for (const [metric, value] of Object.entries(evaluation.metrics)) {
            if (value !== undefined) {
                if (!scorecard.metrics[metric]) {
                    scorecard.metrics[metric] = {
                        average: 0,
                        count: 0,
                        trend: 'stable',
                    };
                }
                const metricData = scorecard.metrics[metric];
                metricData.average = ((metricData.average * metricData.count) + value) / (metricData.count + 1);
                metricData.count++;
            }
        }
        // Keep recent evaluations (last 10)
        scorecard.recentEvaluations.push(evaluation);
        if (scorecard.recentEvaluations.length > 10) {
            scorecard.recentEvaluations.shift();
        }
        scorecard.lastUpdated = Date.now();
    }
    /**
     * Get scorecard for an agent
     */
    getScorecard(agentName) {
        return this.scorecards.get(agentName);
    }
    /**
     * Get all scorecards
     */
    getAllScorecards() {
        return Array.from(this.scorecards.values());
    }
    /**
     * Get pending human reviews
     */
    getPendingReviews() {
        return Array.from(this.pendingReviews.entries()).map(([id, review]) => ({
            id,
            ...review,
        }));
    }
}
exports.EvaluationSystem = EvaluationSystem;
/**
 * Create evaluation system with configuration
 */
function createEvaluationSystem(config) {
    return new EvaluationSystem(config);
}
/**
 * Global evaluation system instance
 */
exports.globalEvaluationSystem = new EvaluationSystem({
    enableSelfEvaluation: true,
    enablePeerReview: false,
    enableHumanReview: false,
    metrics: ['accuracy', 'relevance', 'completeness'],
});
//# sourceMappingURL=evaluation.js.map