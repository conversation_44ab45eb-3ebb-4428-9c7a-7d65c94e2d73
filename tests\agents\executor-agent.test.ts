import { describe, test, expect, beforeEach } from '@jest/globals';
import { z } from 'zod';
import { 
  Agent, 
  Tool, 
  Memory, 
  createTestState, 
  createMockTool, 
  assertAgentResult, 
  assertPerformance,
  globalPerformanceMeasurer,
  testData,
  testConfig
} from '../setup.js';

/**
 * Test Suite 1: Executor Agent
 * 
 * Tests the core executor agent functionality including:
 * - Basic task execution
 * - Tool usage and integration
 * - Memory management
 * - Error handling and retries
 * - Performance optimization
 */

describe('Executor Agent Tests', () => {
  let executorAgent: Agent;
  let mockTools: Tool[];
  let memory: Memory;

  beforeEach(() => {
    // Create mock tools for testing
    mockTools = [
      new Tool(
        {
          name: 'calculator',
          description: 'Perform mathematical calculations',
          parameters: z.object({
            expression: z.string().describe('Mathematical expression to evaluate'),
          }),
        },
        async ({ expression }) => {
          // Simple calculator mock
          try {
            const result = eval(expression.replace(/[^0-9+\-*/().]/g, ''));
            return `The result of ${expression} is ${result}`;
          } catch {
            return `Invalid expression: ${expression}`;
          }
        }
      ),
      new Tool(
        {
          name: 'text_processor',
          description: 'Process and analyze text',
          parameters: z.object({
            text: z.string(),
            operation: z.enum(['count_words', 'uppercase', 'lowercase', 'reverse']),
          }),
        },
        async ({ text, operation }) => {
          switch (operation) {
            case 'count_words':
              return `Word count: ${text.split(' ').length}`;
            case 'uppercase':
              return text.toUpperCase();
            case 'lowercase':
              return text.toLowerCase();
            case 'reverse':
              return text.split('').reverse().join('');
            default:
              return text;
          }
        }
      ),
      new Tool(
        {
          name: 'data_fetcher',
          description: 'Fetch data from external sources',
          parameters: z.object({
            source: z.string(),
            query: z.string().optional(),
          }),
          timeout: 2000,
          retries: 2,
        },
        async ({ source, query }) => {
          // Simulate network delay
          await new Promise(resolve => setTimeout(resolve, 100));
          return `Data from ${source}${query ? ` for query: ${query}` : ''}`;
        }
      )
    ];

    memory = new Memory({
      type: 'buffer',
      maxMessages: 20,
    });

    executorAgent = new Agent({
      name: 'Test Executor',
      instructions: 'You are a test executor agent. Use the available tools to complete tasks efficiently.',
      role: 'executor',
      tools: mockTools,
      memory,
      maxIterations: testConfig.agents.maxIterations,
    });
  });

  test('should create executor agent with correct configuration', () => {
    expect(executorAgent.name).toBe('Test Executor');
    expect(executorAgent.role).toBe('executor');
    expect(executorAgent.tools).toHaveLength(3);
    expect(executorAgent.tools.map(t => t.name)).toEqual(['calculator', 'text_processor', 'data_fetcher']);
  });

  test('should execute simple task without tools', async () => {
    globalPerformanceMeasurer.start();
    
    const result = await executorAgent.run('Hello, please introduce yourself');
    
    const duration = globalPerformanceMeasurer.measure('simple_task_execution');
    
    assertAgentResult(result, ['finalOutput', 'finalState', 'steps', 'metrics']);
    assertPerformance(duration, testConfig.performance.maxExecutionTime);
    
    expect(result.finalOutput).toBeDefined();
    expect(result.steps.length).toBeGreaterThan(0);
    expect(result.metrics.totalSteps).toBeGreaterThan(0);
  });

  test('should execute task with tool usage', async () => {
    globalPerformanceMeasurer.start();
    
    const result = await executorAgent.run('Calculate 15 + 27 and tell me the result');
    
    const duration = globalPerformanceMeasurer.measure('tool_usage_execution');
    
    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 2); // Allow more time for tool usage
    
    expect(result.metrics.toolCalls).toBeGreaterThan(0);
    expect(result.finalOutput).toContain('42'); // Expected calculation result
  });

  test('should handle multiple tool calls in sequence', async () => {
    globalPerformanceMeasurer.start();
    
    const result = await executorAgent.run(
      'First calculate 10 * 5, then count the words in "hello world test", and finally fetch data from "api.example.com"'
    );
    
    const duration = globalPerformanceMeasurer.measure('multiple_tools_execution');
    
    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 3);
    
    expect(result.metrics.toolCalls).toBeGreaterThanOrEqual(3);
    expect(result.steps.filter(s => s.step === 'action')).toHaveLength(3);
  });

  test('should maintain conversation memory', async () => {
    // First interaction
    const result1 = await executorAgent.run('My name is Alice and I like mathematics');
    assertAgentResult(result1);

    // Second interaction - should remember previous context
    const result2 = await executorAgent.run('What is my name and what do I like?');
    assertAgentResult(result2);

    expect(result2.finalState.messages.length).toBeGreaterThan(result1.finalState.messages.length);
    expect(result2.finalOutput.toLowerCase()).toContain('alice');
    expect(result2.finalOutput.toLowerCase()).toContain('mathematics');
  });

  test('should handle tool errors gracefully', async () => {
    const errorTool = new Tool(
      {
        name: 'error_tool',
        description: 'A tool that always fails',
        parameters: z.object({ input: z.string() }),
        retries: 2,
      },
      async () => {
        throw new Error('Simulated tool error');
      }
    );

    const agentWithErrorTool = new Agent({
      name: 'Error Test Agent',
      instructions: 'Test agent with error-prone tool',
      tools: [errorTool],
      maxIterations: 3,
    });

    const result = await agentWithErrorTool.run('Use the error tool');
    
    assertAgentResult(result);
    expect(result.steps.some(s => s.content.includes('Error'))).toBe(true);
  });

  test('should respect maximum iterations limit', async () => {
    const limitedAgent = new Agent({
      name: 'Limited Agent',
      instructions: 'Keep trying to use tools indefinitely',
      tools: mockTools,
      maxIterations: 2,
    });

    globalPerformanceMeasurer.start();
    
    const result = await limitedAgent.run('Keep calculating different numbers forever');
    
    const duration = globalPerformanceMeasurer.measure('iteration_limit_test');
    
    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime);
    
    expect(result.steps.length).toBeLessThanOrEqual(6); // 2 iterations * 3 steps max per iteration
  });

  test('should handle concurrent executions', async () => {
    const promises = Array.from({ length: 3 }, (_, i) => 
      executorAgent.run(`Calculate ${i + 1} * 10`)
    );

    globalPerformanceMeasurer.start();
    
    const results = await Promise.all(promises);
    
    const duration = globalPerformanceMeasurer.measure('concurrent_executions');
    
    results.forEach((result, i) => {
      assertAgentResult(result);
      expect(result.finalOutput).toContain((i + 1) * 10);
    });
    
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 2);
  });

  test('should provide detailed execution metrics', async () => {
    const result = await executorAgent.run('Calculate 5 + 5 and process the text "Hello World"');
    
    assertAgentResult(result);
    
    expect(result.metrics).toHaveProperty('totalSteps');
    expect(result.metrics).toHaveProperty('toolCalls');
    expect(result.metrics).toHaveProperty('executionTime');
    expect(result.metrics.totalSteps).toBeGreaterThan(0);
    expect(result.metrics.executionTime).toBeGreaterThan(0);
  });

  test('should handle empty or invalid inputs', async () => {
    const emptyResult = await executorAgent.run('');
    assertAgentResult(emptyResult);
    
    const whitespaceResult = await executorAgent.run('   ');
    assertAgentResult(whitespaceResult);
    
    const specialCharsResult = await executorAgent.run('!@#$%^&*()');
    assertAgentResult(specialCharsResult);
  });

  test('should convert agent to tool successfully', () => {
    const agentTool = executorAgent.asTool();
    
    expect(agentTool.name).toContain('agent_test_executor');
    expect(agentTool.description).toContain('Test Executor');
    expect(agentTool.parameters).toBeDefined();
    expect(typeof agentTool.execute).toBe('function');
  });

  test('should maintain performance under load', async () => {
    const iterations = 10;
    const durations: number[] = [];

    for (let i = 0; i < iterations; i++) {
      globalPerformanceMeasurer.start();
      
      await executorAgent.run(`Calculate ${i} + ${i + 1}`);
      
      const duration = globalPerformanceMeasurer.measure(`load_test_${i}`);
      durations.push(duration);
    }

    // Check that performance doesn't degrade significantly
    const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    const maxDuration = Math.max(...durations);
    
    expect(avgDuration).toBeLessThan(testConfig.performance.maxExecutionTime);
    expect(maxDuration).toBeLessThan(testConfig.performance.maxExecutionTime * 2);
    
    // Performance should be relatively consistent
    const variance = durations.reduce((sum, d) => sum + Math.pow(d - avgDuration, 2), 0) / durations.length;
    expect(Math.sqrt(variance)).toBeLessThan(avgDuration * 0.5); // Standard deviation < 50% of mean
  });
});
