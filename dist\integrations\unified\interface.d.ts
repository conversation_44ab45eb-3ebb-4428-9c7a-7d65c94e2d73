import { MCPMessage, MCPAssistantMessage, AgentState, NodeFunction } from "../../core/types";
/**
 * Unified tool definition that works across providers
 */
export interface UnifiedTool {
    name: string;
    description: string;
    parameters: {
        type: 'object';
        properties: Record<string, any>;
        required?: string[];
    };
}
/**
 * Unified configuration for LLM providers
 */
export interface UnifiedLLMConfig {
    provider: 'openai' | 'anthropic';
    model?: string;
    temperature?: number;
    maxTokens?: number;
    timeout?: number;
    apiKey?: string;
    baseURL?: string;
}
/**
 * Options for LLM requests
 */
export interface UnifiedLLMOptions {
    systemMessage?: string;
    tools?: UnifiedTool[];
    toolChoice?: 'auto' | 'none' | {
        name: string;
    };
    stream?: boolean;
    responseFormat?: 'text' | 'json';
}
/**
 * Streaming callback types
 */
export type StreamChunkCallback = (chunk: string) => void;
export type StreamEventCallback = (event: any) => void;
/**
 * Unified LLM client interface
 */
export interface UnifiedLLMClient {
    /**
     * Create a completion
     */
    createCompletion(messages: MCPMessage[], options?: UnifiedLLMOptions): Promise<MCPAssistantMessage>;
    /**
     * Create a streaming completion
     */
    createStreamingCompletion(messages: MCPMessage[], options?: UnifiedLLMOptions, onChunk?: StreamChunkCallback, onEvent?: StreamEventCallback): Promise<MCPAssistantMessage>;
    /**
     * Create an agent node
     */
    createAgentNode<TState extends AgentState>(options?: UnifiedLLMOptions): NodeFunction<TState>;
    /**
     * Test connection
     */
    testConnection(): Promise<boolean>;
    /**
     * Get provider name
     */
    getProvider(): string;
    /**
     * Get available models
     */
    getAvailableModels(): Promise<string[]>;
}
/**
 * Factory function to create unified LLM clients
 */
export declare function createUnifiedLLMClient(config: UnifiedLLMConfig): Promise<UnifiedLLMClient>;
/**
 * Helper function to create a simple unified client
 */
export declare function createSimpleLLMClient(provider: 'openai' | 'anthropic', apiKey?: string, model?: string): Promise<UnifiedLLMClient>;
//# sourceMappingURL=interface.d.ts.map