{"version": 3, "file": "multi-agent-system.js", "sourceRoot": "", "sources": ["../../src/examples/multi-agent-system.ts"], "names": [], "mappings": ";AAAA,qCAAqC;;;AAErC,kCAAmC;AACnC,gCAAwC;AACxC,4CAQsB;AAEtB;;;GAGG;AAEH,+CAA+C;AAE/C,MAAM,aAAa,GAAG;IACpB,SAAS,EAAE,KAAK,EAAE,IAA4C,EAAE,EAAE;QAChE,MAAM,EAAE,KAAK,EAAE,UAAU,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;QAE9C,sBAAsB;QACtB,MAAM,OAAO,GAAG;YACd,EAAE,KAAK,EAAE,GAAG,KAAK,aAAa,EAAE,GAAG,EAAE,uBAAuB,EAAE,OAAO,EAAE,6BAA6B,KAAK,EAAE,EAAE;YAC7G,EAAE,KAAK,EAAE,GAAG,KAAK,oBAAoB,EAAE,GAAG,EAAE,uBAAuB,EAAE,OAAO,EAAE,sBAAsB,KAAK,EAAE,EAAE;YAC7G,EAAE,KAAK,EAAE,GAAG,KAAK,mBAAmB,EAAE,GAAG,EAAE,uBAAuB,EAAE,OAAO,EAAE,sBAAsB,KAAK,EAAE,EAAE;SAC7G,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QAEvB,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;IAC5C,CAAC;IAED,WAAW,EAAE,KAAK,EAAE,IAA6C,EAAE,EAAE;QACnE,MAAM,EAAE,IAAI,EAAE,YAAY,GAAG,SAAS,EAAE,GAAG,IAAI,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,sBAAsB,YAAY,WAAW,CAAC,CAAC;QAE3D,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,YAAY;YACZ,QAAQ,EAAE,CAAC,qBAAqB,IAAI,EAAE,EAAE,sBAAsB,IAAI,EAAE,EAAE,2BAA2B,IAAI,EAAE,CAAC;YACxG,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;IACL,CAAC;CACF,CAAC;AAEF,MAAM,YAAY,GAAG;IACnB,eAAe,EAAE,KAAK,EAAE,IAAwD,EAAE,EAAE;QAClF,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,cAAc,EAAE,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,mBAAmB,KAAK,EAAE,CAAC,CAAC;QAE9D,OAAO,KAAK,KAAK;;YAET,KAAK,gBAAgB,KAAK,wCAAwC,MAAM;;;0BAG1D,KAAK;mCACI,KAAK;2BACb,KAAK;;;EAG9B,KAAK,2FAA2F,CAAC;IACjG,CAAC;IAED,WAAW,EAAE,KAAK,EAAE,IAAgD,EAAE,EAAE;QACtE,MAAM,EAAE,OAAO,EAAE,YAAY,GAAG,iBAAiB,EAAE,GAAG,IAAI,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,yCAAyC,YAAY,EAAE,CAAC,CAAC;QAErE,OAAO,YAAY,OAAO,8DAA8D,YAAY,GAAG,CAAC;IAC1G,CAAC;CACF,CAAC;AAEF,MAAM,gBAAgB,GAAG;IACvB,SAAS,EAAE,KAAK,EAAE,IAA4B,EAAE,EAAE;QAChD,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,mBAAmB,UAAU,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC;YACH,+DAA+D;YAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC,CAAC;YACjE,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED,cAAc,EAAE,KAAK,EAAE,IAA0C,EAAE,EAAE;QACnE,MAAM,EAAE,OAAO,EAAE,IAAI,GAAG,OAAO,EAAE,GAAG,IAAI,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,WAAW,CAAC,CAAC;QAEtD,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/C,MAAM,GAAG,GAAG,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;QACjC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC;QAEjC,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,IAAI;YACJ,OAAO,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE;YAC1D,QAAQ,EAAE,CAAC,oBAAoB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,oBAAoB,GAAG,OAAO,GAAG,EAAE,CAAC;SACtF,CAAC,CAAC;IACL,CAAC;CACF,CAAC;AAEF,8CAA8C;AAE9C,MAAM,kBAAkB,GAAG,GAAkB,EAAE,CAAC;IAC9C;QACE,EAAE,EAAE,YAAY;QAChB,IAAI,EAAE,qBAAqB;QAC3B,WAAW,EAAE,mDAAmD;QAChE,YAAY,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,CAAC;QACrE,eAAe,EAAE,CAAC,iBAAiB,EAAE,sBAAsB,EAAE,gBAAgB,CAAC;QAC9E,KAAK,EAAE,IAAA,+BAAmB,EAAC,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC;KACrD;IACD;QACE,EAAE,EAAE,QAAQ;QACZ,IAAI,EAAE,gBAAgB;QACtB,WAAW,EAAE,wCAAwC;QACrD,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,kBAAkB,EAAE,WAAW,CAAC;QACrE,eAAe,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,SAAS,CAAC;QACnE,KAAK,EAAE,IAAA,+BAAmB,EAAC,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;KACpD;IACD;QACE,EAAE,EAAE,SAAS;QACb,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,iDAAiD;QAC9D,YAAY,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,SAAS,EAAE,uBAAuB,CAAC;QAClF,eAAe,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,EAAE,aAAa,CAAC;QAC9E,KAAK,EAAE,IAAA,+BAAmB,EAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC;KACxD;CACF,CAAC;AAEF,oDAAoD;AAEpD,MAAM,iBAAiB,GAAG,KAAK,IAAI,EAAE;IACnC,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAEzD,MAAM,QAAQ,GAAG,sJAAsJ,CAAC;IAExK,OAAO,CAAC,GAAG,CAAC,iBAAiB,QAAQ,IAAI,CAAC,CAAC;IAE3C,yBAAyB;IACzB,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACpC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7B,MAAM,YAAY,GAAG,IAAA,8BAAkB,EAAoB;QACzD,aAAa,EAAE,mJAAmJ;KACnK,CAAC,CAAC;IAEH,MAAM,eAAe,GAAG,IAAI,eAAQ,CAAC,YAAY,CAAC,CAAC;IACnD,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC,OAAO,CAAC;QACnD,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;KAChD,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,mCAAmC,cAAc,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC;IAE5F,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;QACzB,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QACvD,OAAO;IACT,CAAC;IAED,8BAA8B;IAC9B,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7B,MAAM,OAAO,GAAG,kBAAkB,EAAE,CAAC;IACrC,MAAM,iBAAiB,GAAG,IAAA,mCAAuB,EAAyB;QACxE,OAAO;QACP,aAAa,EAAE,6JAA6J;KAC7K,CAAC,CAAC;IAEH,MAAM,oBAAoB,GAAG,IAAI,eAAQ,CAAC,iBAAiB,CAAC,CAAC;IAE7D,0CAA0C;IAC1C,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,MAAM,mBAAmB,GAAG,MAAM,oBAAoB,CAAC,mBAAmB,CACxE;QACE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,sBAAsB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;QAClG,IAAI,EAAE,cAAc,CAAC,IAAI;KAC1B,EACD,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;QACf,SAAS,EAAE,CAAC;QACZ,MAAM,OAAO,GAAG,IAAA,oBAAc,EAAC,KAAK,CAAC,CAAC;QAEtC,IAAI,OAAO,EAAE,IAAI,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,QAAQ,SAAS,KAAK,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,0BAA0B;QAC1B,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,SAAS,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM,CAAC;YAC1F,MAAM,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,gBAAgB,SAAS,IAAI,KAAK,kBAAkB,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,cAAc;IAC/B,CAAC,CACF,CAAC;IAEF,0BAA0B;IAC1B,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7B,MAAM,YAAY,GAAG,IAAA,oBAAc,EAAC,mBAAmB,CAAC,CAAC;IACzD,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAChC,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,IAAI,2BAA2B,CAAC,CAAC;IAElE,4BAA4B;IAC5B,IAAI,mBAAmB,CAAC,cAAc,EAAE,CAAC;QACvC,MAAM,KAAK,GAAG;YACZ,KAAK,EAAE,mBAAmB,CAAC,cAAc,CAAC,MAAM;YAChD,SAAS,EAAE,mBAAmB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM;YAC1F,MAAM,EAAE,mBAAmB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM;YACpF,OAAO,EAAE,mBAAmB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM;SACvF,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,mBAAmB,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACzF,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;AAC7D,CAAC,CAAC;AA2CO,8CAAiB;AAzC1B,4CAA4C;AAE5C,MAAM,yBAAyB,GAAG,KAAK,IAAI,EAAE;IAC3C,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE1D,6BAA6B;IAC7B,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACnD,MAAM,cAAc,GAAG,IAAA,+BAAmB,EAAC;QACzC,KAAK,EAAE;YACL,KAAK,EAAE,KAAK,EAAE,IAAsB,EAAE,EAAE,CAAC,UAAU,IAAI,CAAC,IAAI,GAAG;SAChE;KACF,CAAC,CAAC;IAEH,MAAM,cAAc,GAAG,MAAM,IAAI,eAAQ,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC;QAChE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,oBAAoB,EAAE,CAAC;KAC5D,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,IAAA,oBAAc,EAAC,cAAc,CAAC,EAAE,OAAO,CAAC,CAAC;IAEhE,4BAA4B;IAC5B,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACpD,MAAM,aAAa,GAAG,IAAA,8BAAkB,GAAE,CAAC;IAE3C,MAAM,aAAa,GAAG,MAAM,IAAI,eAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC;QAC9D,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC;KAC7E,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAEtF,+BAA+B;IAC/B,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,MAAM,gBAAgB,GAAG,IAAA,+BAAmB,GAAE,CAAC;IAE/C,MAAM,cAAc,GAAG,MAAM,IAAI,eAAQ,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC;QAClE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC;KAC7E,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AACrF,CAAC,CAAC;AAG0B,8DAAyB;AAErD,wCAAwC;AACxC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,iBAAiB,EAAE;SAChB,IAAI,CAAC,GAAG,EAAE,CAAC,yBAAyB,EAAE,CAAC;SACvC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC1B,CAAC"}