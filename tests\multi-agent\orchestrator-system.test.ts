import { describe, test, expect, beforeEach } from '@jest/globals';
import { z } from 'zod';
import { 
  Agent, 
  Graph, 
  Tool, 
  Memory, 
  createTestState, 
  assertAgentResult, 
  assertPerformance,
  globalPerformanceMeasurer,
  testData,
  testConfig
} from '../setup.js';

/**
 * Test Suite 4: Multi-Agent Orchestrator System
 * 
 * Tests complex multi-agent orchestration including:
 * - Agent handoffs and coordination
 * - Hierarchical agent management
 * - Task routing and delegation
 * - Parallel agent execution
 * - Cross-agent communication
 * - System-level performance and reliability
 */

describe('Multi-Agent Orchestrator System Tests', () => {
  let orchestratorAgent: Agent;
  let plannerAgent: Agent;
  let executorAgent: Agent;
  let analystAgent: Agent;
  let criticAgent: Agent;
  let orchestrationGraph: Graph<any>;
  let sharedMemory: Memory;

  beforeEach(async () => {
    // Create shared memory for cross-agent communication
    sharedMemory = new Memory({
      type: 'buffer',
      maxMessages: 50,
    });

    // Create specialized agents
    plannerAgent = new Agent({
      name: 'Strategic Planner',
      instructions: 'Create detailed plans and break down complex tasks into manageable steps.',
      role: 'planner',
      tools: [
        new Tool(
          {
            name: 'create_plan',
            description: 'Create a structured plan for a given objective',
            parameters: z.object({
              objective: z.string(),
              complexity: z.enum(['simple', 'moderate', 'complex']),
            }),
          },
          async ({ objective, complexity }) => {
            const steps = {
              simple: 3,
              moderate: 5,
              complex: 8
            };
            
            const planSteps = Array.from({ length: steps[complexity] }, (_, i) => 
              `Step ${i + 1}: ${objective.split(' ').slice(0, 3).join(' ')} phase ${i + 1}`
            );
            
            return `Plan for "${objective}":\n${planSteps.map((step, i) => `${i + 1}. ${step}`).join('\n')}`;
          }
        )
      ],
      memory: sharedMemory,
      maxIterations: 4,
    });

    executorAgent = new Agent({
      name: 'Task Executor',
      instructions: 'Execute specific tasks and operations efficiently.',
      role: 'executor',
      tools: [
        new Tool(
          {
            name: 'execute_task',
            description: 'Execute a specific task',
            parameters: z.object({
              task: z.string(),
              priority: z.enum(['low', 'medium', 'high']).default('medium'),
            }),
          },
          async ({ task, priority }) => {
            const executionTime = { low: 100, medium: 200, high: 300 }[priority];
            await new Promise(resolve => setTimeout(resolve, executionTime));
            return `Task executed: ${task} (Priority: ${priority})`;
          }
        ),
        new Tool(
          {
            name: 'validate_completion',
            description: 'Validate that a task has been completed successfully',
            parameters: z.object({
              task: z.string(),
              criteria: z.array(z.string()),
            }),
          },
          async ({ task, criteria }) => {
            const validationResults = criteria.map(criterion => ({
              criterion,
              passed: Math.random() > 0.2 // 80% pass rate
            }));
            
            const allPassed = validationResults.every(r => r.passed);
            return `Validation for "${task}":\n${validationResults.map(r => 
              `- ${r.criterion}: ${r.passed ? 'PASS' : 'FAIL'}`
            ).join('\n')}\nOverall: ${allPassed ? 'SUCCESS' : 'NEEDS_REVIEW'}`;
          }
        )
      ],
      memory: sharedMemory,
      maxIterations: 3,
    });

    analystAgent = new Agent({
      name: 'Performance Analyst',
      instructions: 'Analyze performance data and provide insights.',
      role: 'analyst',
      tools: [
        new Tool(
          {
            name: 'analyze_metrics',
            description: 'Analyze performance metrics',
            parameters: z.object({
              metrics: z.record(z.number()),
              timeframe: z.string(),
            }),
          },
          async ({ metrics, timeframe }) => {
            const analysis = Object.entries(metrics).map(([metric, value]) => {
              const status = value > 0.8 ? 'Excellent' : value > 0.6 ? 'Good' : value > 0.4 ? 'Fair' : 'Poor';
              return `${metric}: ${value} (${status})`;
            });
            
            return `Performance Analysis (${timeframe}):\n${analysis.join('\n')}`;
          }
        )
      ],
      memory: sharedMemory,
      maxIterations: 3,
    });

    criticAgent = new Agent({
      name: 'Quality Critic',
      instructions: 'Review and critique work quality, providing feedback for improvement.',
      role: 'critic',
      tools: [
        new Tool(
          {
            name: 'quality_review',
            description: 'Review the quality of completed work',
            parameters: z.object({
              workItem: z.string(),
              criteria: z.array(z.string()),
            }),
          },
          async ({ workItem, criteria }) => {
            const scores = criteria.map(criterion => ({
              criterion,
              score: Math.random() * 0.4 + 0.6 // Score between 0.6-1.0
            }));
            
            const avgScore = scores.reduce((sum, s) => sum + s.score, 0) / scores.length;
            const feedback = avgScore > 0.8 ? 'Excellent work' : avgScore > 0.7 ? 'Good with minor improvements needed' : 'Requires significant improvements';
            
            return `Quality Review for "${workItem}":\n${scores.map(s => 
              `- ${s.criterion}: ${s.score.toFixed(2)}`
            ).join('\n')}\nAverage Score: ${avgScore.toFixed(2)}\nFeedback: ${feedback}`;
          }
        )
      ],
      memory: sharedMemory,
      maxIterations: 2,
    });

    // Create orchestrator with handoff capabilities
    orchestratorAgent = new Agent({
      name: 'System Orchestrator',
      instructions: `You are the system orchestrator responsible for coordinating multiple specialized agents.
        
        Available agents:
        - Strategic Planner: Creates plans and breaks down complex tasks
        - Task Executor: Executes specific tasks and operations
        - Performance Analyst: Analyzes metrics and provides insights
        - Quality Critic: Reviews work quality and provides feedback
        
        Route tasks to the appropriate agents based on the request type.
        Coordinate handoffs between agents when needed.
        Ensure all work meets quality standards before completion.`,
      role: 'orchestrator',
      tools: [
        new Tool(
          {
            name: 'route_task',
            description: 'Route a task to the appropriate agent',
            parameters: z.object({
              task: z.string(),
              targetAgent: z.enum(['planner', 'executor', 'analyst', 'critic']),
              priority: z.enum(['low', 'medium', 'high']).default('medium'),
            }),
          },
          async ({ task, targetAgent, priority }) => {
            return `Task routed to ${targetAgent}: "${task}" (Priority: ${priority})`;
          }
        )
      ],
      handoffs: [plannerAgent, executorAgent, analystAgent, criticAgent],
      memory: sharedMemory,
      maxIterations: 6,
    });

    // Create orchestration graph
    orchestrationGraph = new Graph()
      .addNode('orchestrator', async (state) => {
        const result = await orchestratorAgent.run(state);
        return result.finalState;
      })
      .addNode('planner', async (state) => {
        const result = await plannerAgent.run(state);
        return result.finalState;
      })
      .addNode('executor', async (state) => {
        const result = await executorAgent.run(state);
        return result.finalState;
      })
      .addNode('analyst', async (state) => {
        const result = await analystAgent.run(state);
        return result.finalState;
      })
      .addNode('critic', async (state) => {
        const result = await criticAgent.run(state);
        return result.finalState;
      })
      .setEntryPoint('orchestrator')
      .addConditionalEdges('orchestrator', (state) => {
        const lastMessage = state.messages[state.messages.length - 1]?.content || '';
        if (lastMessage.includes('plan')) return 'planner';
        if (lastMessage.includes('execute')) return 'executor';
        if (lastMessage.includes('analyze')) return 'analyst';
        if (lastMessage.includes('review')) return 'critic';
        return 'END';
      }, {
        planner: 'executor',
        executor: 'critic',
        analyst: 'END',
        critic: 'END',
        END: 'END'
      })
      .addEdge('planner', 'executor')
      .addEdge('executor', 'critic')
      .addEdge('analyst', 'END')
      .addEdge('critic', 'END');

    // Add parallel execution configuration
    orchestrationGraph.addParallel('parallel_analysis', {
      nodes: ['analyst', 'critic'],
      mergeStrategy: 'all',
      timeout: 10000,
    });

    // Add retry configuration
    orchestrationGraph.addRetry('executor', {
      maxAttempts: 3,
      backoffMs: 1000,
      exponential: true,
      retryCondition: (error) => error.message.includes('temporary'),
    });

    orchestrationGraph.compile();
  });

  test('should create orchestrator system with all agents', () => {
    expect(orchestratorAgent.name).toBe('System Orchestrator');
    expect(orchestratorAgent.handoffs).toHaveLength(4);
    expect(plannerAgent.name).toBe('Strategic Planner');
    expect(executorAgent.name).toBe('Task Executor');
    expect(analystAgent.name).toBe('Performance Analyst');
    expect(criticAgent.name).toBe('Quality Critic');
  });

  test('should route tasks to appropriate agents', async () => {
    globalPerformanceMeasurer.start();
    
    const result = await orchestratorAgent.run(
      'I need to create a comprehensive plan for launching a new product feature'
    );
    
    const duration = globalPerformanceMeasurer.measure('task_routing');
    
    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 2);
    
    expect(result.finalOutput.toLowerCase()).toContain('plan');
    expect(result.finalState.metadata?.handoffTarget).toBeDefined();
  });

  test('should execute complete workflow through graph', async () => {
    globalPerformanceMeasurer.start();
    
    const initialState = createTestState({
      messages: [{ role: 'user', content: 'Plan and execute a database optimization project' }],
      metadata: { workflowType: 'complete' }
    });
    
    const result = await orchestrationGraph.run(initialState);
    
    const duration = globalPerformanceMeasurer.measure('complete_workflow');
    
    expect(result).toBeDefined();
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 4);
    
    expect(result.messages.length).toBeGreaterThan(1);
  });

  test('should handle agent handoffs correctly', async () => {
    // Start with planning request
    const planningResult = await orchestratorAgent.run(
      'Create a plan for implementing a new authentication system'
    );
    
    assertAgentResult(planningResult);
    
    // Should indicate handoff to planner
    expect(planningResult.finalState.metadata?.handoffTarget).toBe('Strategic Planner');
    
    // Execute the handoff
    if (planningResult.finalState.metadata?.handoffTarget) {
      const plannerResult = await plannerAgent.run(planningResult.finalState);
      assertAgentResult(plannerResult);
      expect(plannerResult.finalOutput.toLowerCase()).toContain('plan');
    }
  });

  test('should coordinate parallel agent execution', async () => {
    globalPerformanceMeasurer.start();
    
    // Create a state that triggers parallel execution
    const parallelState = createTestState({
      messages: [{ role: 'user', content: 'Analyze performance metrics and review code quality simultaneously' }],
      metadata: { executionMode: 'parallel' }
    });
    
    // Execute analyst and critic in parallel
    const [analystResult, criticResult] = await Promise.all([
      analystAgent.run('Analyze these performance metrics: {cpu: 0.8, memory: 0.6, response_time: 0.9}'),
      criticAgent.run('Review the quality of this implementation: "Database connection pooling with retry logic"')
    ]);
    
    const duration = globalPerformanceMeasurer.measure('parallel_execution');
    
    assertAgentResult(analystResult);
    assertAgentResult(criticResult);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 2);
    
    expect(analystResult.finalOutput.toLowerCase()).toContain('performance');
    expect(criticResult.finalOutput.toLowerCase()).toContain('quality');
  });

  test('should maintain shared context across agents', async () => {
    // Agent 1: Create initial context
    const planResult = await plannerAgent.run(
      'Create a plan for Project Alpha: mobile app development with 3-month timeline'
    );
    
    assertAgentResult(planResult);
    
    // Agent 2: Reference shared context
    const executorResult = await executorAgent.run(
      'Execute the first phase of Project Alpha that was planned earlier'
    );
    
    assertAgentResult(executorResult);
    
    // Both agents should have access to shared memory
    expect(executorResult.finalState.messages.length).toBeGreaterThan(planResult.finalState.messages.length);
    expect(executorResult.finalOutput.toLowerCase()).toContain('project alpha');
  });

  test('should handle complex multi-step workflows', async () => {
    globalPerformanceMeasurer.start();
    
    const workflowResult = await orchestratorAgent.run(`
      Coordinate a complete software release process:
      1. Plan the release strategy
      2. Execute the deployment steps
      3. Analyze the performance metrics
      4. Review the overall quality
      
      Ensure each step is completed before moving to the next.
    `);
    
    const duration = globalPerformanceMeasurer.measure('complex_workflow');
    
    assertAgentResult(workflowResult);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 3);
    
    expect(workflowResult.finalOutput.toLowerCase()).toContain('release');
    expect(workflowResult.metrics.totalSteps).toBeGreaterThan(3);
  });

  test('should handle agent failures gracefully', async () => {
    // Create a mock failing agent
    const failingAgent = new Agent({
      name: 'Failing Agent',
      instructions: 'This agent will fail',
      tools: [
        new Tool(
          {
            name: 'failing_tool',
            description: 'A tool that always fails',
            parameters: z.object({ input: z.string() }),
          },
          async () => {
            throw new Error('Simulated agent failure');
          }
        )
      ],
      maxIterations: 1,
    });

    // Add failing agent to orchestrator handoffs
    const testOrchestrator = new Agent({
      name: 'Test Orchestrator',
      instructions: 'Route to failing agent',
      handoffs: [failingAgent],
      maxIterations: 2,
    });

    const result = await testOrchestrator.run('Route this to the failing agent');
    
    assertAgentResult(result);
    // Should complete despite agent failure
    expect(result.finalOutput).toBeDefined();
  });

  test('should optimize performance under load', async () => {
    const concurrentTasks = 5;
    const tasks = Array.from({ length: concurrentTasks }, (_, i) => 
      `Task ${i + 1}: Process data batch ${i + 1}`
    );

    globalPerformanceMeasurer.start();
    
    const results = await Promise.all(
      tasks.map(task => orchestratorAgent.run(task))
    );
    
    const duration = globalPerformanceMeasurer.measure('load_performance');
    
    results.forEach(result => assertAgentResult(result));
    assertPerformance(duration, testConfig.performance.maxExecutionTime * concurrentTasks);
    
    // All tasks should complete successfully
    expect(results).toHaveLength(concurrentTasks);
  });

  test('should provide comprehensive system metrics', async () => {
    const result = await orchestratorAgent.run(
      'Coordinate a simple task and provide detailed metrics'
    );
    
    assertAgentResult(result);
    
    expect(result.metrics).toHaveProperty('totalSteps');
    expect(result.metrics).toHaveProperty('toolCalls');
    expect(result.metrics).toHaveProperty('executionTime');
    expect(result.metrics.executionTime).toBeGreaterThan(0);
  });

  test('should support dynamic agent configuration', async () => {
    // Add new agent dynamically
    const newAgent = new Agent({
      name: 'Dynamic Agent',
      instructions: 'Handle dynamic tasks',
      tools: [],
      maxIterations: 2,
    });

    // Update orchestrator handoffs
    orchestratorAgent.handoffs.push(newAgent);
    
    const result = await orchestratorAgent.run('Route to the new dynamic agent');
    
    assertAgentResult(result);
    expect(result.finalOutput).toBeDefined();
  });

  test('should maintain system reliability over extended operation', async () => {
    const iterations = 10;
    const successCount = { value: 0 };
    
    for (let i = 0; i < iterations; i++) {
      try {
        const result = await orchestratorAgent.run(`Iteration ${i + 1}: Simple coordination task`);
        if (result.finalOutput) {
          successCount.value++;
        }
      } catch (error) {
        console.warn(`Iteration ${i + 1} failed:`, error);
      }
    }
    
    // Should maintain high reliability (>90% success rate)
    const successRate = successCount.value / iterations;
    expect(successRate).toBeGreaterThan(0.9);
  });

  test('should handle resource constraints gracefully', async () => {
    // Simulate resource constraints by limiting iterations
    const constrainedOrchestrator = new Agent({
      name: 'Constrained Orchestrator',
      instructions: 'Work within resource constraints',
      handoffs: [executorAgent],
      maxIterations: 1, // Very limited
    });

    const result = await constrainedOrchestrator.run(
      'Complete a complex task with limited resources'
    );
    
    assertAgentResult(result);
    
    // Should complete within constraints
    expect(result.metrics.totalSteps).toBeLessThanOrEqual(3);
    expect(result.finalOutput).toBeDefined();
  });
});
