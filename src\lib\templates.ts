// src/lib/templates.ts

import { Graph } from '../core/graph';
import { Executor } from '../core/executor';
import { AgentState, MCPMessage, MCPAssistantMessage, NodeFunction } from '../core/types';

/**
 * Tool function type for executor agents
 */
export type Tool = (args: any) => any | Promise<any>;

/**
 * Create an executor agent that can use tools to complete tasks
 * 
 * This is a fundamental pattern in AG3NTIC where an LLM agent can:
 * 1. Receive a user request
 * 2. Decide which tools to call
 * 3. Execute the tools
 * 4. Continue the conversation with the results
 * 
 * @param llmNode - The LLM node function that generates responses and tool calls
 * @param tools - Map of tool names to tool functions
 * @returns A graph that implements the executor pattern
 */
export const createExecutorAgent = <TState extends AgentState>(
  llmNode: NodeFunction<TState>,
  tools: Record<string, Tool>
): Graph<TState> => {
  
  const toolExecutorNode: NodeFunction<TState> = async (state: TState) => {
    const lastMessage = state.messages[state.messages.length - 1] as MCPAssistantMessage;
    if (!lastMessage.tool_calls) {
      throw new Error("Tool node called but no tool_calls found in the last message.");
    }

    const toolCall = lastMessage.tool_calls[0];
    const toolName = toolCall.function.name;
    const tool = tools[toolName];

    if (!tool) {
      throw new Error(`Tool "${toolName}" is not defined in the tool map.`);
    }

    console.log(`\n🛠️  Executing Tool: ${toolName}`);
    const args = JSON.parse(toolCall.function.arguments);
    const result = await Promise.resolve(tool(args));

    const newMessages: MCPMessage[] = [
      ...state.messages,
      { role: 'tool', tool_call_id: toolCall.id, content: JSON.stringify(result, null, 2) }
    ];

    return { messages: newMessages } as Partial<TState>;
  };

  const shouldCallTool = (state: TState): 'call_tool' | '__end__' => {
    const lastMessage = state.messages[state.messages.length - 1];
    return lastMessage.role === 'assistant' && lastMessage.tool_calls ? 'call_tool' : '__end__';
  };

  return new Graph<TState>()
    .addNode('agent', llmNode)
    .addNode('tools', toolExecutorNode)
    .setEntryPoint('agent')
    .addConditionalEdge('agent', shouldCallTool, {
      'call_tool': 'tools',
      '__end__': '__END__'
    })
    .addEdge('tools', 'agent');
};

/**
 * Plan step interface for planner agents
 */
export interface PlanStep {
  id: string;
  description: string;
  dependencies?: string[];
  estimatedTime?: number;
  priority?: 'low' | 'medium' | 'high';
  status?: 'pending' | 'in_progress' | 'completed' | 'failed';
}

/**
 * Extended state for planner agents
 */
export interface PlannerAgentState extends AgentState {
  plan?: PlanStep[];
  currentStep?: number;
  planningStrategy?: 'sequential' | 'parallel' | 'adaptive';
}

/**
 * Create a planner agent that breaks down complex goals into actionable steps
 * 
 * This agent specializes in:
 * 1. Analyzing complex goals or requests
 * 2. Breaking them down into logical, sequential steps
 * 3. Identifying dependencies between steps
 * 4. Creating a structured execution plan
 * 
 * @param llmNode - The LLM node function that generates plans
 * @param options - Configuration options for the planner
 * @returns A graph that implements the planner pattern
 */
export const createPlannerAgent = <TState extends PlannerAgentState>(
  llmNode: NodeFunction<TState>,
  options: {
    maxSteps?: number;
    strategy?: 'sequential' | 'parallel' | 'adaptive';
    includeTimeEstimates?: boolean;
  } = {}
): Graph<TState> => {
  
  const {
    maxSteps = 10,
    strategy = 'sequential',
    includeTimeEstimates = false
  } = options;

  const plannerNode: NodeFunction<TState> = async (state: TState) => {
    const lastMessage = state.messages[state.messages.length - 1];
    
    if (lastMessage.role !== 'user') {
      throw new Error("Planner node expects a user message with the goal to plan for.");
    }

    console.log(`\n📋 Planning Strategy: ${strategy}`);
    console.log(`📋 Max Steps: ${maxSteps}`);
    
    // Call the LLM to generate a response
    const llmResponse = await llmNode(state);
    const updatedState = { ...state, ...llmResponse };
    
    // Extract plan from the LLM response (this is a simplified version)
    // In a real implementation, you'd parse the LLM response to extract structured plan data
    const plan = generateMockPlan(lastMessage.content, strategy, maxSteps, includeTimeEstimates);
    
    return {
      ...updatedState,
      plan,
      currentStep: 0,
      planningStrategy: strategy
    } as Partial<TState>;
  };

  return new Graph<TState>()
    .addNode('planner', plannerNode)
    .setEntryPoint('planner')
    .addEdge('planner', '__END__');
};

/**
 * Generate a mock plan for demonstration purposes
 * In a real implementation, this would be replaced by LLM-generated structured output
 */
function generateMockPlan(
  goal: string,
  strategy: string,
  maxSteps: number,
  includeTimeEstimates: boolean
): PlanStep[] {
  const steps: PlanStep[] = [];

  // Simple heuristic-based planning for demo
  console.log(`📋 Generating ${strategy} plan for: ${goal}`);
  const keywords = goal.toLowerCase().split(' ');
  
  if (keywords.includes('research') || keywords.includes('analyze')) {
    steps.push({
      id: 'research-1',
      description: 'Gather initial information and define scope',
      priority: 'high',
      status: 'pending',
      ...(includeTimeEstimates && { estimatedTime: 30 })
    });
  }
  
  if (keywords.includes('plan') || keywords.includes('design')) {
    const designStep: PlanStep = {
      id: 'design-1',
      description: 'Create detailed design and specifications',
      priority: 'high',
      status: 'pending'
    };

    if (steps.length > 0) {
      designStep.dependencies = [steps[0].id];
    }

    if (includeTimeEstimates) {
      designStep.estimatedTime = 60;
    }

    steps.push(designStep);
  }
  
  if (keywords.includes('implement') || keywords.includes('build') || keywords.includes('create')) {
    const implementStep: PlanStep = {
      id: 'implement-1',
      description: 'Implement the solution according to specifications',
      priority: 'medium',
      status: 'pending'
    };

    if (steps.length > 0) {
      implementStep.dependencies = [steps[steps.length - 1].id];
    }

    if (includeTimeEstimates) {
      implementStep.estimatedTime = 120;
    }

    steps.push(implementStep);
  }
  
  if (keywords.includes('test') || keywords.includes('validate')) {
    const testStep: PlanStep = {
      id: 'test-1',
      description: 'Test and validate the implementation',
      priority: 'medium',
      status: 'pending'
    };

    if (steps.length > 0) {
      testStep.dependencies = [steps[steps.length - 1].id];
    }

    if (includeTimeEstimates) {
      testStep.estimatedTime = 45;
    }

    steps.push(testStep);
  }
  
  // Always add a completion step
  const completionStep: PlanStep = {
    id: 'complete-1',
    description: 'Review results and finalize deliverables',
    priority: 'low',
    status: 'pending'
  };

  if (steps.length > 0) {
    completionStep.dependencies = [steps[steps.length - 1].id];
  }

  if (includeTimeEstimates) {
    completionStep.estimatedTime = 15;
  }

  steps.push(completionStep);
  
  return steps.slice(0, maxSteps);
}

/**
 * Worker agent interface for orchestrator agents
 */
export interface WorkerAgent {
  id: string;
  name: string;
  description: string;
  capabilities: string[];
  specializations?: string[];
  graph: Graph<any>;
  isAvailable?: boolean;
}

/**
 * Step execution tracking for orchestrator agents
 */
export interface StepExecution {
  stepId: string;
  assignedWorker?: string;
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'failed';
  startTime?: Date;
  endTime?: Date;
  result?: any;
  error?: string;
}

/**
 * Extended state for orchestrator agents
 */
export interface OrchestratorAgentState extends PlannerAgentState {
  workers?: WorkerAgent[];
  stepExecutions?: StepExecution[];
  orchestrationStatus?: 'planning' | 'executing' | 'completed' | 'failed';
  currentStepIndex?: number;
}

/**
 * Create an orchestrator agent that coordinates multiple worker agents
 * 
 * This agent specializes in:
 * 1. Taking a plan and coordinating its execution
 * 2. Assigning tasks to the most suitable worker agents
 * 3. Managing dependencies between tasks
 * 4. Monitoring progress and handling failures
 * 5. Synthesizing results from multiple workers
 * 
 * @param llmNode - The LLM node function that makes orchestration decisions
 * @param workers - Array of available worker agents
 * @param options - Configuration options for the orchestrator
 * @returns A graph that implements the orchestrator pattern
 */
export const createOrchestratorAgent = <TState extends OrchestratorAgentState>(
  _llmNode: NodeFunction<TState>,
  workers: WorkerAgent[],
  options: {
    maxConcurrentTasks?: number;
    retryFailedTasks?: boolean;
    timeoutPerTask?: number;
  } = {}
): Graph<TState> => {

  const {
    maxConcurrentTasks = 3,
    timeoutPerTask = 300000 // 5 minutes
  } = options;

  // Note: retryFailedTasks would be used in a more sophisticated implementation
  const retryFailedTasks = options.retryFailedTasks ?? true;
  console.log(`🎭 Orchestrator configured with retry: ${retryFailedTasks}`);

  const orchestratorNode: NodeFunction<TState> = async (state: TState) => {
    console.log(`\n🎭 Orchestrator coordinating ${workers.length} workers`);
    console.log(`🎭 Max concurrent tasks: ${maxConcurrentTasks}`);
    
    if (!state.plan || state.plan.length === 0) {
      throw new Error("Orchestrator requires a plan to execute. Use a planner agent first.");
    }

    // Initialize step executions if not present
    if (!state.stepExecutions) {
      const stepExecutions: StepExecution[] = state.plan.map(step => ({
        stepId: step.id,
        status: 'pending'
      }));
      
      return {
        stepExecutions,
        orchestrationStatus: 'executing' as const,
        currentStepIndex: 0,
        workers
      } as Partial<TState>;
    }

    // Find next executable step
    const nextStep = findNextExecutableStep(state.stepExecutions, state.plan);
    
    if (!nextStep) {
      // All steps completed or no more executable steps
      const allCompleted = state.stepExecutions.every(exec => exec.status === 'completed');
      
      if (allCompleted) {
        console.log("🎉 All steps completed successfully!");
        return {
          orchestrationStatus: 'completed' as const
        } as Partial<TState>;
      } else {
        console.log("⚠️  No more executable steps, but not all completed");
        return {
          orchestrationStatus: 'failed' as const
        } as Partial<TState>;
      }
    }

    // Assign worker to the step
    const assignedWorker = assignWorkerToStep(nextStep.step, workers);
    
    if (!assignedWorker) {
      console.log(`❌ No suitable worker found for step: ${nextStep.step.description}`);
      
      const updatedExecutions = state.stepExecutions.map(exec =>
        exec.stepId === nextStep.step.id
          ? { ...exec, status: 'failed' as const, error: 'No suitable worker available' }
          : exec
      );
      
      return { stepExecutions: updatedExecutions } as Partial<TState>;
    }

    console.log(`👷 Assigning "${nextStep.step.description}" to ${assignedWorker.name}`);
    
    // Execute the step with the assigned worker
    try {
      const stepResult = await executeStepWithWorker(nextStep.step, assignedWorker, timeoutPerTask);
      
      const updatedExecutions = state.stepExecutions.map(exec =>
        exec.stepId === nextStep.step.id
          ? {
              ...exec,
              status: 'completed' as const,
              assignedWorker: assignedWorker.id,
              endTime: new Date(),
              result: stepResult
            }
          : exec
      );
      
      return { stepExecutions: updatedExecutions } as Partial<TState>;
      
    } catch (error) {
      console.log(`❌ Step execution failed: ${error instanceof Error ? error.message : String(error)}`);
      
      const updatedExecutions = state.stepExecutions.map(exec =>
        exec.stepId === nextStep.step.id
          ? {
              ...exec,
              status: 'failed' as const,
              assignedWorker: assignedWorker.id,
              endTime: new Date(),
              error: error instanceof Error ? error.message : String(error)
            }
          : exec
      );
      
      return { stepExecutions: updatedExecutions } as Partial<TState>;
    }
  };

  const shouldContinue = (state: TState): 'continue' | '__end__' => {
    if (!state.stepExecutions) return 'continue';
    
    const hasMoreWork = state.stepExecutions.some(exec => 
      exec.status === 'pending' || exec.status === 'assigned' || exec.status === 'in_progress'
    );
    
    return hasMoreWork ? 'continue' : '__end__';
  };

  return new Graph<TState>()
    .addNode('orchestrator', orchestratorNode)
    .setEntryPoint('orchestrator')
    .addConditionalEdge('orchestrator', shouldContinue, {
      'continue': 'orchestrator',
      '__end__': '__END__'
    });
};

/**
 * Helper functions for orchestrator
 */

function findNextExecutableStep(
  executions: StepExecution[], 
  plan: PlanStep[]
): { step: PlanStep; execution: StepExecution } | null {
  
  for (const execution of executions) {
    if (execution.status !== 'pending') continue;
    
    const step = plan.find(s => s.id === execution.stepId);
    if (!step) continue;
    
    // Check if all dependencies are completed
    if (step.dependencies) {
      const dependenciesCompleted = step.dependencies.every(depId =>
        executions.find(exec => exec.stepId === depId)?.status === 'completed'
      );
      
      if (!dependenciesCompleted) continue;
    }
    
    return { step, execution };
  }
  
  return null;
}

function assignWorkerToStep(step: PlanStep, workers: WorkerAgent[]): WorkerAgent | null {
  // Simple assignment based on step description keywords
  const stepKeywords = step.description.toLowerCase().split(' ');
  
  let bestWorker: WorkerAgent | null = null;
  let bestScore = 0;
  
  for (const worker of workers) {
    if (worker.isAvailable === false) continue;
    
    let score = 0;
    
    // Check capabilities match
    for (const capability of worker.capabilities) {
      if (stepKeywords.some(keyword => capability.toLowerCase().includes(keyword))) {
        score += 2;
      }
    }
    
    // Check specializations match
    if (worker.specializations) {
      for (const specialization of worker.specializations) {
        if (stepKeywords.some(keyword => specialization.toLowerCase().includes(keyword))) {
          score += 3;
        }
      }
    }
    
    if (score > bestScore) {
      bestScore = score;
      bestWorker = worker;
    }
  }
  
  return bestWorker;
}

async function executeStepWithWorker(
  step: PlanStep, 
  worker: WorkerAgent, 
  timeout: number
): Promise<any> {
  console.log(`🔄 Executing step "${step.description}" with ${worker.name}`);
  
  // Create a timeout promise
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error(`Step execution timed out after ${timeout}ms`)), timeout);
  });
  
  // Execute the worker's graph with the step as input
  const executor = new Executor(worker.graph);
  const executionPromise = executor.execute({
    messages: [
      { role: 'user', content: `Execute this task: ${step.description}` }
    ]
  });
  
  // Race between execution and timeout
  const result = await Promise.race([executionPromise, timeoutPromise]);
  
  return result;
}
