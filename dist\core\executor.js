"use strict";
// src/core/executor.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.Executor = void 0;
/**
 * Ultra-lightweight Executor optimized for maximum performance
 *
 * Optimizations:
 * - Direct delegation to graph (zero overhead)
 * - Minimal method calls and object allocation
 * - Fast path for common operations
 */
class Executor {
    constructor(graph) {
        this.graph = graph;
    }
    /**
     * Execute graph (ultra-fast direct delegation)
     */
    execute(initialState) {
        return this.graph.execute(initialState);
    }
    /**
     * Execute with history collection (optimized)
     */
    async executeWithHistory(initialState) {
        const states = [initialState];
        for await (const state of this.graph.stream(initialState)) {
            states.push(state);
        }
        return states;
    }
    /**
     * Execute with callback (optimized for monitoring)
     */
    async executeWithCallback(initialState, onStateChange) {
        let lastState = initialState;
        let stepNumber = 0;
        for await (const state of this.graph.stream(initialState)) {
            stepNumber++;
            await onStateChange(state, stepNumber);
            lastState = state;
        }
        return lastState;
    }
    /**
     * Stream execution (direct delegation)
     */
    stream(initialState) {
        return this.graph.stream(initialState);
    }
    /**
     * Get graph instance
     */
    getGraph() {
        return this.graph;
    }
    /**
     * Get performance metrics
     */
    getMetrics() {
        return this.graph.getMetrics();
    }
    /**
     * Fast validation
     */
    validate() {
        this.graph.validate();
    }
}
exports.Executor = Executor;
//# sourceMappingURL=executor.js.map