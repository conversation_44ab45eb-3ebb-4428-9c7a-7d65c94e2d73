"use strict";
// src/core/executor.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.Executor = void 0;
/**
 * Executor class for running AG3NTIC graphs
 * Provides a simple interface for executing graphs from start to finish
 */
class Executor {
    constructor(graph) {
        this.graph = graph;
    }
    /**
     * Execute the graph from start to finish and return the final state
     * @param initialState The starting state for the graph
     * @returns Promise that resolves to the final state
     */
    async execute(initialState) {
        let lastState = initialState;
        for await (const state of this.graph.stream(initialState)) {
            lastState = state;
        }
        return lastState;
    }
    /**
     * Execute the graph and collect all intermediate states
     * @param initialState The starting state for the graph
     * @returns Promise that resolves to an array of all states during execution
     */
    async executeWithHistory(initialState) {
        const states = [initialState];
        for await (const state of this.graph.stream(initialState)) {
            states.push(state);
        }
        return states;
    }
    /**
     * Execute the graph with a callback for each state change
     * @param initialState The starting state for the graph
     * @param onStateChange Callback function called after each node execution
     * @returns Promise that resolves to the final state
     */
    async executeWithCallback(initialState, onStateChange) {
        let lastState = initialState;
        let stepNumber = 0;
        for await (const state of this.graph.stream(initialState)) {
            stepNumber++;
            await onStateChange(state, stepNumber);
            lastState = state;
        }
        return lastState;
    }
    /**
     * Get the underlying graph instance
     * @returns The graph instance used by this executor
     */
    getGraph() {
        return this.graph;
    }
}
exports.Executor = Executor;
//# sourceMappingURL=executor.js.map