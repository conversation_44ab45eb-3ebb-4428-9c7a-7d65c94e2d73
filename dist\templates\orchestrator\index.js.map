{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/templates/orchestrator/index.ts"], "names": [], "mappings": ";AAAA,sCAAsC;;AAyFtC,0DAsNC;AA7SD,qCAAuE;AACvE,mCAAuD;AA6EvD;;;;;;;;GAQG;AACH,SAAgB,uBAAuB,CACrC,OAAiC;IAEjC,MAAM,EACJ,OAAO,EACP,gBAAgB,EAChB,uBAAuB,GAAG,KAAK,EAChC,GAAG,OAAO,CAAC;IAEZ,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI;;;;;;;;qBAQ5B,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAE7F,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,EAAE,CAAC;IAElD,OAAO,CAAC,GAAG,CAAC,iCAAiC,OAAO,CAAC,MAAM,iBAAiB,aAAa,aAAa,CAAC,CAAC;IACxG,OAAO,CAAC,GAAG,CAAC,mBAAmB,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IAEpE,gCAAgC;IAChC,MAAM,WAAW,GAAyC,EAAE,CAAC;IAC7D,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,WAAW,CAAC,UAAU,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,EAAE,KAAa,EAA4B,EAAE;YACrF,mDAAmD;YACnD,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;YAC1C,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;YAED,IAAI,CAAC;gBACH,sCAAsC;gBACtC,MAAM,kBAAkB,GAAG;oBACzB,QAAQ,EAAE;wBACR,EAAE,IAAI,EAAE,MAAe,EAAE,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE;qBACjE;iBACK,CAAC;gBAET,6BAA6B;gBAC7B,MAAM,QAAQ,GAAG,IAAI,eAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC5C,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;gBAEhE,qBAAqB;gBACrB,MAAM,WAAW,GAAG,IAAA,oBAAc,EAAC,YAAY,CAAC,CAAC;gBACjD,MAAM,MAAM,GAAG,WAAW,EAAE,OAAO,IAAI,gBAAgB,CAAC;gBAExD,+BAA+B;gBAC/B,MAAM,iBAAiB,GAAG,KAAK,CAAC,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CACzD,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,WAAW,CAAC,IAAI,CAAC,EAAE;oBAClC,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,MAAM,EAAE,WAAoB,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,IAAI,EAAE,EAAE;oBACxE,CAAC,CAAC,IAAI,CACT,IAAI,EAAE,CAAC;gBAER,OAAO;oBACL,cAAc,EAAE,iBAAiB;oBACjC,UAAU,EAAE;wBACV,GAAG,KAAK,CAAC,UAAU;wBACnB,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,MAAM;qBAC9B;iBACiB,CAAC;YAEvB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,gCAAgC;gBAChC,MAAM,iBAAiB,GAAG,KAAK,CAAC,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CACzD,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,WAAW,CAAC,IAAI,CAAC,EAAE;oBAClC,CAAC,CAAC;wBACE,GAAG,IAAI;wBACP,MAAM,EAAE,QAAiB;wBACzB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;wBAC7D,OAAO,EAAE,IAAI,IAAI,EAAE;qBACpB;oBACH,CAAC,CAAC,IAAI,CACT,IAAI,EAAE,CAAC;gBAER,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAqB,CAAC;YAClE,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,mCAAmC;IACnC,MAAM,uBAAuB,GAAyB,KAAK,EAAE,KAAK,EAAE,EAAE;QACpE,qCAAqC;QACrC,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;YACxC,MAAM,cAAc,GAAoB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC9D,IAAI;gBACJ,MAAM,EAAE,SAAS;aAClB,CAAC,CAAC,CAAC;YAEJ,OAAO;gBACL,cAAc;gBACd,mBAAmB,EAAE,WAAoB;gBACzC,gBAAgB,EAAE,CAAC;gBACnB,UAAU,EAAE,EAAE;gBACd,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;aACtB,CAAC;QACvB,CAAC;QAED,qCAAqC;QACrC,IAAI,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,EAAE,CAAC;YAClG,MAAM,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;YAEpF,MAAM,gBAAgB,GAAG;gBACvB,IAAI,EAAE,WAAoB;gBAC1B,OAAO,EAAE,2DAA2D,WAAW,EAAE;aAClF,CAAC;YAEF,OAAO;gBACL,GAAG,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC;gBACtC,mBAAmB,EAAE,WAAoB;gBACzC,WAAW;aACZ,CAAC;QACJ,CAAC;QAED,4BAA4B;QAC5B,MAAM,QAAQ,GAAG,sBAAsB,CAAC,KAAK,CAAC,cAAc,IAAI,EAAE,EAAE,uBAAuB,CAAC,CAAC;QAC7F,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,uEAAuE;YACvE,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,IAAI,EAAE,CAAC;YACzF,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3B,MAAM,gBAAgB,GAAG;oBACvB,IAAI,EAAE,WAAoB;oBAC1B,OAAO,EAAE,uCAAuC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;iBACtG,CAAC;gBAEF,OAAO;oBACL,GAAG,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC;oBACtC,mBAAmB,EAAE,QAAiB;iBACvC,CAAC;YACJ,CAAC;YAED,OAAO,EAAE,CAAC,CAAC,0CAA0C;QACvD,CAAC;QAED,iCAAiC;QACjC,MAAM,cAAc,GAAG,kBAAkB,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAClE,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,4CAA4C;YAC5C,MAAM,iBAAiB,GAAG,KAAK,CAAC,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CACzD,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAC/B,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,MAAM,EAAE,SAAkB,EAAE,KAAK,EAAE,0BAA0B,EAAE;gBAC5E,CAAC,CAAC,IAAI,CACT,IAAI,EAAE,CAAC;YAER,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAqB,CAAC;QAClE,CAAC;QAED,iCAAiC;QACjC,MAAM,iBAAiB,GAAG,KAAK,CAAC,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CACzD,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,QAAQ,CAAC,IAAI,CAAC,EAAE;YAC/B,CAAC,CAAC;gBACE,GAAG,IAAI;gBACP,MAAM,EAAE,UAAmB;gBAC3B,cAAc,EAAE,cAAc,CAAC,EAAE;gBACjC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;YACH,CAAC,CAAC,IAAI,CACT,IAAI,EAAE,CAAC;QAER,MAAM,gBAAgB,GAAG;YACvB,IAAI,EAAE,WAAoB;YAC1B,OAAO,EAAE,mBAAmB,QAAQ,CAAC,IAAI,CAAC,WAAW,QAAQ,cAAc,CAAC,IAAI,EAAE;SACnF,CAAC;QAEF,OAAO;YACL,GAAG,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC;YACtC,cAAc,EAAE,iBAAiB;SACf,CAAC;IACvB,CAAC,CAAC;IAEF,4CAA4C;IAC5C,MAAM,qBAAqB,GAAG,gBAAgB,IAAI,uBAAuB,CAAC;IAE1E,qDAAqD;IACrD,MAAM,KAAK,GAAG,IAAI,YAAK,EAAU;SAC9B,OAAO,CAAC,cAAc,EAAE,qBAAqB,CAAC;SAC9C,aAAa,CAAC,cAAc,CAAC,CAAC;IAEjC,mBAAmB;IACnB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,KAAK,CAAC,OAAO,CAAC,UAAU,MAAM,CAAC,EAAE,EAAE,EAAE,WAAW,CAAC,UAAU,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,KAAK,CAAC,kBAAkB,CAAC,cAAc,EAAE,CAAC,KAAK,EAAE,EAAE;QACjD,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAI,WAAW,EAAE,cAAc,EAAE,CAAC;YAChC,OAAO,UAAU,WAAW,CAAC,cAAc,EAAE,CAAC;QAChD,CAAC;QAED,qCAAqC;QACrC,IAAI,KAAK,CAAC,mBAAmB,KAAK,WAAW,IAAI,KAAK,CAAC,mBAAmB,KAAK,QAAQ,EAAE,CAAC;YACxF,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC,EAAE;QACD,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YAChC,GAAG,CAAC,UAAU,MAAM,CAAC,EAAE,EAAE,CAAC,GAAG,UAAU,MAAM,CAAC,EAAE,EAAE,CAAC;YACnD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA4B,CAAC;QAChC,UAAU,EAAE,cAAc;QAC1B,SAAS,EAAE,SAAS;KACrB,CAAC,CAAC;IAEH,8CAA8C;IAC9C,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,KAAK,CAAC,OAAO,CAAC,UAAU,MAAM,CAAC,EAAE,EAAE,EAAE,cAAc,CAAC,CAAC;IACvD,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AAEH,SAAS,cAAc,CAAC,KAA6B;IACnD,OAAO,KAAK,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;AACxE,CAAC;AAED,SAAS,sBAAsB,CAAC,UAA2B,EAAE,cAAuB;IAClF,OAAO,CAAC,GAAG,CAAC,gCAAgC,cAAc,GAAG,CAAC,CAAC;IAC/D,0DAA0D;IAC1D,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QAC1C,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS;YAAE,OAAO,KAAK,CAAC;QAE5C,0CAA0C;QAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC;QAClD,OAAO,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAChC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,MAAM,KAAK,WAAW,CAClE,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,4CAA4C;AACpE,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAc,EAAE,OAAsB;IAChE,oEAAoE;IACpE,MAAM,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI,EAAE,CAAC;IAE7D,0CAA0C;IAC1C,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;QACzC,MAAM,oBAAoB,GAAG,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAC7D,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAClC,CAAC;QACF,MAAM,KAAK,GAAG,oBAAoB,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QACrF,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,0CAA0C;IAC1C,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAChD,OAAO,aAAa,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B;AACxG,CAAC;AAED,SAAS,iBAAiB,CAAC,UAA2B,EAAE,UAA+B;IACrF,OAAO,CAAC,GAAG,CAAC,6BAA6B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,qBAAqB,CAAC,CAAC;IAC9F,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC;IAE9E,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAChC,OAAO,uCAAuC,CAAC;IACjD,CAAC;IAED,MAAM,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CACxC,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,MAAM,IAAI,WAAW,EAAE,CAC1D,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEb,OAAO,aAAa,cAAc,CAAC,MAAM,cAAc,OAAO,EAAE,CAAC;AACnE,CAAC"}