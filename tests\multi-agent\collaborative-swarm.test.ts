import { describe, test, expect, beforeEach } from '@jest/globals';
import { z } from 'zod';
import { 
  Agent, 
  Graph, 
  Tool, 
  Memory, 
  EvaluationSystem,
  createTestState, 
  assertAgentResult, 
  assertPerformance,
  globalPerformanceMeasurer,
  testData,
  testConfig
} from '../setup.js';

/**
 * Test Suite 5: Collaborative Agent Swarm
 * 
 * Tests advanced multi-agent collaboration including:
 * - Swarm intelligence and collective decision making
 * - Peer-to-peer agent communication
 * - Consensus building and voting mechanisms
 * - Dynamic role assignment and specialization
 * - Emergent behavior and self-organization
 * - Distributed problem solving
 */

describe('Collaborative Agent Swarm Tests', () => {
  let swarmAgents: Agent[];
  let coordinatorAgent: Agent;
  let evaluationSystem: EvaluationSystem;
  let swarmMemory: Memory;
  let collaborationGraph: Graph<any>;

  beforeEach(async () => {
    // Create shared memory for swarm communication
    swarmMemory = new Memory({
      type: 'graph',
      maxMessages: 100,
    });

    // Create evaluation system for swarm performance
    evaluationSystem = new EvaluationSystem({
      enableSelfEvaluation: true,
      enablePeerReview: true,
      enableHumanReview: false,
      metrics: ['accuracy', 'efficiency', 'collaboration', 'innovation'],
    });

    // Create specialized swarm tools
    const swarmTools = [
      new Tool(
        {
          name: 'broadcast_message',
          description: 'Broadcast a message to all agents in the swarm',
          parameters: z.object({
            message: z.string(),
            priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
            targetRole: z.string().optional(),
          }),
        },
        async ({ message, priority, targetRole }) => {
          return `Broadcasting ${priority} priority message${targetRole ? ` to ${targetRole} agents` : ''}: "${message}"`;
        }
      ),
      new Tool(
        {
          name: 'vote_on_proposal',
          description: 'Vote on a proposal within the swarm',
          parameters: z.object({
            proposalId: z.string(),
            vote: z.enum(['approve', 'reject', 'abstain']),
            reasoning: z.string().optional(),
          }),
        },
        async ({ proposalId, vote, reasoning }) => {
          return `Vote recorded for proposal ${proposalId}: ${vote.toUpperCase()}${reasoning ? ` - Reasoning: ${reasoning}` : ''}`;
        }
      ),
      new Tool(
        {
          name: 'request_assistance',
          description: 'Request assistance from other agents in the swarm',
          parameters: z.object({
            task: z.string(),
            requiredSkills: z.array(z.string()),
            urgency: z.enum(['low', 'medium', 'high']).default('medium'),
          }),
        },
        async ({ task, requiredSkills, urgency }) => {
          return `Assistance request (${urgency} urgency): "${task}" - Required skills: [${requiredSkills.join(', ')}]`;
        }
      ),
      new Tool(
        {
          name: 'share_knowledge',
          description: 'Share knowledge or insights with the swarm',
          parameters: z.object({
            knowledge: z.string(),
            category: z.enum(['technical', 'strategic', 'operational', 'creative']),
            confidence: z.number().min(0).max(1),
          }),
        },
        async ({ knowledge, category, confidence }) => {
          return `Knowledge shared (${category}, confidence: ${confidence}): "${knowledge}"`;
        }
      ),
      new Tool(
        {
          name: 'form_subgroup',
          description: 'Form a specialized subgroup for focused collaboration',
          parameters: z.object({
            purpose: z.string(),
            memberRoles: z.array(z.string()),
            duration: z.string().optional(),
          }),
        },
        async ({ purpose, memberRoles, duration }) => {
          return `Subgroup formed for "${purpose}" with roles: [${memberRoles.join(', ')}]${duration ? ` Duration: ${duration}` : ''}`;
        }
      )
    ];

    // Create diverse swarm agents with different specializations
    swarmAgents = [
      new Agent({
        name: 'Research Specialist',
        instructions: 'Specialize in research, data gathering, and information synthesis. Collaborate with others to provide comprehensive insights.',
        role: 'analyst',
        tools: [
          ...swarmTools,
          new Tool(
            {
              name: 'research_topic',
              description: 'Research a specific topic',
              parameters: z.object({ topic: z.string(), depth: z.enum(['shallow', 'medium', 'deep']) }),
            },
            async ({ topic, depth }) => {
              const insights = {
                shallow: `Basic overview of ${topic}`,
                medium: `Detailed analysis of ${topic} with key findings`,
                deep: `Comprehensive research on ${topic} with multiple perspectives and implications`
              };
              return insights[depth];
            }
          )
        ],
        memory: swarmMemory,
        maxIterations: 5,
      }),
      new Agent({
        name: 'Creative Innovator',
        instructions: 'Focus on creative problem-solving, ideation, and innovative approaches. Collaborate to generate novel solutions.',
        role: 'planner',
        tools: [
          ...swarmTools,
          new Tool(
            {
              name: 'generate_ideas',
              description: 'Generate creative ideas for a given challenge',
              parameters: z.object({ challenge: z.string(), quantity: z.number().default(5) }),
            },
            async ({ challenge, quantity }) => {
              const ideas = Array.from({ length: quantity }, (_, i) => 
                `Creative idea ${i + 1} for ${challenge}: Innovative approach using emerging technologies`
              );
              return `Generated ${quantity} ideas:\n${ideas.join('\n')}`;
            }
          )
        ],
        memory: swarmMemory,
        maxIterations: 4,
      }),
      new Agent({
        name: 'Technical Executor',
        instructions: 'Handle technical implementation, execution, and optimization. Collaborate to ensure technical feasibility.',
        role: 'executor',
        tools: [
          ...swarmTools,
          new Tool(
            {
              name: 'assess_feasibility',
              description: 'Assess technical feasibility of a proposal',
              parameters: z.object({ proposal: z.string(), constraints: z.array(z.string()).optional() }),
            },
            async ({ proposal, constraints = [] }) => {
              const feasibilityScore = Math.random() * 0.4 + 0.6; // 0.6-1.0
              return `Feasibility assessment for "${proposal}": ${feasibilityScore.toFixed(2)} (${constraints.length} constraints considered)`;
            }
          )
        ],
        memory: swarmMemory,
        maxIterations: 3,
      }),
      new Agent({
        name: 'Quality Assurance',
        instructions: 'Ensure quality, validate solutions, and provide critical feedback. Collaborate to maintain high standards.',
        role: 'critic',
        tools: [
          ...swarmTools,
          new Tool(
            {
              name: 'quality_check',
              description: 'Perform quality assessment',
              parameters: z.object({ item: z.string(), criteria: z.array(z.string()) }),
            },
            async ({ item, criteria }) => {
              const scores = criteria.map(c => ({ criterion: c, score: Math.random() * 0.3 + 0.7 }));
              const avgScore = scores.reduce((sum, s) => sum + s.score, 0) / scores.length;
              return `Quality assessment for "${item}":\n${scores.map(s => `- ${s.criterion}: ${s.score.toFixed(2)}`).join('\n')}\nOverall: ${avgScore.toFixed(2)}`;
            }
          )
        ],
        memory: swarmMemory,
        maxIterations: 3,
      }),
      new Agent({
        name: 'Strategic Coordinator',
        instructions: 'Coordinate strategic decisions, facilitate consensus, and manage overall direction. Lead collaborative efforts.',
        role: 'orchestrator',
        tools: [
          ...swarmTools,
          new Tool(
            {
              name: 'facilitate_consensus',
              description: 'Facilitate consensus building among agents',
              parameters: z.object({ topic: z.string(), options: z.array(z.string()) }),
            },
            async ({ topic, options }) => {
              const votes = options.map(option => ({
                option,
                votes: Math.floor(Math.random() * swarmAgents.length) + 1
              }));
              const winner = votes.reduce((max, current) => current.votes > max.votes ? current : max);
              return `Consensus on "${topic}":\n${votes.map(v => `- ${v.option}: ${v.votes} votes`).join('\n')}\nConsensus: ${winner.option}`;
            }
          )
        ],
        memory: swarmMemory,
        maxIterations: 6,
      })
    ];

    // Create coordinator agent for swarm management
    coordinatorAgent = new Agent({
      name: 'Swarm Coordinator',
      instructions: `You coordinate a swarm of specialized agents working together on complex problems.
        
        Available agents:
        - Research Specialist: Research and information gathering
        - Creative Innovator: Creative problem-solving and ideation
        - Technical Executor: Technical implementation and feasibility
        - Quality Assurance: Quality validation and critical feedback
        - Strategic Coordinator: Strategic decisions and consensus building
        
        Facilitate collaboration, ensure all perspectives are considered, and guide the swarm toward optimal solutions.`,
      role: 'orchestrator',
      tools: swarmTools,
      handoffs: swarmAgents,
      memory: swarmMemory,
      maxIterations: 8,
    });

    // Create collaboration graph for swarm workflows
    collaborationGraph = new Graph()
      .addNode('coordinator', async (state) => {
        const result = await coordinatorAgent.run(state);
        return result.finalState;
      })
      .addNode('research', async (state) => {
        const result = await swarmAgents[0].run(state); // Research Specialist
        return result.finalState;
      })
      .addNode('ideation', async (state) => {
        const result = await swarmAgents[1].run(state); // Creative Innovator
        return result.finalState;
      })
      .addNode('execution', async (state) => {
        const result = await swarmAgents[2].run(state); // Technical Executor
        return result.finalState;
      })
      .addNode('validation', async (state) => {
        const result = await swarmAgents[3].run(state); // Quality Assurance
        return result.finalState;
      })
      .addNode('consensus', async (state) => {
        const result = await swarmAgents[4].run(state); // Strategic Coordinator
        return result.finalState;
      })
      .setEntryPoint('coordinator')
      .addConditionalEdges('coordinator', (state) => {
        const content = state.messages[state.messages.length - 1]?.content || '';
        if (content.includes('research')) return 'research';
        if (content.includes('creative') || content.includes('idea')) return 'ideation';
        if (content.includes('implement') || content.includes('execute')) return 'execution';
        if (content.includes('validate') || content.includes('quality')) return 'validation';
        if (content.includes('consensus') || content.includes('decide')) return 'consensus';
        return 'research'; // Default to research
      }, {
        research: 'ideation',
        ideation: 'execution',
        execution: 'validation',
        validation: 'consensus',
        consensus: 'END'
      });

    // Add parallel collaboration patterns
    collaborationGraph.addParallel('parallel_analysis', {
      nodes: ['research', 'ideation'],
      mergeStrategy: 'all',
      timeout: 15000,
    });

    collaborationGraph.addParallel('parallel_review', {
      nodes: ['execution', 'validation'],
      mergeStrategy: 'all',
      timeout: 10000,
    });

    collaborationGraph.compile();
  });

  test('should create collaborative swarm with diverse agents', () => {
    expect(swarmAgents).toHaveLength(5);
    expect(coordinatorAgent.name).toBe('Swarm Coordinator');
    expect(coordinatorAgent.handoffs).toHaveLength(5);
    
    const roles = swarmAgents.map(agent => agent.role);
    expect(roles).toContain('analyst');
    expect(roles).toContain('planner');
    expect(roles).toContain('executor');
    expect(roles).toContain('critic');
    expect(roles).toContain('orchestrator');
  });

  test('should facilitate swarm communication and broadcasting', async () => {
    globalPerformanceMeasurer.start();
    
    const result = await coordinatorAgent.run(
      'Broadcast to all agents: We need to solve a complex optimization problem collaboratively'
    );
    
    const duration = globalPerformanceMeasurer.measure('swarm_communication');
    
    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 2);
    
    expect(result.finalOutput.toLowerCase()).toContain('broadcast');
    expect(result.metrics.toolCalls).toBeGreaterThan(0);
  });

  test('should enable peer-to-peer collaboration', async () => {
    // Research agent requests assistance
    const assistanceResult = await swarmAgents[0].run(
      'I need assistance with analyzing market trends. Looking for agents with strategic and creative skills.'
    );
    
    assertAgentResult(assistanceResult);
    expect(assistanceResult.finalOutput.toLowerCase()).toContain('assistance');
    
    // Creative agent responds with knowledge sharing
    const knowledgeResult = await swarmAgents[1].run(
      'Sharing creative insights about market trend analysis: Use sentiment analysis combined with predictive modeling'
    );
    
    assertAgentResult(knowledgeResult);
    expect(knowledgeResult.finalOutput.toLowerCase()).toContain('knowledge');
  });

  test('should support consensus building and voting', async () => {
    globalPerformanceMeasurer.start();
    
    const result = await swarmAgents[4].run( // Strategic Coordinator
      'Facilitate consensus on our approach to the new product launch: Option A (aggressive timeline), Option B (conservative approach), Option C (hybrid strategy)'
    );
    
    const duration = globalPerformanceMeasurer.measure('consensus_building');
    
    assertAgentResult(result);
    assertPerformance(duration, testConfig.performance.maxExecutionTime);
    
    expect(result.finalOutput.toLowerCase()).toContain('consensus');
    expect(result.finalOutput).toContain('Option');
  });

  test('should form dynamic subgroups for specialized tasks', async () => {
    const result = await coordinatorAgent.run(
      'Form a specialized subgroup with research and technical agents to tackle the data architecture challenge'
    );
    
    assertAgentResult(result);
    
    expect(result.finalOutput.toLowerCase()).toContain('subgroup');
    expect(result.finalOutput.toLowerCase()).toContain('research');
    expect(result.finalOutput.toLowerCase()).toContain('technical');
  });

  test('should execute complete collaborative workflow', async () => {
    globalPerformanceMeasurer.start();
    
    const initialState = createTestState({
      messages: [{ 
        role: 'user', 
        content: 'Collaborate to design and validate a new AI-powered customer service system' 
      }],
      metadata: { workflowType: 'collaborative', complexity: 'high' }
    });
    
    const result = await collaborationGraph.run(initialState);
    
    const duration = globalPerformanceMeasurer.measure('collaborative_workflow');
    
    expect(result).toBeDefined();
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 5);
    
    expect(result.messages.length).toBeGreaterThan(3);
    expect(result.metadata?.workflowType).toBe('collaborative');
  });

  test('should demonstrate emergent swarm intelligence', async () => {
    // Complex problem requiring multiple perspectives
    const complexProblem = `
      Design a sustainable smart city infrastructure that addresses:
      - Energy efficiency and renewable integration
      - Traffic optimization and autonomous vehicles
      - Waste management and circular economy
      - Citizen engagement and digital services
      - Economic development and job creation
      
      Each agent should contribute their expertise and build on others' ideas.
    `;

    globalPerformanceMeasurer.start();
    
    // Execute parallel collaboration
    const collaborationResults = await Promise.all([
      swarmAgents[0].run(`Research sustainable smart city technologies: ${complexProblem}`),
      swarmAgents[1].run(`Generate innovative ideas for smart city design: ${complexProblem}`),
      swarmAgents[2].run(`Assess technical feasibility of smart city components: ${complexProblem}`),
      swarmAgents[3].run(`Validate quality and sustainability of smart city proposals: ${complexProblem}`),
    ]);
    
    const duration = globalPerformanceMeasurer.measure('emergent_intelligence');
    
    collaborationResults.forEach(result => assertAgentResult(result));
    assertPerformance(duration, testConfig.performance.maxExecutionTime * 3);
    
    // Check for diverse perspectives and building on ideas
    const outputs = collaborationResults.map(r => r.finalOutput.toLowerCase());
    expect(outputs.some(o => o.includes('research') || o.includes('data'))).toBe(true);
    expect(outputs.some(o => o.includes('creative') || o.includes('innovative'))).toBe(true);
    expect(outputs.some(o => o.includes('technical') || o.includes('feasibility'))).toBe(true);
    expect(outputs.some(o => o.includes('quality') || o.includes('sustainable'))).toBe(true);
  });

  test('should handle conflicting opinions and resolve disputes', async () => {
    // Simulate conflicting votes
    const conflictResults = await Promise.all([
      swarmAgents[1].run('Vote on proposal PROJ-001: I vote APPROVE - this is innovative and promising'),
      swarmAgents[2].run('Vote on proposal PROJ-001: I vote REJECT - technical risks are too high'),
      swarmAgents[3].run('Vote on proposal PROJ-001: I vote ABSTAIN - need more information for proper assessment'),
    ]);
    
    conflictResults.forEach(result => assertAgentResult(result));
    
    // Coordinator should facilitate resolution
    const resolutionResult = await swarmAgents[4].run(
      'We have conflicting votes on PROJ-001. Facilitate discussion and work toward consensus.'
    );
    
    assertAgentResult(resolutionResult);
    expect(resolutionResult.finalOutput.toLowerCase()).toContain('consensus');
  });

  test('should adapt roles dynamically based on task requirements', async () => {
    const adaptationResult = await coordinatorAgent.run(`
      We have a new challenge that requires specialized expertise in blockchain technology.
      Temporarily reassign roles: Research Specialist becomes Blockchain Researcher,
      Technical Executor becomes Blockchain Developer.
    `);
    
    assertAgentResult(adaptationResult);
    
    expect(adaptationResult.finalOutput.toLowerCase()).toContain('blockchain');
    expect(adaptationResult.finalOutput.toLowerCase()).toContain('reassign');
  });

  test('should maintain swarm cohesion under stress', async () => {
    // Simulate high-pressure scenario with tight deadlines
    const stressResults = await Promise.all([
      coordinatorAgent.run('URGENT: Critical system failure - all agents coordinate immediate response'),
      swarmAgents[0].run('Research the root cause of the system failure immediately'),
      swarmAgents[2].run('Execute emergency recovery procedures'),
      swarmAgents[3].run('Validate that recovery measures are working'),
    ]);
    
    stressResults.forEach(result => assertAgentResult(result));
    
    // All agents should respond appropriately to urgent situation
    const urgentResponses = stressResults.filter(r => 
      r.finalOutput.toLowerCase().includes('urgent') || 
      r.finalOutput.toLowerCase().includes('emergency') ||
      r.finalOutput.toLowerCase().includes('immediate')
    );
    
    expect(urgentResponses.length).toBeGreaterThan(0);
  });

  test('should demonstrate collective learning and knowledge accumulation', async () => {
    // First learning cycle
    const learning1 = await swarmAgents[0].run(
      'Research machine learning best practices and share findings with the swarm'
    );
    
    assertAgentResult(learning1);
    
    // Second agent builds on shared knowledge
    const learning2 = await swarmAgents[1].run(
      'Based on the ML research shared earlier, generate creative applications for our use case'
    );
    
    assertAgentResult(learning2);
    
    // Third agent applies accumulated knowledge
    const learning3 = await swarmAgents[2].run(
      'Implement the ML solution using the research and creative ideas discussed by the team'
    );
    
    assertAgentResult(learning3);
    
    // Knowledge should build progressively
    expect(learning2.finalState.messages.length).toBeGreaterThan(learning1.finalState.messages.length);
    expect(learning3.finalState.messages.length).toBeGreaterThan(learning2.finalState.messages.length);
  });

  test('should optimize swarm performance through self-organization', async () => {
    globalPerformanceMeasurer.start();
    
    // Measure initial performance
    const initialTask = await coordinatorAgent.run('Coordinate a simple task distribution among agents');
    const initialDuration = globalPerformanceMeasurer.measure('initial_performance');
    
    // Self-organization phase
    const organizationResult = await coordinatorAgent.run(
      'Analyze our collaboration patterns and optimize agent coordination for better efficiency'
    );
    
    assertAgentResult(organizationResult);
    
    // Measure optimized performance
    globalPerformanceMeasurer.start();
    const optimizedTask = await coordinatorAgent.run('Coordinate the same type of task with optimized patterns');
    const optimizedDuration = globalPerformanceMeasurer.measure('optimized_performance');
    
    assertAgentResult(optimizedTask);
    
    // Performance should improve or at least maintain consistency
    expect(optimizedDuration).toBeLessThanOrEqual(initialDuration * 1.2); // Allow 20% variance
  });

  test('should handle swarm scalability and agent addition/removal', async () => {
    // Add new specialized agent to swarm
    const newAgent = new Agent({
      name: 'Security Specialist',
      instructions: 'Focus on security analysis and threat assessment',
      role: 'analyst',
      tools: swarmAgents[0].tools, // Reuse existing tools
      memory: swarmMemory,
      maxIterations: 3,
    });

    // Update coordinator handoffs
    coordinatorAgent.handoffs.push(newAgent);
    
    const scalabilityResult = await coordinatorAgent.run(
      'Welcome our new Security Specialist to the swarm and integrate them into our workflow'
    );
    
    assertAgentResult(scalabilityResult);
    expect(scalabilityResult.finalOutput.toLowerCase()).toContain('security');
    expect(coordinatorAgent.handoffs).toHaveLength(6);
  });

  test('should provide comprehensive swarm analytics and insights', async () => {
    // Execute several collaborative tasks to generate data
    await Promise.all([
      swarmAgents[0].run('Research task A'),
      swarmAgents[1].run('Creative task B'),
      swarmAgents[2].run('Technical task C'),
    ]);

    const analyticsResult = await coordinatorAgent.run(
      'Provide comprehensive analytics on our swarm performance, collaboration patterns, and efficiency metrics'
    );
    
    assertAgentResult(analyticsResult);
    
    expect(analyticsResult.finalOutput.toLowerCase()).toContain('analytics');
    expect(analyticsResult.finalOutput.toLowerCase()).toContain('performance');
    expect(analyticsResult.finalOutput.toLowerCase()).toContain('collaboration');
  });
});
