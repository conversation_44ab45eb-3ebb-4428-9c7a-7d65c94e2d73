/**
 * Model Context Protocol (MCP) Integration for AG3NTIC
 *
 * This module provides comprehensive MCP integration, allowing AG3NTIC to:
 *
 * 1. **Act as an MCP Client**: Connect to external MCP servers to access tools, resources, and prompts
 * 2. **Act as an MCP Server**: Expose AG3NTIC capabilities via the standardized MCP protocol
 * 3. **Seamless Integration**: Work with the entire MCP ecosystem while maintaining AG3NTIC's simplicity
 *
 * Key Features:
 * - Full JSON-RPC 2.0 compliance
 * - Support for Streamable HTTP and SSE transports
 * - Backwards compatibility with legacy MCP servers
 * - Type-safe integration with AG3NTIC agent templates
 * - Automatic tool function conversion
 * - Resource and prompt management
 *
 * Usage Examples:
 *
 * ```typescript
 * // As MCP Client (connect to external servers)
 * const client = await createMCPClient({
 *   name: 'my-agent',
 *   version: '1.0.0',
 *   serverUrl: 'http://localhost:3000/mcp'
 * });
 *
 * const tools = await client.getToolFunctions();
 * const agent = createExecutorAgent(llmNode, tools);
 *
 * // As MCP Server (expose AG3NTIC capabilities)
 * const server = new MCPServer({
 *   name: 'ag3ntic-server',
 *   version: '1.0.0'
 * });
 *
 * server.registerAG3NTICTools(myTools);
 * await server.startHTTP();
 * ```
 */
export { MCPClient, createMCPClient, createMCPClientWithFallback, type MCPClientConfig } from './client';
export { MCPServer, createMCPServer, createMCPHTTPServer, type MCPServerConfig, type MCPToolHandler, type MCPResourceHandler, type MCPPromptHandler } from './server';
export { type MCPRequest, type MCPResponse, type MCPNotification, type MCPProtocolMessage, type MCPContent, type MCPTextContent, type MCPImageContent, type MCPResourceContent, type MCPTool, type MCPToolResult, type MCPResource, type MCPResourceContents, type MCPPrompt, type MCPPromptResult, type MCPPromptMessage } from '../../core/types';
/**
 * MCP Integration Utilities
 */
/**
 * Check if MCP SDK is available
 */
export declare function isMCPAvailable(): Promise<boolean>;
/**
 * Get MCP SDK version if available
 */
export declare function getMCPVersion(): Promise<string | null>;
/**
 * Create an MCP-enabled tool function that can work with both
 * AG3NTIC's tool system and MCP protocol
 */
export declare function createMCPCompatibleTool(name: string, description: string, inputSchema: any, handler: (args: any) => Promise<any>): {
    toolFunction: (args: any) => Promise<any>;
    mcpDefinition: {
        name: string;
        description: string;
        inputSchema: any;
    };
    mcpHandler: (args: Record<string, unknown>) => Promise<{
        content: {
            type: "text";
            text: string;
        }[];
        isError?: never;
    } | {
        content: {
            type: "text";
            text: string;
        }[];
        isError: boolean;
    }>;
};
/**
 * Convert AG3NTIC tools to MCP format
 */
export declare function convertAG3NTICToolsToMCP(tools: Record<string, (args: any) => any>): {
    name: string;
    definition: any;
    handler: any;
}[];
/**
 * Create a unified MCP + AG3NTIC agent that can use both local tools
 * and remote MCP server tools
 */
export declare function createMCPEnabledAgent(config: {
    localTools?: Record<string, (args: any) => any>;
    mcpServers?: Array<{
        name: string;
        url: string;
        transport?: 'http' | 'sse';
    }>;
    agentConfig: {
        name: string;
        version: string;
    };
}): Promise<{
    tools: Record<string, (args: any) => any>;
    mcpClients: any[];
    cleanup(): Promise<void>;
}>;
/**
 * MCP Integration Status
 */
export declare function getMCPIntegrationStatus(): Promise<{
    available: boolean;
    version: string | null;
    features: {
        client: boolean;
        server: boolean;
        streamableHttp: boolean;
        sse: boolean;
    };
}>;
//# sourceMappingURL=index.d.ts.map