{"version": 3, "file": "server.js", "sourceRoot": "", "sources": ["../../../src/integrations/mcp/server.ts"], "names": [], "mappings": ";AAAA,iCAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmYjC,0CAIC;AAKD,kDAIC;AApYD;;GAEG;AACH,KAAK,UAAU,WAAW;IACxB,IAAI,CAAC;QACH,wDAAa,yCAAyC,GAAC,CAAC;IAC1D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CACb,0EAA0E;YAC1E,iDAAiD,CAClD,CAAC;IACJ,CAAC;AACH,CAAC;AAmCD;;;;;;;GAOG;AACH,MAAa,SAAS;IAQpB,YAAY,MAAuB;QAL3B,UAAK,GAAkE,IAAI,GAAG,EAAE,CAAC;QACjF,cAAS,GAA0E,IAAI,GAAG,EAAE,CAAC;QAC7F,YAAO,GAAsE,IAAI,GAAG,EAAE,CAAC;QACvF,YAAO,GAAY,KAAK,CAAC;QAG/B,IAAI,CAAC,MAAM,GAAG;YACZ,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,WAAW;YACjB,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,KAAK;YACjB,YAAY,EAAE;gBACZ,KAAK,EAAE,IAAI;gBACX,SAAS,EAAE,IAAI;gBACf,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,IAAI;aACd;YACD,GAAG,MAAM;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,IAAY,EAAE,UAAmB,EAAE,OAAuB;QACrE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,EAAE,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,IAAY,EAAE,UAAuB,EAAE,OAA2B;QACjF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAY,EAAE,UAAqB,EAAE,OAAyB;QAC3E,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,EAAE,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,WAAW,EAAE,CAAC;YAEpB,wDAAwD;YACxD,MAAM,EAAE,SAAS,EAAE,GAAG,wDAAa,yCAAyC,GAAC,CAAC;YAE9E,IAAI,CAAC,MAAM,GAAG,IAAI,SAAS,CAAC;gBAC1B,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;gBACtB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;aAC7B,CAAC,CAAC;YAEH,qBAAqB;YACrB,KAAK,MAAM,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACzD,IAAI,CAAC,MAAM,CAAC,YAAY,CACtB,IAAI,EACJ;oBACE,KAAK,EAAE,UAAU,CAAC,IAAI;oBACtB,WAAW,EAAE,UAAU,CAAC,WAAW;oBACnC,WAAW,EAAE,UAAU,CAAC,WAAW;iBACpC,EACD,KAAK,EAAE,IAA6B,EAAE,EAAE;oBACtC,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;wBACnC,OAAO,MAAM,CAAC;oBAChB,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,UAAU,IAAI,UAAU,EAAE,KAAK,CAAC,CAAC;wBAC/C,OAAO;4BACL,OAAO,EAAE,CAAC;oCACR,IAAI,EAAE,MAAM;oCACZ,IAAI,EAAE,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iCACzE,CAAC;4BACF,OAAO,EAAE,IAAI;yBACd,CAAC;oBACJ,CAAC;gBACH,CAAC,CACF,CAAC;YACJ,CAAC;YAED,yBAAyB;YACzB,KAAK,MAAM,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC7D,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAC1B,IAAI,EACJ,UAAU,CAAC,GAAG,EACd;oBACE,KAAK,EAAE,UAAU,CAAC,IAAI;oBACtB,WAAW,EAAE,UAAU,CAAC,WAAW;oBACnC,QAAQ,EAAE,UAAU,CAAC,QAAQ;iBAC9B,EACD,KAAK,EAAE,GAAW,EAAE,EAAE;oBACpB,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;wBAClC,OAAO,MAAM,CAAC;oBAChB,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,cAAc,IAAI,UAAU,EAAE,KAAK,CAAC,CAAC;wBACnD,OAAO;4BACL,QAAQ,EAAE,CAAC;oCACT,GAAG;oCACH,IAAI,EAAE,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oCACxE,QAAQ,EAAE,YAAY;iCACvB,CAAC;yBACH,CAAC;oBACJ,CAAC;gBACH,CAAC,CACF,CAAC;YACJ,CAAC;YAED,uBAAuB;YACvB,KAAK,MAAM,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC3D,IAAI,CAAC,MAAM,CAAC,cAAc,CACxB,IAAI,EACJ;oBACE,KAAK,EAAE,UAAU,CAAC,IAAI;oBACtB,WAAW,EAAE,UAAU,CAAC,WAAW;oBACnC,UAAU,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;wBAChC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;4BAC1C,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC;4BACpE,OAAO,MAAM,CAAC;wBAChB,CAAC,EAAE,EAAyB,CAAC,CAAC,CAAC,CAAC,EAAE;iBACrC,EACD,KAAK,EAAE,IAA6B,EAAE,EAAE;oBACtC,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;wBACnC,OAAO,MAAM,CAAC;oBAChB,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,OAAO,CAAC,KAAK,CAAC,YAAY,IAAI,UAAU,EAAE,KAAK,CAAC,CAAC;wBACjD,OAAO;4BACL,WAAW,EAAE,mBAAmB,IAAI,EAAE;4BACtC,QAAQ,EAAE,CAAC;oCACT,IAAI,EAAE,WAAoB;oCAC1B,OAAO,EAAE;wCACP,IAAI,EAAE,MAAM;wCACZ,IAAI,EAAE,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;qCACzE;iCACF,CAAC;yBACH,CAAC;oBACJ,CAAC;gBACH,CAAC,CACF,CAAC;YACJ,CAAC;YAED,wCAAwC;YACxC,MAAM,EAAE,oBAAoB,EAAE,GAAG,wDAAa,2CAA2C,GAAC,CAAC;YAC3F,MAAM,SAAS,GAAG,IAAI,oBAAoB,EAAE,CAAC;YAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAErC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,MAAM,CAAC,IAAI,gCAAgC,CAAC,CAAC;QAElF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS;QACb,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,WAAW,EAAE,CAAC;YAEpB,gDAAgD;YAChD,MAAM,OAAO,GAAG,wDAAa,SAAS,GAAC,CAAC;YACxC,MAAM,EAAE,6BAA6B,EAAE,GAAG,wDAAa,oDAAoD,GAAC,CAAC;YAE7G,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;YAC9B,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YAEhC,4BAA4B;YAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;gBAC3B,IAAI,CAAC;oBACH,MAAM,IAAI,GAAG,wDAAa,MAAM,GAAC,CAAC;oBAClC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;wBACnB,MAAM,EAAE,GAAG;wBACX,cAAc,EAAE,CAAC,gBAAgB,CAAC;wBAClC,cAAc,EAAE,CAAC,cAAc,EAAE,gBAAgB,CAAC;qBACnD,CAAC,CAAC,CAAC;gBACN,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAC;oBACvF,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBACzD,CAAC;YACH,CAAC;YAED,wCAAwC;YACxC,MAAM,UAAU,GAAiC,EAAE,CAAC;YAEpD,sBAAsB;YACtB,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;gBAC3C,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAuB,CAAC;gBACtE,IAAI,SAAc,CAAC;gBAEnB,IAAI,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBACvC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;gBACpC,CAAC;qBAAM,CAAC;oBACN,MAAM,EAAE,UAAU,EAAE,GAAG,wDAAa,aAAa,GAAC,CAAC;oBACnD,SAAS,GAAG,IAAI,6BAA6B,CAAC;wBAC5C,kBAAkB,EAAE,GAAG,EAAE,CAAC,UAAU,EAAE;wBACtC,oBAAoB,EAAE,CAAC,SAAiB,EAAE,EAAE;4BAC1C,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;wBACpC,CAAC;qBACF,CAAC,CAAC;oBAEH,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;wBACvB,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;4BACxB,OAAO,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;wBACzC,CAAC;oBACH,CAAC,CAAC;oBAEF,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;gBACvC,CAAC;gBAED,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC;YAEH,wBAAwB;YACxB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAK,EAAE,GAAG,EAAE;gBACnE,OAAO,CAAC,GAAG,CAAC,0CAA0C,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,CAAC;YACpG,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAC1B,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;YACrB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;YACtB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;YAC5B,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YACtB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YAC9B,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YAC1B,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;SACvC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAyC;QAC5D,KAAK,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzD,IAAI,CAAC,YAAY,CACf,IAAI,EACJ;gBACE,IAAI;gBACJ,WAAW,EAAE,iBAAiB,IAAI,EAAE;gBACpC,WAAW,EAAE;oBACX,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE,EAAE;oBACd,QAAQ,EAAE,EAAE;iBACb;aACF,EACD,KAAK,EAAE,IAA6B,EAAE,EAAE;gBACtC,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;oBAEzD,+BAA+B;oBAC/B,MAAM,OAAO,GAAiB,CAAC;4BAC7B,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;yBAC5E,CAAC,CAAC;oBAEH,OAAO,EAAE,OAAO,EAAE,CAAC;gBACrB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO;wBACL,OAAO,EAAE,CAAC;gCACR,IAAI,EAAE,MAAM;gCACZ,IAAI,EAAE,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;6BACzE,CAAC;wBACF,OAAO,EAAE,IAAI;qBACd,CAAC;gBACJ,CAAC;YACH,CAAC,CACF,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AA3TD,8BA2TC;AAED;;GAEG;AACI,KAAK,UAAU,eAAe,CAAC,MAAuB;IAC3D,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;IACrC,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;IACrB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,mBAAmB,CAAC,MAAuB;IAC/D,MAAM,MAAM,GAAG,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;IACrC,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;IACzB,OAAO,MAAM,CAAC;AAChB,CAAC"}