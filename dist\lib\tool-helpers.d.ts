import { AgentState, MCPToolCall, NodeFunction } from "../core/types";
/**
 * Ultra-fast tool execution helpers optimized for performance
 *
 * Optimizations:
 * - Pre-compiled tool maps for O(1) lookup
 * - Minimal object allocation in hot paths
 * - Efficient error handling
 * - Cached JSON parsing where possible
 */
/**
 * Optimized tool function map type
 */
export type ToolMap = Record<string, (...args: any[]) => any>;
/**
 * Get tool calls (fast extraction)
 */
export declare const getToolCalls: <TState extends AgentState>(state: TState) => MCPToolCall[];
/**
 * Check for tool calls (O(1) operation)
 */
export declare const hasToolCalls: <TState extends AgentState>(state: TState) => boolean;
/**
 * Fast routing for tool execution
 */
export declare const shouldCallTools: <TState extends AgentState>(state: TState) => "tools" | "__end__";
/**
 * Create optimized tool node (ultra-fast execution)
 */
export declare const createToolNode: <TState extends AgentState>(tools: ToolMap) => NodeFunction<TState>;
/**
 * Create fast tool validator (pre-compiled)
 */
export declare const createToolValidator: (requiredTools: string[], availableTools: ToolMap) => (() => void);
/**
 * Create enhanced tool node (optimized with features)
 */
export declare const createEnhancedToolNode: <TState extends AgentState>(tools: ToolMap, options?: {
    logExecution?: boolean;
    timeout?: number;
    retries?: number;
}) => NodeFunction<TState>;
/**
 * Additional optimized tool helpers
 */
/**
 * Fast tool name extraction
 */
export declare const getToolNames: (tools: ToolMap) => string[];
/**
 * Check if tool exists (O(1))
 */
export declare const hasToolFunction: (tools: ToolMap, name: string) => boolean;
/**
 * Get tool count (O(1))
 */
export declare const getToolCount: (tools: ToolMap) => number;
/**
 * Create tool execution logger (optimized)
 */
export declare const createToolLogger: (prefix?: string) => {
    start: (name: string) => void;
    success: (name: string) => void;
    error: (name: string, error: string) => void;
};
//# sourceMappingURL=tool-helpers.d.ts.map