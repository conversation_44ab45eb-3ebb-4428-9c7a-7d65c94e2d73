import { AgentState, MCPToolCall, NodeFunction } from "../core/types";
/**
 * Type for tool function map
 */
export type ToolMap = Record<string, (...args: any[]) => any>;
/**
 * Get tool calls from the last assistant message
 * @param state The current state
 * @returns Array of tool calls or empty array if none exist
 */
export declare function getToolCalls<TState extends AgentState>(state: TState): MCPToolCall[];
/**
 * Check if the last message contains tool calls
 * @param state The current state
 * @returns True if the last message has tool calls
 */
export declare function hasToolCalls<TState extends AgentState>(state: TState): boolean;
/**
 * Simple routing function for agent -> tool -> agent loops
 * @param state The current state
 * @returns 'tools' if the last message has tool calls, '__end__' otherwise
 */
export declare function shouldCallTools<TState extends AgentState>(state: TState): 'tools' | '__end__';
/**
 * Create a tool node that can execute a map of tools
 * @param tools Map of tool names to their executable functions
 * @returns A node function that can execute tools based on the last assistant message
 */
export declare function createToolNode<TState extends AgentState>(tools: ToolMap): NodeFunction<TState>;
/**
 * Create a tool validation function that checks if required tools are available
 * @param requiredTools Array of tool names that must be available
 * @param availableTools Map of available tools
 * @returns Validation function that throws if tools are missing
 */
export declare function createToolValidator(requiredTools: string[], availableTools: ToolMap): () => void;
/**
 * Create a tool execution wrapper with error handling and logging
 * @param tools Map of tool functions
 * @param options Configuration options
 * @returns Enhanced tool node with additional features
 */
export declare function createEnhancedToolNode<TState extends AgentState>(tools: ToolMap, options?: {
    logExecution?: boolean;
    timeout?: number;
    retries?: number;
}): NodeFunction<TState>;
//# sourceMappingURL=tool-helpers.d.ts.map