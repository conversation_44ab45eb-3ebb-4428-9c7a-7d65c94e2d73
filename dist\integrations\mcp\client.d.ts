import { MCPTool, MCP<PERSON>oolResult, MCPResource, MCPResourceContents, MCPPrompt, MCPPromptResult } from '../../core/types';
/**
 * MCP Client Configuration
 */
export interface MCPClientConfig {
    name: string;
    version: string;
    serverUrl?: string;
    transport?: 'stdio' | 'http' | 'sse';
    timeout?: number;
    maxRetries?: number;
}
/**
 * MCP Client for connecting AG3NTIC agents to external MCP servers
 *
 * This allows AG3NTIC agents to:
 * - Connect to any MCP-compliant server
 * - Access external tools, resources, and prompts
 * - Use standardized MCP protocol for communication
 */
export declare class MCPClient {
    private config;
    private client;
    private connected;
    private requestId;
    constructor(config: MCPClientConfig);
    /**
     * Connect to an MCP server
     */
    connect(): Promise<void>;
    /**
     * Disconnect from the MCP server
     */
    disconnect(): Promise<void>;
    /**
     * Check if client is connected
     */
    isConnected(): boolean;
    /**
     * List available tools from the MCP server
     */
    listTools(): Promise<MCPTool[]>;
    /**
     * Call a tool on the MCP server
     */
    callTool(name: string, arguments_: Record<string, unknown>): Promise<MCPToolResult>;
    /**
     * List available resources from the MCP server
     */
    listResources(): Promise<MCPResource[]>;
    /**
     * Read a resource from the MCP server
     */
    readResource(uri: string): Promise<MCPResourceContents>;
    /**
     * List available prompts from the MCP server
     */
    listPrompts(): Promise<MCPPrompt[]>;
    /**
     * Get a prompt from the MCP server
     */
    getPrompt(name: string, arguments_?: Record<string, unknown>): Promise<MCPPromptResult>;
    /**
     * Send a raw MCP request
     */
    sendRequest(method: string, params?: Record<string, unknown>): Promise<any>;
    /**
     * Create an MCP-compatible tool function for use with AG3NTIC agents
     */
    createToolFunction(toolName: string): (args: Record<string, unknown>) => Promise<string>;
    /**
     * Get all tools as AG3NTIC-compatible tool functions
     */
    getToolFunctions(): Promise<Record<string, (args: any) => Promise<string>>>;
    /**
     * Ensure the client is connected
     */
    private ensureConnected;
}
/**
 * Create an MCP client with automatic connection
 */
export declare function createMCPClient(config: MCPClientConfig): Promise<MCPClient>;
/**
 * Create MCP client with backwards compatibility fallback
 */
export declare function createMCPClientWithFallback(httpUrl: string, sseUrl?: string, config?: Partial<MCPClientConfig>): Promise<MCPClient>;
//# sourceMappingURL=client.d.ts.map