"use strict";
// src/examples/multi-agent-system.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.runSimpleTemplateExamples = exports.runMultiAgentDemo = void 0;
const core_1 = require("../core");
const lib_1 = require("../lib");
const templates_1 = require("../templates");
/**
 * Comprehensive example showing how to combine all AG3NTIC templates
 * to create a sophisticated multi-agent system
 */
// --- 1. Define Tools for Different Agents ---
const researchTools = {
    searchWeb: async (args) => {
        const { query, maxResults = 5 } = args;
        console.log(`🔍 Searching web for: ${query}`);
        // Mock search results
        const results = [
            { title: `${query} - Overview`, url: 'https://example.com/1', snippet: `Comprehensive overview of ${query}` },
            { title: `${query} - Latest Research`, url: 'https://example.com/2', snippet: `Recent research on ${query}` },
            { title: `${query} - Best Practices`, url: 'https://example.com/3', snippet: `Best practices for ${query}` }
        ].slice(0, maxResults);
        return JSON.stringify({ query, results });
    },
    analyzeData: async (args) => {
        const { data, analysisType = 'general' } = args;
        console.log(`📊 Analyzing data: ${analysisType} analysis`);
        return JSON.stringify({
            analysisType,
            findings: [`Key insight about ${data}`, `Important trend in ${data}`, `Recommendation based on ${data}`],
            confidence: 0.85
        });
    }
};
const writingTools = {
    generateContent: async (args) => {
        const { topic, style = 'professional', length = 500 } = args;
        console.log(`✍️ Generating ${style} content about: ${topic}`);
        return `# ${topic}

This is a ${style} piece about ${topic}. The content would be approximately ${length} words and cover the key aspects of the topic in a comprehensive manner.

## Key Points
- Important aspect 1 of ${topic}
- Critical consideration 2 about ${topic}  
- Future implications of ${topic}

## Conclusion
${topic} represents an important area that requires careful consideration and strategic approach.`;
    },
    editContent: async (args) => {
        const { content, instructions = 'general editing' } = args;
        console.log(`📝 Editing content with instructions: ${instructions}`);
        return `[EDITED] ${content}\n\n[Editor's note: Content has been refined according to: ${instructions}]`;
    }
};
const calculationTools = {
    calculate: async (args) => {
        const { expression } = args;
        console.log(`🧮 Calculating: ${expression}`);
        try {
            // Simple calculation (in production, use a proper math parser)
            const result = eval(expression.replace(/[^0-9+\-*/().\s]/g, ''));
            return JSON.stringify({ expression, result });
        }
        catch (error) {
            return JSON.stringify({ expression, error: 'Invalid expression' });
        }
    },
    analyzeMetrics: async (args) => {
        const { metrics, type = 'basic' } = args;
        console.log(`📈 Analyzing metrics: ${type} analysis`);
        const sum = metrics.reduce((a, b) => a + b, 0);
        const avg = sum / metrics.length;
        const max = Math.max(...metrics);
        const min = Math.min(...metrics);
        return JSON.stringify({
            type,
            summary: { sum, average: avg, maximum: max, minimum: min },
            insights: [`Average value is ${avg.toFixed(2)}`, `Range spans from ${min} to ${max}`]
        });
    }
};
// --- 2. Create Specialized Worker Agents ---
const createWorkerAgents = () => [
    {
        id: 'researcher',
        name: 'Research Specialist',
        description: 'Conducts comprehensive research and data analysis',
        capabilities: ['web_search', 'data_analysis', 'research', 'analysis'],
        specializations: ['market_research', 'competitive_analysis', 'trend_analysis'],
        graph: (0, templates_1.createResearchAgent)({ tools: researchTools })
    },
    {
        id: 'writer',
        name: 'Content Writer',
        description: 'Creates and edits high-quality content',
        capabilities: ['writing', 'editing', 'content_creation', 'synthesis'],
        specializations: ['technical_writing', 'marketing_copy', 'reports'],
        graph: (0, templates_1.createExecutorAgent)({ tools: writingTools })
    },
    {
        id: 'analyst',
        name: 'Data Analyst',
        description: 'Performs calculations and quantitative analysis',
        capabilities: ['calculation', 'data_analysis', 'metrics', 'quantitative_analysis'],
        specializations: ['financial_analysis', 'statistical_analysis', 'forecasting'],
        graph: (0, templates_1.createExecutorAgent)({ tools: calculationTools })
    }
];
// --- 3. Create the Complete Multi-Agent System ---
const runMultiAgentDemo = async () => {
    console.log("=== AG3NTIC Multi-Agent System Demo ===\n");
    const userGoal = "Create a comprehensive market analysis report for AI-powered productivity tools, including market size, key players, trends, and growth projections.";
    console.log(`🎯 User Goal: ${userGoal}\n`);
    // Step 1: Planning Phase
    console.log("📋 Phase 1: Planning");
    console.log("=".repeat(50));
    const plannerGraph = (0, templates_1.createPlannerAgent)({
        systemMessage: "You are an expert strategic planner. Break down complex business goals into detailed, actionable steps that can be executed by specialized teams."
    });
    const plannerExecutor = new core_1.Executor(plannerGraph);
    const planningResult = await plannerExecutor.execute({
        messages: [{ role: 'user', content: userGoal }]
    });
    console.log(`✅ Planning completed. Generated ${planningResult.plan?.length || 0} steps.\n`);
    if (!planningResult.plan) {
        console.error("❌ Planning failed - no plan generated");
        return;
    }
    // Step 2: Orchestration Phase
    console.log("🎭 Phase 2: Orchestration & Execution");
    console.log("=".repeat(50));
    const workers = createWorkerAgents();
    const orchestratorGraph = (0, templates_1.createOrchestratorAgent)({
        workers,
        systemMessage: "You are managing a team of specialists to execute a comprehensive market analysis. Coordinate their work effectively to produce a high-quality deliverable."
    });
    const orchestratorExecutor = new core_1.Executor(orchestratorGraph);
    // Execute with detailed progress tracking
    let stepCount = 0;
    const orchestrationResult = await orchestratorExecutor.executeWithCallback({
        messages: [{ role: 'user', content: `Execute this plan: ${JSON.stringify(planningResult.plan)}` }],
        plan: planningResult.plan
    }, (state, _step) => {
        stepCount++;
        const lastMsg = (0, lib_1.getLastMessage)(state);
        if (lastMsg?.role === 'assistant') {
            console.log(`Step ${stepCount}: ${lastMsg.content}`);
        }
        // Show execution progress
        if (state.stepExecutions) {
            const completed = state.stepExecutions.filter(exec => exec.status === 'completed').length;
            const total = state.stepExecutions.length;
            console.log(`   Progress: ${completed}/${total} steps completed`);
        }
        console.log(); // Add spacing
    });
    // Step 3: Results Summary
    console.log("📊 Phase 3: Results Summary");
    console.log("=".repeat(50));
    const finalMessage = (0, lib_1.getLastMessage)(orchestrationResult);
    console.log("🎉 Final Result:");
    console.log(finalMessage?.content || "No final result available");
    // Show execution statistics
    if (orchestrationResult.stepExecutions) {
        const stats = {
            total: orchestrationResult.stepExecutions.length,
            completed: orchestrationResult.stepExecutions.filter(e => e.status === 'completed').length,
            failed: orchestrationResult.stepExecutions.filter(e => e.status === 'failed').length,
            skipped: orchestrationResult.stepExecutions.filter(e => e.status === 'skipped').length
        };
        console.log("\n📈 Execution Statistics:");
        console.log(`   Total Steps: ${stats.total}`);
        console.log(`   Completed: ${stats.completed}`);
        console.log(`   Failed: ${stats.failed}`);
        console.log(`   Skipped: ${stats.skipped}`);
        console.log(`   Success Rate: ${((stats.completed / stats.total) * 100).toFixed(1)}%`);
    }
    console.log("\n✨ Multi-agent system execution completed!");
};
exports.runMultiAgentDemo = runMultiAgentDemo;
// --- 4. Simple Template Usage Examples ---
const runSimpleTemplateExamples = async () => {
    console.log("\n=== Simple Template Usage Examples ===\n");
    // Example 1: Simple Executor
    console.log("🔧 Example 1: Simple Executor Agent");
    const simpleExecutor = (0, templates_1.createExecutorAgent)({
        tools: {
            greet: async (args) => `Hello, ${args.name}!`
        }
    });
    const executorResult = await new core_1.Executor(simpleExecutor).execute({
        messages: [{ role: 'user', content: 'Please greet Alice' }]
    });
    console.log("Result:", (0, lib_1.getLastMessage)(executorResult)?.content);
    // Example 2: Simple Planner
    console.log("\n📋 Example 2: Simple Planner Agent");
    const simplePlanner = (0, templates_1.createPlannerAgent)();
    const plannerResult = await new core_1.Executor(simplePlanner).execute({
        messages: [{ role: 'user', content: 'Plan a birthday party for 20 people' }]
    });
    console.log("Plan:", plannerResult.plan?.map(step => step.description).join('\n   '));
    // Example 3: Simple Researcher
    console.log("\n🔍 Example 3: Simple Research Agent");
    const simpleResearcher = (0, templates_1.createResearchAgent)();
    const researchResult = await new core_1.Executor(simpleResearcher).execute({
        messages: [{ role: 'user', content: 'Research the benefits of TypeScript' }]
    });
    console.log("Research findings:", researchResult.findings?.insights.join('\n   '));
};
exports.runSimpleTemplateExamples = runSimpleTemplateExamples;
// Run if this file is executed directly
if (require.main === module) {
    runMultiAgentDemo()
        .then(() => runSimpleTemplateExamples())
        .catch(console.error);
}
//# sourceMappingURL=multi-agent-system.js.map