import { AgentState, MCPMessage, MCPAssistantMessage, NodeFunction } from "../../core/types";
/**
 * OpenRouter API configuration
 */
export interface OpenRouterConfig {
    apiKey: string;
    baseUrl?: string;
    defaultModel?: string;
    defaultTemperature?: number;
    timeout?: number;
}
/**
 * Tool definition for OpenRouter API
 */
export interface OpenRouterTool {
    type: 'function';
    function: {
        name: string;
        description: string;
        parameters: {
            type: 'object';
            properties: Record<string, any>;
            required?: string[];
        };
    };
}
/**
 * Options for creating an OpenRouter agent node
 */
export interface OpenRouterAgentOptions {
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemMessage?: string;
    tools?: OpenRouterTool[];
}
/**
 * OpenRouter API client
 */
export declare class OpenRouterClient {
    private config;
    constructor(config: OpenRouterConfig);
    /**
     * Make a chat completion request to OpenRouter
     */
    createChatCompletion(messages: MCPMessage[], options?: OpenRouterAgentOptions): Promise<MCPAssistantMessage>;
    /**
     * Create an agent node that uses OpenRouter for LLM calls
     */
    createAgentNode<TState extends AgentState>(options?: OpenRouterAgentOptions): NodeFunction<TState>;
    /**
     * Test the connection to OpenRouter
     */
    testConnection(): Promise<boolean>;
    /**
     * Get available models from OpenRouter
     */
    getAvailableModels(): Promise<string[]>;
}
//# sourceMappingURL=client.d.ts.map