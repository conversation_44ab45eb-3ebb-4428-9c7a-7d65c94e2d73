{"version": 3, "file": "template-patterns.js", "sourceRoot": "", "sources": ["../../src/examples/template-patterns.ts"], "names": [], "mappings": ";;AAEA,oCAAoC;;AA6VnB,+BAAe;AA3VhC,kCAAmC;AACnC,gCAUgB;AAEhB;;;;;;;GAOG;AAEH,wCAAwC;AAExC,MAAM,SAAS,GAAyB;IACtC,iBAAiB,EAAE,KAAK,EAAE,IAAyC,EAAE,EAAE;QACrE,OAAO,CAAC,GAAG,CAAC,4BAA4B,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QACzD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,oBAAoB;QAE5E,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;YAC5E,SAAS,EAAE,eAAe;YAC1B,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,SAAS;SAC7B,CAAC;IACJ,CAAC;IAED,SAAS,EAAE,KAAK,EAAE,IAAmD,EAAE,EAAE;QACvE,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,mBAAmB;QAE3E,OAAO;YACL,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;YAC3D,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC;IAED,YAAY,EAAE,KAAK,EAAE,IAA4C,EAAE,EAAE;QACnE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,UAAU,aAAa,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QAEzE,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC;QAClD,OAAO;YACL,cAAc,EAAE,IAAI,CAAC,MAAM;YAC3B,aAAa,EAAE,IAAI,CAAC,UAAU;YAC9B,SAAS,EAAE,GAAG;YACd,WAAW,EAAE,IAAI,CAAC,MAAM,GAAG,GAAG;SAC/B,CAAC;IACJ,CAAC;IAED,cAAc,EAAE,KAAK,EAAE,IAAuC,EAAE,EAAE;QAChE,OAAO,CAAC,GAAG,CAAC,8BAA8B,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QACxD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,oBAAoB;QAE5E,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,OAAO,EAAE;gBACP,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,gBAAgB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;gBAC/D,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,gBAAgB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;gBAC/D,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,gBAAgB,IAAI,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;aAChE;YACD,UAAU,EAAE,CAAC;YACb,aAAa,EAAE,MAAM;SACtB,CAAC;IACJ,CAAC;CACF,CAAC;AAEF,kCAAkC;AAElC,MAAM,iBAAiB,GAAG,CAAC,SAAiB,EAAE,EAAE;IAC9C,OAAO,KAAK,EAAE,KAAU,EAAE,EAAE;QAC1B,MAAM,WAAW,GAAG,IAAA,oBAAc,EAAC,KAAK,CAAC,CAAC;QAE1C,IAAI,QAAgB,CAAC;QACrB,IAAI,SAA4B,CAAC;QAEjC,IAAI,SAAS,KAAK,UAAU,EAAE,CAAC;YAC7B,6CAA6C;YAC7C,IAAI,WAAW,EAAE,IAAI,KAAK,MAAM,EAAE,CAAC;gBACjC,oDAAoD;gBACpD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACnD,QAAQ,GAAG,yDAAyD,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;YAC5G,CAAC;iBAAM,CAAC;gBACN,0DAA0D;gBAC1D,MAAM,OAAO,GAAG,WAAW,EAAE,OAAO,IAAI,EAAE,CAAC;gBAE3C,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;oBAChD,QAAQ,GAAG,iCAAiC,CAAC;oBAC7C,SAAS,GAAG,CAAC;4BACX,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;4BACrD,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE;gCACR,IAAI,EAAE,mBAAmB;gCACzB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;oCACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;oCAC/D,IAAI,EAAE,SAAS;iCAChB,CAAC;6BACH;yBACF,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oBACnD,QAAQ,GAAG,+BAA+B,CAAC;oBAC3C,SAAS,GAAG,CAAC;4BACX,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;4BACrD,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE;gCACR,IAAI,EAAE,WAAW;gCACjB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;oCACxB,EAAE,EAAE,kBAAkB;oCACtB,OAAO,EAAE,YAAY;oCACrB,IAAI,EAAE,0DAA0D;iCACjE,CAAC;6BACH;yBACF,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBACjD,QAAQ,GAAG,iCAAiC,CAAC;oBAC7C,SAAS,GAAG,CAAC;4BACX,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;4BACrD,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE;gCACR,IAAI,EAAE,cAAc;gCACpB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;6BAC1D;yBACF,CAAC,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,QAAQ,GAAG,uDAAuD,CAAC;gBACrE,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YACnC,QAAQ,GAAG,qCAAqC,WAAW,EAAE,OAAO,iMAAiM,CAAC;QACxQ,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,SAAS,SAAS,4BAA4B,WAAW,EAAE,OAAO,GAAG,CAAC;QACnF,CAAC;QAED,MAAM,gBAAgB,GAAG;YACvB,IAAI,EAAE,WAAoB;YAC1B,OAAO,EAAE,QAAQ;YACjB,GAAG,CAAC,SAAS,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;SAC5C,CAAC;QAEF,OAAO,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;IAC7C,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,yBAAyB;AAEzB,KAAK,UAAU,iBAAiB;IAC9B,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACjD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7B,MAAM,OAAO,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC;IAC9C,MAAM,aAAa,GAAG,IAAA,yBAAmB,EAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAC9D,MAAM,QAAQ,GAAG,IAAI,eAAQ,CAAC,aAAa,CAAC,CAAC;IAE7C,MAAM,SAAS,GAAG;QAChB,mCAAmC;QACnC,6CAA6C;QAC7C,mCAAmC;KACpC,CAAC;IAEF,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;QACjC,OAAO,CAAC,GAAG,CAAC,cAAc,QAAQ,EAAE,CAAC,CAAC;QAEtC,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC;YACpC,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;SAChD,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,IAAA,oBAAc,EAAC,MAAM,CAAC,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,iBAAiB,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;IACxD,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;AACrD,CAAC;AAED,KAAK,UAAU,gBAAgB;IAC7B,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7B,MAAM,OAAO,GAAG,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC7C,MAAM,YAAY,GAAG,IAAA,wBAAkB,EAAoB,OAAO,EAAE;QAClE,QAAQ,EAAE,CAAC;QACX,QAAQ,EAAE,YAAY;QACtB,oBAAoB,EAAE,IAAI;KAC3B,CAAC,CAAC;IACH,MAAM,QAAQ,GAAG,IAAI,eAAQ,CAAC,YAAY,CAAC,CAAC;IAE5C,MAAM,KAAK,GAAG;QACZ,2CAA2C;QAC3C,8CAA8C;QAC9C,yCAAyC;KAC1C,CAAC;IAEF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC;QAElC,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC;YACpC,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;SAC5C,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAClC,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YAChB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBAClC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,aAAa,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;gBACxE,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,iBAAiB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzF,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,mBAAmB,MAAM,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;AACpD,CAAC;AAED,KAAK,UAAU,qBAAqB;IAClC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAE7B,4BAA4B;IAC5B,MAAM,OAAO,GAAkB;QAC7B;YACE,EAAE,EAAE,YAAY;YAChB,IAAI,EAAE,qBAAqB;YAC3B,WAAW,EAAE,2CAA2C;YACxD,YAAY,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,gBAAgB,CAAC;YACxD,eAAe,EAAE,CAAC,iBAAiB,EAAE,sBAAsB,CAAC;YAC5D,KAAK,EAAE,IAAA,yBAAmB,EAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE,EAAE,cAAc,EAAE,SAAS,CAAC,cAAc,EAAE,CAAC;SAC1G;QACD;YACE,EAAE,EAAE,cAAc;YAClB,IAAI,EAAE,0BAA0B;YAChC,WAAW,EAAE,iCAAiC;YAC9C,YAAY,EAAE,CAAC,eAAe,EAAE,OAAO,EAAE,WAAW,CAAC;YACrD,eAAe,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,CAAC;YACxD,KAAK,EAAE,IAAA,yBAAmB,EAAC,iBAAiB,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,SAAS,EAAE,CAAC;SAClG;QACD;YACE,EAAE,EAAE,SAAS;YACb,IAAI,EAAE,cAAc;YACpB,WAAW,EAAE,yCAAyC;YACtD,YAAY,EAAE,CAAC,aAAa,EAAE,UAAU,EAAE,WAAW,CAAC;YACtD,eAAe,EAAE,CAAC,oBAAoB,EAAE,sBAAsB,CAAC;YAC/D,KAAK,EAAE,IAAA,yBAAmB,EAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,YAAY,EAAE,SAAS,CAAC,YAAY,EAAE,CAAC;SACnG;KACF,CAAC;IAEF,MAAM,OAAO,GAAG,iBAAiB,CAAC,cAAc,CAAC,CAAC;IAClD,MAAM,iBAAiB,GAAG,IAAA,6BAAuB,EAAyB,OAAO,EAAE,OAAO,EAAE;QAC1F,kBAAkB,EAAE,CAAC;QACrB,gBAAgB,EAAE,IAAI;QACtB,cAAc,EAAE,KAAK;KACtB,CAAC,CAAC;IAEH,uCAAuC;IACvC,MAAM,YAAY,GAAG,IAAA,wBAAkB,EAAoB,iBAAiB,CAAC,SAAS,CAAC,EAAE;QACvF,QAAQ,EAAE,CAAC;QACX,QAAQ,EAAE,YAAY;KACvB,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,MAAM,UAAU,GAAG,MAAM,IAAI,eAAQ,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC;QAC1D,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,gEAAgE,EAAE,CAAC;KACxG,CAAC,CAAC;IAEH,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,MAAM,oBAAoB,GAAG,IAAI,eAAQ,CAAC,iBAAiB,CAAC,CAAC;IAE7D,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QACrB,OAAO,CAAC,KAAK,CAAC,wDAAwD,CAAC,CAAC;QACxE,OAAO;IACT,CAAC;IAED,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,MAAM,MAAM,GAAG,MAAM,oBAAoB,CAAC,mBAAmB,CAC3D;QACE,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,6CAA6C,EAAE,CAAC;QACpF,IAAI,EAAE,UAAU,CAAC,IAAI;KACtB,EACD,CAAC,KAAK,EAAE,EAAE;QACR,SAAS,EAAE,CAAC;QACZ,OAAO,CAAC,GAAG,CAAC,WAAW,SAAS,iBAAiB,CAAC,CAAC;QAEnD,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;YACzB,MAAM,SAAS,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,MAAM,CAAC;YAC1F,MAAM,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,gBAAgB,SAAS,IAAI,KAAK,kBAAkB,CAAC,CAAC;QACpE,CAAC;IACH,CAAC,CACF,CAAC;IAEF,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC3C,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;QAC1B,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;YACjD,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBACzC,SAAS,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YACxD,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM,SAAS,KAAK,GAAG,CAAC,KAAK,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;YACzF,IAAI,SAAS,CAAC,cAAc,EAAE,CAAC;gBAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,cAAc,CAAC,CAAC;gBACpE,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,EAAE,IAAI,IAAI,SAAS,CAAC,cAAc,EAAE,CAAC,CAAC;YAChF,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,oBAAoB,MAAM,CAAC,mBAAmB,EAAE,CAAC,CAAC;IAC9D,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;AACzD,CAAC;AAED,6BAA6B;AAE7B,KAAK,UAAU,IAAI;IACjB,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IACjD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7B,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;IAE/D,IAAI,CAAC;QACH,MAAM,iBAAiB,EAAE,CAAC;QAC1B,MAAM,gBAAgB,EAAE,CAAC;QACzB,MAAM,qBAAqB,EAAE,CAAC;QAE9B,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;QAC1E,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CAAC,sEAAsE,CAAC,CAAC;QACpF,OAAO,CAAC,GAAG,CAAC,qFAAqF,CAAC,CAAC;IAErG,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC;AACH,CAAC;AAED,eAAe;AACf,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,IAAI,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC"}