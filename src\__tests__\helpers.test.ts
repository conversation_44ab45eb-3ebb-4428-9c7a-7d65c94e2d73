// src/__tests__/helpers.test.ts

import {
  getLastMessage,
  addMessage,
  getToolCalls,
  hasToolCalls,
  createToolNode,
  shouldCallTools,
  getMessagesByRole,
  countMessagesByRole
} from '../core/helpers';
import { AgentState, MCPMessage } from '../core/types';

interface TestState extends AgentState {
  messages: MCPMessage[];
}

describe('Helper Functions', () => {
  describe('getLastMessage', () => {
    it('should return the last message', () => {
      const state: TestState = {
        messages: [
          { role: 'user', content: 'Hello' },
          { role: 'assistant', content: 'Hi there!' }
        ]
      };
      
      const lastMessage = getLastMessage(state);
      expect(lastMessage).toEqual({ role: 'assistant', content: 'Hi there!' });
    });

    it('should return undefined for empty messages', () => {
      const state: TestState = { messages: [] };
      const lastMessage = getLastMessage(state);
      expect(lastMessage).toBeUndefined();
    });
  });

  describe('addMessage', () => {
    it('should add a message to the state', () => {
      const state: TestState = {
        messages: [{ role: 'user', content: 'Hello' }]
      };
      
      const newMessage: MCPMessage = { role: 'assistant', content: 'Hi!' };
      const update = addMessage(state, newMessage);
      
      expect(update.messages).toHaveLength(2);
      expect(update.messages![1]).toEqual(newMessage);
    });

    it('should not mutate the original state', () => {
      const state: TestState = {
        messages: [{ role: 'user', content: 'Hello' }]
      };
      
      const originalLength = state.messages.length;
      addMessage(state, { role: 'assistant', content: 'Hi!' });
      
      expect(state.messages).toHaveLength(originalLength);
    });
  });

  describe('getToolCalls', () => {
    it('should return tool calls from assistant message', () => {
      const state: TestState = {
        messages: [{
          role: 'assistant',
          content: null,
          tool_calls: [{
            id: 'call_1',
            type: 'function',
            function: { name: 'test_tool', arguments: '{}' }
          }]
        }]
      };
      
      const toolCalls = getToolCalls(state);
      expect(toolCalls).toHaveLength(1);
      expect(toolCalls[0].id).toBe('call_1');
    });

    it('should return empty array when no tool calls', () => {
      const state: TestState = {
        messages: [{ role: 'assistant', content: 'Hello' }]
      };
      
      const toolCalls = getToolCalls(state);
      expect(toolCalls).toEqual([]);
    });

    it('should return empty array when last message is not assistant', () => {
      const state: TestState = {
        messages: [{ role: 'user', content: 'Hello' }]
      };
      
      const toolCalls = getToolCalls(state);
      expect(toolCalls).toEqual([]);
    });
  });

  describe('hasToolCalls', () => {
    it('should return true when tool calls exist', () => {
      const state: TestState = {
        messages: [{
          role: 'assistant',
          content: null,
          tool_calls: [{
            id: 'call_1',
            type: 'function',
            function: { name: 'test_tool', arguments: '{}' }
          }]
        }]
      };
      
      expect(hasToolCalls(state)).toBe(true);
    });

    it('should return false when no tool calls exist', () => {
      const state: TestState = {
        messages: [{ role: 'assistant', content: 'Hello' }]
      };
      
      expect(hasToolCalls(state)).toBe(false);
    });
  });

  describe('createToolNode', () => {
    const mockTools = {
      add: async (args: { a: number; b: number }) => args.a + args.b,
      greet: async (args: { name: string }) => `Hello, ${args.name}!`,
      error_tool: async () => { throw new Error('Tool error'); }
    };

    it('should execute tools and return results', async () => {
      const toolNode = createToolNode<TestState>(mockTools);
      
      const state: TestState = {
        messages: [{
          role: 'assistant',
          content: null,
          tool_calls: [{
            id: 'call_1',
            type: 'function',
            function: { name: 'add', arguments: '{"a": 5, "b": 3}' }
          }]
        }]
      };
      
      const result = await toolNode(state);

      expect(result.messages).toHaveLength(2);
      expect(result.messages![1]).toEqual({
        role: 'tool',
        tool_call_id: 'call_1',
        content: '8'
      });
    });

    it('should handle multiple tool calls', async () => {
      const toolNode = createToolNode<TestState>(mockTools);
      
      const state: TestState = {
        messages: [{
          role: 'assistant',
          content: null,
          tool_calls: [
            {
              id: 'call_1',
              type: 'function',
              function: { name: 'add', arguments: '{"a": 5, "b": 3}' }
            },
            {
              id: 'call_2',
              type: 'function',
              function: { name: 'greet', arguments: '{"name": "Alice"}' }
            }
          ]
        }]
      };
      
      const result = await toolNode(state);

      expect(result.messages).toHaveLength(3);
      expect(result.messages![1].content).toBe('8');
      expect(result.messages![2].content).toBe('Hello, Alice!');
    });

    it('should handle tool execution errors', async () => {
      const toolNode = createToolNode<TestState>(mockTools);
      
      const state: TestState = {
        messages: [{
          role: 'assistant',
          content: null,
          tool_calls: [{
            id: 'call_1',
            type: 'function',
            function: { name: 'error_tool', arguments: '{}' }
          }]
        }]
      };
      
      const result = await toolNode(state);

      expect(result.messages![1].content).toContain('Error executing tool "error_tool": Tool error');
    });

    it('should handle unknown tools', async () => {
      const toolNode = createToolNode<TestState>(mockTools);
      
      const state: TestState = {
        messages: [{
          role: 'assistant',
          content: null,
          tool_calls: [{
            id: 'call_1',
            type: 'function',
            function: { name: 'unknown_tool', arguments: '{}' }
          }]
        }]
      };
      
      const result = await toolNode(state);

      expect(result.messages![1].content).toContain('Tool "unknown_tool" not found');
    });

    it('should throw error when called without tool calls', async () => {
      const toolNode = createToolNode<TestState>(mockTools);
      
      const state: TestState = {
        messages: [{ role: 'user', content: 'Hello' }]
      };
      
      await expect(toolNode(state)).rejects.toThrow(
        'Tool node called but last message is not an assistant message with tool calls'
      );
    });
  });

  describe('shouldCallTools', () => {
    it('should return "tools" when tool calls exist', () => {
      const state: TestState = {
        messages: [{
          role: 'assistant',
          content: null,
          tool_calls: [{
            id: 'call_1',
            type: 'function',
            function: { name: 'test_tool', arguments: '{}' }
          }]
        }]
      };
      
      expect(shouldCallTools(state)).toBe('tools');
    });

    it('should return "__end__" when no tool calls exist', () => {
      const state: TestState = {
        messages: [{ role: 'assistant', content: 'Hello' }]
      };
      
      expect(shouldCallTools(state)).toBe('__end__');
    });
  });

  describe('getMessagesByRole', () => {
    it('should filter messages by role', () => {
      const state: TestState = {
        messages: [
          { role: 'user', content: 'Hello' },
          { role: 'assistant', content: 'Hi!' },
          { role: 'user', content: 'How are you?' },
          { role: 'assistant', content: 'Good!' }
        ]
      };
      
      const userMessages = getMessagesByRole(state, 'user');
      expect(userMessages).toHaveLength(2);
      expect(userMessages[0].content).toBe('Hello');
      expect(userMessages[1].content).toBe('How are you?');
    });
  });

  describe('countMessagesByRole', () => {
    it('should count messages by role', () => {
      const state: TestState = {
        messages: [
          { role: 'user', content: 'Hello' },
          { role: 'assistant', content: 'Hi!' },
          { role: 'user', content: 'How are you?' }
        ]
      };
      
      expect(countMessagesByRole(state, 'user')).toBe(2);
      expect(countMessagesByRole(state, 'assistant')).toBe(1);
      expect(countMessagesByRole(state, 'system')).toBe(0);
    });
  });
});
