{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../../src/integrations/anthropic/client.ts"], "names": [], "mappings": ";AAAA,uCAAuC;;;AAkOvC,sDASC;AAnOD,2DAAqD;AA0BrD;;;GAGG;AACH,MAAa,eAAe;IAG1B,YAAY,iBAAsB;QAChC,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACjB,QAAsB,EACtB,UAAiC,EAAE;QAEnC,MAAM,EACJ,KAAK,GAAG,4BAA4B,EACpC,WAAW,GAAG,GAAG,EACjB,SAAS,GAAG,IAAI,EAChB,aAAa,EACb,KAAK,EACN,GAAG,OAAO,CAAC;QAEZ,qDAAqD;QACrD,MAAM,oBAAoB,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;QAC3E,MAAM,aAAa,GAAG,aAAa,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE,OAAO,CAAC;QAE5F,2CAA2C;QAC3C,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACvD,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBACxB,OAAO;oBACL,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,aAAa;4BACnB,WAAW,EAAE,GAAG,CAAC,YAAY;4BAC7B,OAAO,EAAE,GAAG,CAAC,OAAO;yBACrB,CAAC;iBACH,CAAC;YACJ,CAAC;YAED,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;gBAC/C,MAAM,OAAO,GAAG,EAAE,CAAC;gBAEnB,8BAA8B;gBAC9B,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;oBAChB,OAAO,CAAC,IAAI,CAAC;wBACX,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,GAAG,CAAC,OAAO;qBAClB,CAAC,CAAC;gBACL,CAAC;gBAED,sBAAsB;gBACtB,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBAC1B,OAAO,CAAC,IAAI,CAAC;wBACX,IAAI,EAAE,UAAU;wBAChB,EAAE,EAAE,EAAE,CAAC,EAAE;wBACT,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;wBACtB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;qBACzC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,OAAO;oBACL,IAAI,EAAE,WAAW;oBACjB,OAAO;iBACR,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW;gBAChD,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,EAAE;aAC3B,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAQ;YACzB,KAAK;YACL,QAAQ,EAAE,iBAAiB;YAC3B,WAAW;YACX,UAAU,EAAE,SAAS;SACtB,CAAC;QAEF,iCAAiC;QACjC,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,CAAC,MAAM,GAAG,aAAa,CAAC;QACvC,CAAC;QAED,wBAAwB;QACxB,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAErE,2CAA2C;YAC3C,IAAI,OAAO,GAAkB,IAAI,CAAC;YAClC,IAAI,UAAU,GAAsB,SAAS,CAAC;YAE9C,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;gBAClF,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC;gBAEtF,uBAAuB;gBACvB,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC1B,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClE,CAAC;gBAED,wCAAwC;gBACxC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC1B,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;wBAC3C,EAAE,EAAE,KAAK,CAAC,EAAE;wBACZ,IAAI,EAAE,UAAU;wBAChB,QAAQ,EAAE;4BACR,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;yBACvC;qBACF,CAAC,CAAC,CAAC;gBACN,CAAC;YACH,CAAC;YAED,MAAM,gBAAgB,GAAwB;gBAC5C,IAAI,EAAE,WAAW;gBACjB,OAAO;gBACP,GAAG,CAAC,UAAU,IAAI,EAAE,UAAU,EAAE,CAAC;aAClC,CAAC;YAEF,OAAO,gBAAgB,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACpG,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CACb,UAAiC,EAAE;QAEnC,OAAO,KAAK,EAAE,KAAa,EAA4B,EAAE;YACvD,IAAI,CAAC;gBACH,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAC3E,OAAO,IAAA,0BAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAE7C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACtG,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,WAAW,GAAe;gBAC9B,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,mCAAmC;aAC7C,CAAC;YAEF,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,WAAW,CAAC,EAAE;gBACtC,KAAK,EAAE,yBAAyB;gBAChC,SAAS,EAAE,EAAE;aACd,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO;YACL,4BAA4B;YAC5B,2BAA2B;YAC3B,wBAAwB;YACxB,0BAA0B;YAC1B,yBAAyB;SAC1B,CAAC;IACJ,CAAC;CACF;AAtLD,0CAsLC;AAED;;;GAGG;AACH,SAAgB,qBAAqB,CAAC,MAAc;IAClD,IAAI,CAAC;QACH,gEAAgE;QAChE,MAAM,SAAS,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;QAC/C,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;QAC5C,OAAO,IAAI,eAAe,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,gFAAgF,CAAC,CAAC;IACpG,CAAC;AACH,CAAC"}