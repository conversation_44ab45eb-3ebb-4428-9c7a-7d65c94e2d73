"use strict";
// src/examples/weather-agent.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.weatherGraph = exports.runWeatherAgent = void 0;
const core_1 = require("../core");
const lib_1 = require("../lib");
// --- 2. Define the Tools ---
// A simple, synchronous tool function.
const get_current_weather = (location, unit = 'fahrenheit') => {
    if (location.toLowerCase().includes("tokyo")) {
        return { location: "Tokyo", temperature: unit === 'celsius' ? "10" : "50", unit };
    }
    if (location.toLowerCase().includes("san francisco")) {
        return { location: "San Francisco", temperature: unit === 'celsius' ? "18" : "72", unit };
    }
    return { location, temperature: "unknown", unit };
};
// --- 3. Define the Nodes ---
// This is our "Agent" node. It's a function that simulates an LLM call.
const agentNode = async (state) => {
    const lastMessage = state.messages[state.messages.length - 1];
    console.log(`\n🤖 Agent is thinking based on: "${lastMessage.role}: ${lastMessage.content}"`);
    // Simulate LLM deciding to call a tool.
    if (lastMessage.role === 'user') {
        const assistantMessage = {
            role: 'assistant',
            content: null,
            tool_calls: [{
                    id: "call_123",
                    type: 'function',
                    function: {
                        name: 'get_current_weather',
                        arguments: JSON.stringify({ location: 'Tokyo' })
                    }
                }]
        };
        return (0, lib_1.addMessage)(state, assistantMessage);
    }
    // Simulate LLM summarizing the tool's response.
    if (lastMessage.role === 'tool') {
        const assistantMessage = {
            role: 'assistant',
            content: `The weather in Tokyo is 10 degrees Celsius.`
        };
        return (0, lib_1.addMessage)(state, assistantMessage);
    }
    return {};
};
// This is our "Tool" node. It executes the actual tool function.
const toolNode = async (state) => {
    const lastMessage = state.messages[state.messages.length - 1];
    const toolCall = lastMessage.tool_calls[0];
    const toolName = toolCall.function.name;
    const args = JSON.parse(toolCall.function.arguments);
    console.log(`\n🛠️ Calling tool "${toolName}" with args:`, args);
    if (toolName === 'get_current_weather') {
        const result = get_current_weather(args.location);
        const newMessages = [
            ...state.messages,
            { role: 'tool', tool_call_id: toolCall.id, content: JSON.stringify(result) }
        ];
        return { messages: newMessages };
    }
    throw new Error(`Tool "${toolName}" not found.`);
};
// --- 4. Define the Graph and Logic ---
// The conditional router decides if we should call a tool or end the process.
const shouldCallTool = (state) => {
    const lastMessage = state.messages[state.messages.length - 1];
    if (lastMessage.role === 'assistant' && lastMessage.tool_calls) {
        return 'call_tool';
    }
    return '__end__';
};
// Instantiate the graph
const weatherGraph = new core_1.Graph()
    .addNode('agent', agentNode)
    .addNode('tools', toolNode)
    .addConditionalEdge('agent', shouldCallTool, {
    'call_tool': 'tools',
    '__end__': '__END__'
})
    .addEdge('tools', 'agent'); // Loop back to the agent after the tool runs
exports.weatherGraph = weatherGraph;
// --- 5. Run it! ---
const main = async () => {
    console.log("--- Starting Weather Agent ---");
    const initialState = {
        topic: "weather",
        messages: [{
                role: 'user',
                content: 'What is the weather in Tokyo?'
            }]
    };
    const executor = new core_1.Executor(weatherGraph);
    const finalState = await executor.execute(initialState);
    console.log("\n--- Final Result ---");
    console.log(finalState.messages[finalState.messages.length - 1].content);
};
exports.runWeatherAgent = main;
// Run if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=weather-agent.js.map