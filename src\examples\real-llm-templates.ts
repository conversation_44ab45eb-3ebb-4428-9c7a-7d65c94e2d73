#!/usr/bin/env -S npm run tsn -T

// src/examples/real-llm-templates.ts

import { Executor } from '../core';
import { 
  createExecutorAgent, 
  createPlannerAgent, 
  createOrchestratorAgent,
  getLastMessage,
  Tool,
  WorkerAgent,
  PlannerAgentState,
  OrchestratorAgentState
} from '../lib';

/**
 * Real LLM Template Patterns Demo
 * 
 * This example demonstrates how to use AG3NTIC templates with real LLM integrations.
 * To run with actual LLMs, you would:
 * 1. Install the appropriate SDK: npm install openai
 * 2. Set your API key: export OPENAI_API_KEY=your_key
 * 3. Use the LLM client to create agent nodes
 */

// --- Real Tools for Demonstration ---

const realTools: Record<string, Tool> = {
  getCurrentWeather: async (args: { location: string; unit?: string }) => {
    console.log(`🌤️  Getting weather for ${args.location}`);
    
    // Simulate API call to weather service
    const weatherData = {
      location: args.location,
      temperature: args.location.toLowerCase().includes('tokyo') ? '22°C' : '18°C',
      condition: 'Partly cloudy',
      humidity: '65%',
      windSpeed: '12 km/h',
      unit: args.unit || 'celsius'
    };
    
    return weatherData;
  },

  sendNotification: async (args: { recipient: string; message: string; priority?: string }) => {
    console.log(`📱 Sending ${args.priority || 'normal'} priority notification to ${args.recipient}`);
    
    // Simulate notification service
    return {
      notificationId: 'notif_' + Math.random().toString(36).substr(2, 9),
      status: 'sent',
      recipient: args.recipient,
      timestamp: new Date().toISOString(),
      priority: args.priority || 'normal'
    };
  },

  searchKnowledgeBase: async (args: { query: string; category?: string }) => {
    console.log(`🔍 Searching knowledge base for: ${args.query}`);
    
    // Simulate knowledge base search
    return {
      query: args.query,
      category: args.category,
      results: [
        {
          id: 'kb_001',
          title: `Understanding ${args.query}`,
          content: `Comprehensive guide about ${args.query} with detailed explanations and examples.`,
          relevance: 0.95,
          source: 'Internal Documentation'
        },
        {
          id: 'kb_002', 
          title: `Best Practices for ${args.query}`,
          content: `Industry best practices and recommendations for ${args.query}.`,
          relevance: 0.87,
          source: 'Best Practices Guide'
        }
      ],
      totalResults: 2
    };
  },

  executeWorkflow: async (args: { workflowName: string; parameters?: Record<string, any> }) => {
    console.log(`⚙️  Executing workflow: ${args.workflowName}`);
    
    // Simulate workflow execution
    return {
      workflowId: 'wf_' + Math.random().toString(36).substr(2, 9),
      name: args.workflowName,
      status: 'completed',
      parameters: args.parameters || {},
      executionTime: '2.3s',
      result: `Workflow ${args.workflowName} completed successfully`
    };
  }
};

// --- Agent Node Creation Functions ---

/**
 * Create an agent node that would integrate with a real LLM
 * In production, this would use OpenAI, Anthropic, or other LLM providers
 */
const createLLMAgentNode = (systemMessage: string, agentType: string = 'general') => {
  return async (state: any) => {
    const lastMessage = getLastMessage(state);

    // In a real implementation, this would call an actual LLM API
    // For example:
    // const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
    // const response = await openai.chat.completions.create({
    //   model: 'gpt-4',
    //   messages: [
    //     { role: 'system', content: systemMessage },
    //     ...state.messages
    //   ],
    //   tools: tools // if applicable
    // });

    console.log(`🤖 Agent (${agentType}) with system: ${systemMessage.substring(0, 50)}...`);
    
    let response: string;
    let toolCalls: any[] | undefined;

    if (agentType === 'executor') {
      // Check if the last message is a tool result
      if (lastMessage?.role === 'tool') {
        const toolResult = JSON.parse(lastMessage.content);
        response = `Perfect! I've successfully completed the task. Here's what I accomplished:\n\n${JSON.stringify(toolResult, null, 2)}\n\nIs there anything else you'd like me to help you with?`;
      } else {
        // Analyze user request and decide which tools to use
        const content = lastMessage?.content || '';
        
        if (content.toLowerCase().includes('weather')) {
          response = "I'll check the current weather conditions for you.";
          const location = content.includes('Tokyo') ? 'Tokyo' : 
                          content.includes('London') ? 'London' : 'San Francisco';
          toolCalls = [{
            id: 'call_' + Math.random().toString(36).substr(2, 9),
            type: 'function',
            function: {
              name: 'getCurrentWeather',
              arguments: JSON.stringify({ location, unit: 'celsius' })
            }
          }];
        } else if (content.toLowerCase().includes('notification') || content.toLowerCase().includes('notify')) {
          response = "I'll send a notification for you.";
          toolCalls = [{
            id: 'call_' + Math.random().toString(36).substr(2, 9),
            type: 'function',
            function: {
              name: 'sendNotification',
              arguments: JSON.stringify({
                recipient: '<EMAIL>',
                message: 'Task completed successfully',
                priority: 'normal'
              })
            }
          }];
        } else if (content.toLowerCase().includes('search') || content.toLowerCase().includes('find')) {
          response = "I'll search our knowledge base for relevant information.";
          const query = content.replace(/search|find|for|about/gi, '').trim();
          toolCalls = [{
            id: 'call_' + Math.random().toString(36).substr(2, 9),
            type: 'function',
            function: {
              name: 'searchKnowledgeBase',
              arguments: JSON.stringify({ query: query || 'general information' })
            }
          }];
        } else if (content.toLowerCase().includes('workflow') || content.toLowerCase().includes('process')) {
          response = "I'll execute the requested workflow for you.";
          toolCalls = [{
            id: 'call_' + Math.random().toString(36).substr(2, 9),
            type: 'function',
            function: {
              name: 'executeWorkflow',
              arguments: JSON.stringify({
                workflowName: 'data-processing-pipeline',
                parameters: { source: 'user-request' }
              })
            }
          }];
        } else {
          response = `I understand you want me to help with: "${content}". I'm ready to assist you with this task using my available tools and capabilities.`;
        }
      }
    } else if (agentType === 'planner') {
      const goal = lastMessage?.content || '';
      response = `I'll create a comprehensive strategic plan for: "${goal}"\n\n## Strategic Plan\n\n1. **Analysis & Research Phase**\n   - Gather requirements and constraints\n   - Analyze current state and resources\n   - Research best practices and solutions\n\n2. **Design & Planning Phase**\n   - Create detailed specifications\n   - Design system architecture\n   - Plan resource allocation\n\n3. **Implementation Phase**\n   - Execute development tasks\n   - Implement core functionality\n   - Integrate components\n\n4. **Testing & Validation Phase**\n   - Conduct comprehensive testing\n   - Validate against requirements\n   - Performance optimization\n\n5. **Deployment & Monitoring Phase**\n   - Deploy to production\n   - Monitor system performance\n   - Gather feedback and iterate\n\nThis plan provides a structured approach to achieving your goal efficiently and effectively.`;
    } else if (agentType === 'orchestrator') {
      response = `As the orchestrator, I'm analyzing the plan and coordinating the execution across our specialized worker agents. I'll ensure optimal task distribution based on each worker's capabilities and current availability.`;
    } else {
      response = `I'm a ${agentType} agent ready to help with: "${lastMessage?.content}". I'll use my specialized knowledge and tools to provide the best possible assistance.`;
    }

    const assistantMessage = {
      role: 'assistant' as const,
      content: response,
      ...(toolCalls && { tool_calls: toolCalls })
    };

    return { messages: [...state.messages, assistantMessage] };
  };
};

// --- Demo Functions ---

async function demoRealExecutorAgent() {
  console.log("🔥 Demo 1: Real Executor Agent with Tools");
  console.log("=" .repeat(50));

  const llmNode = createLLMAgentNode(
    'You are a helpful assistant that can use various tools to complete tasks efficiently. Always be clear about what you\'re doing and provide detailed results.',
    'executor'
  );
  
  const executorGraph = createExecutorAgent(llmNode, realTools);
  const executor = new Executor(executorGraph);

  const testCases = [
    "What's the weather like in Tokyo?",
    "Send a notification about the project completion",
    "Search for information about TypeScript best practices",
    "Execute the data processing workflow"
  ];

  for (const testCase of testCases) {
    console.log(`\n📝 User: ${testCase}`);
    
    const result = await executor.execute({
      messages: [{ role: 'user', content: testCase }]
    });

    const finalMessage = getLastMessage(result);
    console.log(`🤖 Assistant: ${finalMessage?.content}`);
  }

  console.log("\n✅ Real Executor Agent Demo Complete!\n");
}

async function demoRealPlannerAgent() {
  console.log("🔥 Demo 2: Real Planner Agent");
  console.log("=" .repeat(50));

  const llmNode = createLLMAgentNode(
    'You are an expert strategic planner. Create detailed, actionable plans that break down complex goals into manageable steps with clear dependencies and timelines.',
    'planner'
  );
  
  const plannerGraph = createPlannerAgent<PlannerAgentState>(llmNode, {
    maxSteps: 8,
    strategy: 'sequential',
    includeTimeEstimates: true
  });
  
  const executor = new Executor(plannerGraph);

  const goals = [
    "Build a customer support chatbot using AI",
    "Launch a new e-commerce platform",
    "Implement a company-wide digital transformation"
  ];

  for (const goal of goals) {
    console.log(`\n📝 Goal: ${goal}`);
    
    const result = await executor.execute({
      messages: [{ role: 'user', content: goal }]
    });

    const finalMessage = getLastMessage(result);
    console.log(`📋 Plan: ${finalMessage?.content}`);
    
    if (result.plan) {
      console.log(`\n📊 Generated ${result.plan.length} detailed steps`);
      console.log(`🎯 Strategy: ${result.planningStrategy}`);
    }
  }

  console.log("\n✅ Real Planner Agent Demo Complete!\n");
}

async function demoRealOrchestratorAgent() {
  console.log("🔥 Demo 3: Real Orchestrator Agent");
  console.log("=" .repeat(50));

  // Create specialized worker agents
  const workers: WorkerAgent[] = [
    {
      id: 'knowledge-worker',
      name: 'Knowledge Specialist',
      description: 'Searches and analyzes information from knowledge bases',
      capabilities: ['search', 'analysis', 'research'],
      specializations: ['knowledge_management', 'information_retrieval'],
      graph: createExecutorAgent(
        createLLMAgentNode('You are a knowledge specialist', 'executor'),
        { searchKnowledgeBase: realTools.searchKnowledgeBase }
      )
    },
    {
      id: 'notification-worker',
      name: 'Communication Specialist', 
      description: 'Handles all notification and communication tasks',
      capabilities: ['communication', 'notifications', 'messaging'],
      specializations: ['user_communication', 'system_alerts'],
      graph: createExecutorAgent(
        createLLMAgentNode('You are a communication specialist', 'executor'),
        { sendNotification: realTools.sendNotification }
      )
    },
    {
      id: 'workflow-worker',
      name: 'Automation Specialist',
      description: 'Executes workflows and automated processes',
      capabilities: ['automation', 'workflows', 'processing'],
      specializations: ['process_automation', 'workflow_management'],
      graph: createExecutorAgent(
        createLLMAgentNode('You are an automation specialist', 'executor'),
        { executeWorkflow: realTools.executeWorkflow }
      )
    }
  ];

  const orchestratorNode = createLLMAgentNode(
    'You are an expert orchestrator coordinating specialized workers to complete complex tasks efficiently.',
    'orchestrator'
  );
  
  const orchestratorGraph = createOrchestratorAgent<OrchestratorAgentState>(
    orchestratorNode,
    workers,
    {
      maxConcurrentTasks: 2,
      retryFailedTasks: true,
      timeoutPerTask: 30000
    }
  );

  console.log("🎭 Orchestrating a complex multi-step task...");
  
  const executor = new Executor(orchestratorGraph);
  const result = await executor.execute({
    messages: [{ 
      role: 'user', 
      content: 'Research customer feedback trends, notify stakeholders, and execute the improvement workflow'
    }]
  });

  const finalMessage = getLastMessage(result);
  console.log(`🎯 Orchestration Result: ${finalMessage?.content}`);

  console.log("\n✅ Real Orchestrator Agent Demo Complete!\n");
}

// --- Main Demo Function ---

async function main() {
  console.log("🚀 AG3NTIC Real LLM Template Patterns Demo");
  console.log("=" .repeat(60));
  console.log("Demonstrating production-ready agent templates:\n");

  try {
    await demoRealExecutorAgent();
    await demoRealPlannerAgent();
    await demoRealOrchestratorAgent();

    console.log("🎉 All Real LLM Template Patterns Demonstrated Successfully!");
    console.log("\n💡 Production Integration Notes:");
    console.log("   🔌 Replace createLLMAgentNode with actual LLM API calls");
    console.log("   🔑 Set up proper API keys and authentication");
    console.log("   🛠️  Implement real tools that integrate with your systems");
    console.log("   📊 Add proper error handling and retry logic");
    console.log("   🔍 Implement logging and monitoring for production use");
    console.log("\n🏗️  These templates provide the foundation for sophisticated AI systems!");

  } catch (error) {
    console.error("❌ Demo failed:", error);
  }
}

// Run the demo
if (require.main === module) {
  main().catch(console.error);
}

export { main as runRealLLMDemo };
