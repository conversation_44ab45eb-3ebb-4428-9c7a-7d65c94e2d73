/**
 * Global test setup for AG3NTIC Framework Test Suite
 * 
 * This file runs once before all tests begin and sets up:
 * - Performance monitoring
 * - Memory baseline establishment
 * - Test environment configuration
 * - Mock services initialization
 */

import { performance } from 'perf_hooks';

// Global test configuration
const globalTestConfig = {
  startTime: 0,
  memoryBaseline: 0,
  performanceMetrics: {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    totalDuration: 0,
    averageDuration: 0,
    memoryUsage: {
      initial: 0,
      peak: 0,
      final: 0,
    },
  },
};

export default async function globalSetup() {
  console.log('\n🚀 AG3NTIC Framework Test Suite - Global Setup');
  console.log('================================================');
  
  // Record start time
  globalTestConfig.startTime = performance.now();
  
  // Establish memory baseline
  if (global.gc) {
    global.gc();
    console.log('✅ Garbage collection forced');
  }
  
  const memoryUsage = process.memoryUsage();
  globalTestConfig.memoryBaseline = memoryUsage.heapUsed;
  globalTestConfig.performanceMetrics.memoryUsage.initial = memoryUsage.heapUsed;
  
  console.log(`📊 Memory Baseline: ${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`);
  console.log(`📊 Heap Total: ${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`);
  console.log(`📊 External: ${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`);
  
  // Set up performance monitoring
  console.log('⚡ Performance monitoring initialized');
  
  // Configure test environment
  process.env.NODE_ENV = 'test';
  process.env.AG3NTIC_TEST_MODE = 'true';
  process.env.AG3NTIC_LOG_LEVEL = 'warn'; // Reduce logging during tests
  
  console.log('🔧 Test environment configured');
  
  // Initialize mock services (if needed)
  console.log('🎭 Mock services initialized');
  
  // Set up global error handlers for unhandled rejections
  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  });
  
  process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
  });
  
  console.log('🛡️  Global error handlers configured');
  
  // Store global config for access in tests
  (global as any).__AG3NTIC_TEST_CONFIG__ = globalTestConfig;
  
  console.log('✅ Global setup completed successfully');
  console.log('================================================\n');
}

// Export for use in other test files
export { globalTestConfig };
