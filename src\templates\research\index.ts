// src/templates/research/index.ts

import { Graph, AgentState, NodeFunction } from '../../core';
import { createToolNode, shouldCallTools, ToolMap, addMessage, getLastMessage } from '../../lib';

/**
 * Configuration options for the Research Agent
 */
export interface ResearchAgentOptions {
  /** Research tools available */
  tools?: ToolMap;
  /** System message for research */
  systemMessage?: string;
  /** Custom research node function */
  researchNode?: NodeFunction<any>;
  /** Maximum number of research iterations */
  maxIterations?: number;
  /** Research depth (1-5, where 5 is most thorough) */
  researchDepth?: number;
  /** Whether to enable parallel research */
  enableParallelResearch?: boolean;
}

/**
 * Represents a research source
 */
export interface ResearchSource {
  /** Source URL or identifier */
  url: string;
  /** Source title */
  title: string;
  /** Source type */
  type: 'web' | 'academic' | 'news' | 'documentation' | 'other';
  /** Relevance score (1-5) */
  relevance: number;
  /** Content summary */
  summary?: string;
  /** Key findings */
  keyFindings?: string[];
  /** Date accessed */
  accessedAt: Date;
}

/**
 * Research findings and insights
 */
export interface ResearchFindings {
  /** Main topic researched */
  topic: string;
  /** Key insights discovered */
  insights: string[];
  /** Supporting evidence */
  evidence: ResearchSource[];
  /** Research gaps identified */
  gaps?: string[];
  /** Confidence level (1-5) */
  confidence: number;
  /** Recommendations for further research */
  recommendations?: string[];
}

/**
 * State interface for Research Agent
 */
export interface ResearchAgentState extends AgentState {
  /** Research topic */
  topic?: string;
  /** Research query/question */
  query?: string;
  /** Current research status */
  researchStatus?: 'initializing' | 'searching' | 'analyzing' | 'synthesizing' | 'completed' | 'failed';
  /** Sources discovered */
  sources?: ResearchSource[];
  /** Research findings */
  findings?: ResearchFindings;
  /** Research notes/scratchpad */
  researchNotes?: string[];
  /** Current iteration count */
  iterationCount?: number;
  /** Research completeness score (1-5) */
  completenessScore?: number;
}

/**
 * Create a Research Agent - specialized for autonomous web research
 * 
 * This agent can autonomously browse the web, scrape information,
 * and compile comprehensive research briefs on specific topics.
 * 
 * @param options Configuration options
 * @returns Configured Graph ready for execution
 */
export function createResearchAgent<TState extends ResearchAgentState = ResearchAgentState>(
  options: ResearchAgentOptions = {}
): Graph<TState> {
  const {
    tools = createDefaultResearchTools(),
    researchNode,
    maxIterations = 10,
    researchDepth = 3,
    enableParallelResearch = false
  } = options;

  const systemMessage = options.systemMessage || `You are an expert research assistant specializing in comprehensive information gathering and analysis.

Your research process:
1. Analyze the research topic and formulate targeted search queries
2. Search for relevant information from multiple sources
3. Evaluate source credibility and relevance
4. Extract key insights and supporting evidence
5. Identify research gaps and areas needing further investigation
6. Synthesize findings into a comprehensive research brief

Always prioritize accuracy, cite sources, and maintain objectivity in your research.`;

  console.log(`Research agent initialized with depth ${researchDepth}, max ${maxIterations} iterations, parallel: ${enableParallelResearch}`);
  console.log(`System message: ${systemMessage.substring(0, 50)}...`);

  // Create tool execution node
  const toolNode = createToolNode<TState>(tools);

  // Create default research node
  const defaultResearchNode: NodeFunction<TState> = async (state) => {
    const lastMessage = getLastMessage(state);
    
    // Initialize research
    if (lastMessage?.role === 'user' && !state.researchStatus) {
      const topic = lastMessage.content || state.topic || '';
      
      const assistantMessage = {
        role: 'assistant' as const,
        content: null,
        tool_calls: [{
          id: `search_${Date.now()}`,
          type: 'function' as const,
          function: {
            name: 'searchWeb',
            arguments: JSON.stringify({ 
              query: topic,
              maxResults: 5
            })
          }
        }]
      };

      return {
        ...addMessage(state, assistantMessage),
        topic,
        researchStatus: 'searching' as const,
        iterationCount: 1,
        sources: [],
        researchNotes: [`Starting research on: ${topic}`]
      };
    }

    // Process search results
    if (lastMessage?.role === 'tool' && state.researchStatus === 'searching') {
      try {
        const searchResults = JSON.parse(lastMessage.content);
        const sources: ResearchSource[] = searchResults.results?.map((result: any, index: number) => ({
          url: result.url || `https://example.com/${index}`,
          title: result.title || `Result ${index + 1}`,
          type: 'web' as const,
          relevance: Math.min(5, Math.max(1, 5 - index)), // Decreasing relevance
          summary: result.snippet || 'No summary available',
          accessedAt: new Date()
        })) || [];

        // Decide next action based on research depth and iteration count
        const currentIteration = (state.iterationCount || 0) + 1;
        const needsMoreResearch = currentIteration < maxIterations && 
                                 sources.length < researchDepth * 3;

        if (needsMoreResearch) {
          // Continue research with more specific queries
          const nextQuery = generateNextQuery(state.topic || '', sources);
          
          const assistantMessage = {
            role: 'assistant' as const,
            content: null,
            tool_calls: [{
              id: `search_${Date.now()}`,
              type: 'function' as const,
              function: {
                name: 'searchWeb',
                arguments: JSON.stringify({ 
                  query: nextQuery,
                  maxResults: 3
                })
              }
            }]
          };

          return {
            ...addMessage(state, assistantMessage),
            sources: [...(state.sources || []), ...sources],
            iterationCount: currentIteration,
            researchNotes: [
              ...(state.researchNotes || []),
              `Iteration ${currentIteration}: Found ${sources.length} sources, continuing with: ${nextQuery}`
            ]
          };
        } else {
          // Synthesize research findings
          const allSources = [...(state.sources || []), ...sources];
          const findings = synthesizeFindings(state.topic || '', allSources);
          
          const assistantMessage = {
            role: 'assistant' as const,
            content: formatResearchReport(findings)
          };

          return {
            ...addMessage(state, assistantMessage),
            sources: allSources,
            findings,
            researchStatus: 'completed' as const,
            completenessScore: calculateCompletenessScore(findings),
            researchNotes: [
              ...(state.researchNotes || []),
              `Research completed with ${allSources.length} sources analyzed`
            ]
          };
        }
      } catch (error) {
        const assistantMessage = {
          role: 'assistant' as const,
          content: `Research failed: ${error instanceof Error ? error.message : String(error)}`
        };

        return {
          ...addMessage(state, assistantMessage),
          researchStatus: 'failed' as const
        };
      }
    }

    return {};
  };

  // Use provided research node or default
  const finalResearchNode = researchNode || defaultResearchNode;

  // Build and return the graph
  return new Graph<TState>()
    .addNode('researcher', finalResearchNode)
    .addNode('tools', toolNode)
    .setEntryPoint('researcher')
    .addConditionalEdge('researcher', shouldCallTools, {
      'tools': 'tools',
      '__end__': '__END__'
    })
    .addEdge('tools', 'researcher');
}

/**
 * Create default research tools
 */
function createDefaultResearchTools(): ToolMap {
  return {
    searchWeb: async (args: { query: string; maxResults?: number }) => {
      const { query, maxResults = 5 } = args;
      
      // Mock search results
      const mockResults = [
        {
          title: `${query} - Wikipedia`,
          url: `https://en.wikipedia.org/wiki/${encodeURIComponent(query)}`,
          snippet: `Comprehensive information about ${query} from Wikipedia...`
        },
        {
          title: `${query} Research Papers`,
          url: `https://scholar.google.com/search?q=${encodeURIComponent(query)}`,
          snippet: `Academic research and papers related to ${query}...`
        },
        {
          title: `${query} News and Updates`,
          url: `https://news.google.com/search?q=${encodeURIComponent(query)}`,
          snippet: `Latest news and developments about ${query}...`
        }
      ].slice(0, maxResults);

      return JSON.stringify({ 
        query, 
        results: mockResults,
        totalResults: mockResults.length
      });
    },

    scrapeWebpage: async (args: { url: string }) => {
      const { url } = args;
      
      // Mock webpage content
      return JSON.stringify({
        url,
        title: `Content from ${url}`,
        content: `This is mock content scraped from ${url}. In a real implementation, this would contain the actual webpage content.`,
        extractedAt: new Date().toISOString()
      });
    },

    summarizeContent: async (args: { content: string; maxLength?: number }) => {
      const { content, maxLength = 200 } = args;
      
      // Mock summarization
      const summary = content.length > maxLength 
        ? content.substring(0, maxLength) + '...'
        : content;
      
      return JSON.stringify({
        originalLength: content.length,
        summary,
        keyPoints: ['Key point 1', 'Key point 2', 'Key point 3']
      });
    }
  };
}

/**
 * Generate next research query based on current findings
 */
function generateNextQuery(topic: string, sources: ResearchSource[]): string {
  const queries = [
    `${topic} latest developments`,
    `${topic} research studies`,
    `${topic} expert opinions`,
    `${topic} case studies`,
    `${topic} best practices`
  ];
  
  return queries[sources.length % queries.length];
}

/**
 * Synthesize research findings from sources
 */
function synthesizeFindings(topic: string, sources: ResearchSource[]): ResearchFindings {
  const insights = [
    `${topic} is a complex and evolving field`,
    `Multiple perspectives exist on ${topic}`,
    `Recent developments in ${topic} show promising trends`
  ];

  const evidence = sources.slice(0, 5); // Top 5 sources as evidence

  const findings: ResearchFindings = {
    topic,
    insights,
    evidence,
    confidence: Math.min(5, Math.max(1, sources.length / 2)),
    recommendations: ['Conduct follow-up research', 'Verify findings with additional sources']
  };

  if (sources.length < 5) {
    findings.gaps = ['Limited source diversity', 'Need more recent data'];
  }

  return findings;
}

/**
 * Format research report
 */
function formatResearchReport(findings: ResearchFindings): string {
  return `# Research Report: ${findings.topic}

## Key Insights
${findings.insights.map(insight => `• ${insight}`).join('\n')}

## Evidence Sources
${findings.evidence.map(source => `• ${source.title} (${source.type}) - Relevance: ${source.relevance}/5`).join('\n')}

## Confidence Level
${findings.confidence}/5

${findings.gaps ? `\n## Research Gaps\n${findings.gaps.map(gap => `• ${gap}`).join('\n')}` : ''}

${findings.recommendations ? `\n## Recommendations\n${findings.recommendations.map(rec => `• ${rec}`).join('\n')}` : ''}`;
}

/**
 * Calculate research completeness score
 */
function calculateCompletenessScore(findings: ResearchFindings): number {
  let score = 0;
  
  score += Math.min(2, findings.insights.length / 2); // Up to 2 points for insights
  score += Math.min(2, findings.evidence.length / 3); // Up to 2 points for evidence
  score += findings.confidence / 5; // Up to 1 point for confidence
  
  return Math.round(score);
}

/**
 * Create a simple research agent with minimal configuration
 */
export function createSimpleResearcher<TState extends ResearchAgentState = ResearchAgentState>(
  systemMessage?: string
): Graph<TState> {
  const options: ResearchAgentOptions = {};
  if (systemMessage) {
    options.systemMessage = systemMessage;
  }
  return createResearchAgent<TState>(options);
}

/**
 * Create a deep research agent for thorough investigation
 */
export function createDeepResearcher<TState extends ResearchAgentState = ResearchAgentState>(
  maxIterations: number = 15
): Graph<TState> {
  return createResearchAgent<TState>({
    maxIterations,
    researchDepth: 5,
    systemMessage: `You are a thorough research specialist focused on comprehensive, deep-dive investigations. Leave no stone unturned in your research process.`
  });
}
