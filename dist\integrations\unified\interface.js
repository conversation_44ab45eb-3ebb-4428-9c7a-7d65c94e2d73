"use strict";
// src/integrations/unified/interface.ts
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUnifiedLLMClient = createUnifiedLLMClient;
exports.createSimpleLLMClient = createSimpleLLMClient;
/**
 * Factory function to create unified LLM clients
 */
async function createUnifiedLLMClient(config) {
    const { provider, apiKey, ...options } = config;
    switch (provider) {
        case 'openai': {
            const { OpenAIClient } = await Promise.resolve().then(() => __importStar(require('../openai/client')));
            try {
                // Dynamic import to avoid requiring OpenAI SDK as dependency
                const OpenAI = require('openai');
                const openai = new OpenAI({
                    apiKey: apiKey || process.env.OPENAI_API_KEY,
                    baseURL: options.baseURL
                });
                const client = new OpenAIClient(openai, options);
                return new UnifiedOpenAIAdapter(client, options);
            }
            catch (error) {
                throw new Error('OpenAI SDK not found. Please install it with: npm install openai');
            }
        }
        case 'anthropic': {
            const { AnthropicClient } = await Promise.resolve().then(() => __importStar(require('../anthropic/client')));
            try {
                // Dynamic import to avoid requiring Anthropic SDK as dependency
                const Anthropic = require('@anthropic-ai/sdk');
                const anthropic = new Anthropic({
                    apiKey: apiKey || process.env.ANTHROPIC_API_KEY,
                    baseURL: options.baseURL
                });
                const client = new AnthropicClient(anthropic, options);
                return new UnifiedAnthropicAdapter(client, options);
            }
            catch (error) {
                throw new Error('Anthropic SDK not found. Please install it with: npm install @anthropic-ai/sdk');
            }
        }
        default:
            throw new Error(`Unsupported provider: ${provider}`);
    }
}
/**
 * OpenAI adapter for unified interface
 */
class UnifiedOpenAIAdapter {
    constructor(client, // OpenAIClient
    config) {
        this.client = client;
        this.config = config;
    }
    async createCompletion(messages, options = {}) {
        const openaiOptions = this.convertToOpenAIOptions(options);
        return this.client.createChatCompletion(messages, openaiOptions);
    }
    async createStreamingCompletion(messages, options = {}, onChunk, onEvent) {
        const openaiOptions = this.convertToOpenAIOptions(options);
        // Note: OpenAI doesn't have events like Anthropic, so onEvent is unused here
        if (onEvent) {
            console.log('OpenAI streaming events not supported, onEvent callback ignored');
        }
        return this.client.createStreamingCompletion(messages, openaiOptions, onChunk);
    }
    createAgentNode(options = {}) {
        const openaiOptions = this.convertToOpenAIOptions(options);
        return this.client.createAgentNode(openaiOptions);
    }
    async testConnection() {
        return this.client.testConnection();
    }
    getProvider() {
        return 'openai';
    }
    async getAvailableModels() {
        return this.client.getAvailableModels();
    }
    convertToOpenAIOptions(options) {
        const { tools, toolChoice, responseFormat, ...rest } = options;
        const openaiOptions = {
            ...rest,
            model: this.config.model,
            temperature: this.config.temperature,
            maxTokens: this.config.maxTokens,
            timeout: this.config.timeout
        };
        if (tools) {
            openaiOptions.tools = tools.map(tool => ({
                type: 'function',
                function: {
                    name: tool.name,
                    description: tool.description,
                    parameters: tool.parameters
                }
            }));
        }
        if (toolChoice) {
            if (typeof toolChoice === 'string') {
                openaiOptions.toolChoice = toolChoice;
            }
            else {
                openaiOptions.toolChoice = {
                    type: 'function',
                    function: { name: toolChoice.name }
                };
            }
        }
        if (responseFormat === 'json') {
            openaiOptions.responseFormat = { type: 'json_object' };
        }
        return openaiOptions;
    }
}
/**
 * Anthropic adapter for unified interface
 */
class UnifiedAnthropicAdapter {
    constructor(client, // AnthropicClient
    config) {
        this.client = client;
        this.config = config;
    }
    async createCompletion(messages, options = {}) {
        const anthropicOptions = this.convertToAnthropicOptions(options);
        return this.client.createMessage(messages, anthropicOptions);
    }
    async createStreamingCompletion(messages, options = {}, onChunk, onEvent) {
        const anthropicOptions = this.convertToAnthropicOptions(options);
        return this.client.createStreamingMessage(messages, anthropicOptions, onChunk, onEvent);
    }
    createAgentNode(options = {}) {
        const anthropicOptions = this.convertToAnthropicOptions(options);
        return this.client.createAgentNode(anthropicOptions);
    }
    async testConnection() {
        return this.client.testConnection();
    }
    getProvider() {
        return 'anthropic';
    }
    async getAvailableModels() {
        // Anthropic doesn't have a models endpoint, return common models
        return [
            'claude-3-5-sonnet-20241022',
            'claude-3-5-haiku-20241022',
            'claude-3-opus-20240229',
            'claude-3-sonnet-20240229',
            'claude-3-haiku-20240307'
        ];
    }
    convertToAnthropicOptions(options) {
        const { tools, toolChoice, ...rest } = options;
        const anthropicOptions = {
            ...rest,
            model: this.config.model,
            temperature: this.config.temperature,
            maxTokens: this.config.maxTokens,
            timeout: this.config.timeout
        };
        if (tools) {
            anthropicOptions.tools = tools.map(tool => ({
                name: tool.name,
                description: tool.description,
                input_schema: tool.parameters
            }));
        }
        if (toolChoice) {
            if (typeof toolChoice === 'string') {
                anthropicOptions.toolChoice = { type: toolChoice };
            }
            else {
                anthropicOptions.toolChoice = {
                    type: 'tool',
                    name: toolChoice.name
                };
            }
        }
        return anthropicOptions;
    }
}
/**
 * Helper function to create a simple unified client
 */
async function createSimpleLLMClient(provider, apiKey, model) {
    const config = {
        provider,
        model: model || (provider === 'openai' ? 'gpt-4o' : 'claude-3-5-sonnet-20241022'),
        temperature: 0.7,
        maxTokens: 4096
    };
    if (apiKey) {
        config.apiKey = apiKey;
    }
    return createUnifiedLLMClient(config);
}
//# sourceMappingURL=interface.js.map