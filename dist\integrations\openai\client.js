"use strict";
// src/integrations/openai/client.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIClient = void 0;
exports.createOpenAIClient = createOpenAIClient;
const agent_helpers_1 = require("../../lib/agent-helpers");
/**
 * OpenAI client wrapper for AG3NTIC
 * This assumes you have the official OpenAI SDK installed: npm install openai
 *
 * Features:
 * - Proper TypeScript types
 * - Enhanced error handling
 * - Streaming support
 * - Tool calling with runTools pattern
 * - Timeout configuration
 * - Structured outputs
 */
class OpenAIClient {
    constructor(openaiInstance, config = {}) {
        this.openai = openaiInstance;
        this.config = config;
        // Validate that we have a proper OpenAI instance
        if (!openaiInstance?.chat?.completions?.create) {
            throw new Error('Invalid OpenAI instance. Please install the OpenAI SDK: npm install openai');
        }
    }
    /**
     * Create a chat completion using OpenAI with enhanced error handling
     */
    async createChatCompletion(messages, options = {}) {
        const { model = 'gpt-4o', temperature = 0.7, maxTokens = 4096, tools, toolChoice = 'auto', timeout, responseFormat } = options;
        // Convert MCP messages to OpenAI format
        const openaiMessages = this.convertMCPToOpenAI(messages, options.systemMessage);
        const requestParams = {
            model,
            messages: openaiMessages,
            temperature,
            max_tokens: maxTokens
        };
        // Add tools if provided
        if (tools && tools.length > 0) {
            requestParams.tools = tools;
            requestParams.tool_choice = toolChoice;
        }
        // Add response format if specified
        if (responseFormat) {
            requestParams.response_format = responseFormat;
        }
        // Configure timeout
        const requestOptions = {};
        if (timeout || this.config.timeout) {
            requestOptions.timeout = timeout || this.config.timeout;
        }
        try {
            const response = await this.openai.chat.completions.create(requestParams, requestOptions);
            return this.convertOpenAIToMCP(response);
        }
        catch (error) {
            throw this.handleOpenAIError(error);
        }
    }
    /**
     * Convert MCP messages to OpenAI format
     */
    convertMCPToOpenAI(messages, systemMessage) {
        const openaiMessages = [];
        // Add system message if provided
        if (systemMessage) {
            openaiMessages.push({
                role: 'system',
                content: systemMessage
            });
        }
        // Convert MCP messages
        for (const msg of messages) {
            if (msg.role === 'system' && systemMessage) {
                // Skip system messages if we already added one
                continue;
            }
            if (msg.role === 'tool') {
                openaiMessages.push({
                    role: 'tool',
                    content: msg.content,
                    tool_call_id: msg.tool_call_id
                });
            }
            else {
                const openaiMessage = {
                    role: msg.role,
                    content: msg.content
                };
                // Add tool_calls if present (only for assistant messages)
                if (msg.role === 'assistant' && 'tool_calls' in msg && msg.tool_calls) {
                    openaiMessage.tool_calls = msg.tool_calls.map((tc) => ({
                        id: tc.id,
                        type: 'function',
                        function: {
                            name: tc.function.name,
                            arguments: tc.function.arguments
                        }
                    }));
                }
                openaiMessages.push(openaiMessage);
            }
        }
        return openaiMessages;
    }
    /**
     * Convert OpenAI response to MCP format
     */
    convertOpenAIToMCP(response) {
        const choice = response.choices[0];
        if (!choice) {
            throw new Error('No response choice returned from OpenAI API');
        }
        const assistantMessage = {
            role: 'assistant',
            content: choice.message.content
        };
        if (choice.message.tool_calls) {
            assistantMessage.tool_calls = choice.message.tool_calls.map(tc => ({
                id: tc.id,
                type: 'function',
                function: {
                    name: tc.function.name,
                    arguments: tc.function.arguments
                }
            }));
        }
        return assistantMessage;
    }
    /**
     * Handle OpenAI API errors with proper typing
     */
    handleOpenAIError(error) {
        if (error?.status) {
            // This is likely an OpenAI API error
            const openaiError = error;
            return new Error(`OpenAI API error (${openaiError.status}): ${openaiError.message || 'Unknown error'}`);
        }
        if (error instanceof Error) {
            return new Error(`OpenAI error: ${error.message}`);
        }
        return new Error(`OpenAI error: ${String(error)}`);
    }
    /**
     * Create a streaming chat completion
     */
    async createStreamingCompletion(messages, options = {}, onChunk) {
        const { model = 'gpt-4o', temperature = 0.7, maxTokens = 4096, tools, toolChoice = 'auto', timeout } = options;
        const openaiMessages = this.convertMCPToOpenAI(messages, options.systemMessage);
        const requestParams = {
            model,
            messages: openaiMessages,
            temperature,
            max_tokens: maxTokens,
            stream: true
        };
        if (tools && tools.length > 0) {
            requestParams.tools = tools;
            requestParams.tool_choice = toolChoice;
        }
        const requestOptions = {};
        if (timeout || this.config.timeout) {
            requestOptions.timeout = timeout || this.config.timeout;
        }
        try {
            const stream = await this.openai.chat.completions.create(requestParams, requestOptions);
            let content = '';
            let toolCalls = [];
            for await (const chunk of stream) {
                const delta = chunk.choices[0]?.delta;
                if (delta?.content) {
                    content += delta.content;
                    onChunk?.(delta.content);
                }
                if (delta?.tool_calls) {
                    // Handle tool calls in streaming
                    for (const toolCall of delta.tool_calls) {
                        if (toolCall.index !== undefined) {
                            if (!toolCalls[toolCall.index]) {
                                toolCalls[toolCall.index] = {
                                    id: toolCall.id || '',
                                    type: 'function',
                                    function: { name: '', arguments: '' }
                                };
                            }
                            if (toolCall.function?.name) {
                                toolCalls[toolCall.index].function.name += toolCall.function.name;
                            }
                            if (toolCall.function?.arguments) {
                                toolCalls[toolCall.index].function.arguments += toolCall.function.arguments;
                            }
                        }
                    }
                }
            }
            const assistantMessage = {
                role: 'assistant',
                content: content || null
            };
            if (toolCalls.length > 0) {
                assistantMessage.tool_calls = toolCalls.map(tc => ({
                    id: tc.id,
                    type: 'function',
                    function: {
                        name: tc.function.name,
                        arguments: tc.function.arguments
                    }
                }));
            }
            return assistantMessage;
        }
        catch (error) {
            throw this.handleOpenAIError(error);
        }
    }
    /**
     * Create an agent node that uses OpenAI for LLM calls
     */
    createAgentNode(options = {}) {
        return async (state) => {
            try {
                const assistantMessage = await this.createChatCompletion(state.messages, options);
                return (0, agent_helpers_1.addMessage)(state, assistantMessage);
            }
            catch (error) {
                throw new Error(`OpenAI agent error: ${error instanceof Error ? error.message : String(error)}`);
            }
        };
    }
    /**
     * Test the connection to OpenAI
     */
    async testConnection() {
        try {
            const testMessage = {
                role: 'user',
                content: 'Hello, this is a connection test.'
            };
            await this.createChatCompletion([testMessage], {
                model: 'gpt-3.5-turbo',
                maxTokens: 10
            });
            return true;
        }
        catch (error) {
            console.error('OpenAI connection test failed:', error);
            return false;
        }
    }
    /**
     * Get available models (requires OpenAI SDK)
     */
    async getAvailableModels() {
        try {
            const models = await this.openai.models.list();
            return models.data
                .filter((model) => model.id.includes('gpt'))
                .map((model) => model.id);
        }
        catch (error) {
            console.error('Failed to get available models:', error);
            return ['gpt-4', 'gpt-3.5-turbo']; // Fallback to common models
        }
    }
}
exports.OpenAIClient = OpenAIClient;
/**
 * Factory function to create OpenAI client from API key
 * This requires the OpenAI SDK to be installed
 */
function createOpenAIClient(apiKey) {
    try {
        // Dynamic import to avoid requiring OpenAI SDK as dependency
        const OpenAI = require('openai');
        const openai = new OpenAI({ apiKey });
        return new OpenAIClient(openai);
    }
    catch (error) {
        throw new Error('OpenAI SDK not found. Please install it with: npm install openai');
    }
}
//# sourceMappingURL=client.js.map