"use strict";
// src/integrations/openai/client.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIClient = void 0;
exports.createOpenAIClient = createOpenAIClient;
const agent_helpers_1 = require("../../lib/agent-helpers");
/**
 * OpenAI client wrapper for AG3NTIC
 * This assumes you have the official OpenAI SDK installed: npm install openai
 */
class OpenAIClient {
    constructor(openaiInstance) {
        this.openai = openaiInstance;
    }
    /**
     * Create a chat completion using OpenAI
     */
    async createChatCompletion(messages, options = {}) {
        const { model = 'gpt-4', temperature = 0.7, maxTokens = 4096, tools } = options;
        // Convert MCP messages to OpenAI format
        const openaiMessages = messages.map(msg => {
            if (msg.role === 'tool') {
                return {
                    role: 'tool',
                    content: msg.content,
                    tool_call_id: msg.tool_call_id
                };
            }
            return {
                role: msg.role,
                content: msg.content
            };
        });
        const requestParams = {
            model,
            messages: openaiMessages,
            temperature,
            max_tokens: maxTokens
        };
        // Add tools if provided
        if (tools && tools.length > 0) {
            requestParams.tools = tools;
            requestParams.tool_choice = 'auto';
        }
        try {
            const response = await this.openai.chat.completions.create(requestParams);
            const choice = response.choices[0];
            if (!choice) {
                throw new Error('No response choice returned from OpenAI API');
            }
            // Convert OpenAI response to MCP format
            const assistantMessage = {
                role: 'assistant',
                content: choice.message.content,
                tool_calls: choice.message.tool_calls?.map((tc) => ({
                    id: tc.id,
                    type: 'function',
                    function: {
                        name: tc.function.name,
                        arguments: tc.function.arguments
                    }
                }))
            };
            return assistantMessage;
        }
        catch (error) {
            throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    /**
     * Create an agent node that uses OpenAI for LLM calls
     */
    createAgentNode(options = {}) {
        return async (state) => {
            const { systemMessage } = options;
            // Prepare messages for the LLM
            let messages = [...state.messages];
            // Add system message if provided and not already present
            if (systemMessage && (messages.length === 0 || messages[0].role !== 'system')) {
                messages.unshift({
                    role: 'system',
                    content: systemMessage
                });
            }
            try {
                const assistantMessage = await this.createChatCompletion(messages, options);
                return (0, agent_helpers_1.addMessage)(state, assistantMessage);
            }
            catch (error) {
                throw new Error(`OpenAI agent error: ${error instanceof Error ? error.message : String(error)}`);
            }
        };
    }
    /**
     * Test the connection to OpenAI
     */
    async testConnection() {
        try {
            const testMessage = {
                role: 'user',
                content: 'Hello, this is a connection test.'
            };
            await this.createChatCompletion([testMessage], {
                model: 'gpt-3.5-turbo',
                maxTokens: 10
            });
            return true;
        }
        catch (error) {
            console.error('OpenAI connection test failed:', error);
            return false;
        }
    }
    /**
     * Get available models (requires OpenAI SDK)
     */
    async getAvailableModels() {
        try {
            const models = await this.openai.models.list();
            return models.data
                .filter((model) => model.id.includes('gpt'))
                .map((model) => model.id);
        }
        catch (error) {
            console.error('Failed to get available models:', error);
            return ['gpt-4', 'gpt-3.5-turbo']; // Fallback to common models
        }
    }
}
exports.OpenAIClient = OpenAIClient;
/**
 * Factory function to create OpenAI client from API key
 * This requires the OpenAI SDK to be installed
 */
function createOpenAIClient(apiKey) {
    try {
        // Dynamic import to avoid requiring OpenAI SDK as dependency
        const OpenAI = require('openai');
        const openai = new OpenAI({ apiKey });
        return new OpenAIClient(openai);
    }
    catch (error) {
        throw new Error('OpenAI SDK not found. Please install it with: npm install openai');
    }
}
//# sourceMappingURL=client.js.map