{"version": 3, "file": "advanced-agent.js", "sourceRoot": "", "sources": ["../../src/examples/advanced-agent.ts"], "names": [], "mappings": ";AAAA,iCAAiC;;;AAEjC,kCAMiB;AACjB,gCAKgB;AAQhB,0BAA0B;AAC1B;;;GAGG;AACH,MAAM,iBAAiB,GAAG,KAAK,EAAE,IAA2D,EAAmB,EAAE;IAC/G,MAAM,EAAE,QAAQ,EAAE,IAAI,GAAG,YAAY,EAAE,GAAG,IAAI,CAAC;IAE/C,0BAA0B;IAC1B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAEvD,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;QACrD,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,QAAQ,EAAE,mBAAmB;YAC7B,WAAW,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;YACjD,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;IACL,CAAC;IAED,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,QAAQ,EAAE,cAAc;YACxB,WAAW,EAAE,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;YACjD,SAAS,EAAE,OAAO;YAClB,QAAQ,EAAE,KAAK;SAChB,CAAC,CAAC;IACL,CAAC;IAED,OAAO,IAAI,CAAC,SAAS,CAAC;QACpB,QAAQ;QACR,WAAW,EAAE,SAAS;QACtB,SAAS,EAAE,SAAS;QACpB,KAAK,EAAE,wCAAwC;KAChD,CAAC,CAAC;AACL,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,SAAS,GAAG,KAAK,EAAE,IAA6C,EAAmB,EAAE;IACzF,MAAM,EAAE,KAAK,EAAE,WAAW,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;IAExC,0BAA0B;IAC1B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAEvD,sBAAsB;IACtB,MAAM,WAAW,GAAG;QAClB,EAAE,KAAK,EAAE,GAAG,KAAK,cAAc,EAAE,GAAG,EAAE,uBAAuB,EAAE,OAAO,EAAE,qBAAqB,KAAK,KAAK,EAAE;QACzG,EAAE,KAAK,EAAE,GAAG,KAAK,OAAO,EAAE,GAAG,EAAE,kBAAkB,EAAE,OAAO,EAAE,qBAAqB,KAAK,KAAK,EAAE;QAC7F,EAAE,KAAK,EAAE,GAAG,KAAK,QAAQ,EAAE,GAAG,EAAE,mBAAmB,EAAE,OAAO,EAAE,qBAAqB,KAAK,KAAK,EAAE;KAChG,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;IAExB,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,SAAS,GAAG,KAAK,EAAE,IAA4B,EAAmB,EAAE;IACxE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;IAE5B,IAAI,CAAC;QACH,8DAA8D;QAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;IAChD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,UAAU;YACV,KAAK,EAAE,iCAAiC;YACxC,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAChE,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAEF,6BAA6B;AAC7B,MAAM,KAAK,GAAG;IACZ,iBAAiB;IACjB,SAAS;IACT,SAAS;CACV,CAAC;AAEF,iDAAiD;AACjD,MAAM,mBAAmB,GAAG,GAAqC,EAAE;IACjE,OAAO,KAAK,EAAE,KAAyB,EAAwC,EAAE;QAC/E,MAAM,WAAW,GAAG,IAAA,oBAAc,EAAC,KAAK,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,0BAA0B,WAAW,EAAE,IAAI,KAAK,WAAW,EAAE,OAAO,EAAE,CAAC,CAAC;QAEpF,kEAAkE;QAClE,IAAI,WAAW,EAAE,IAAI,KAAK,MAAM,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAElD,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAChC,oDAAoD;gBACpD,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;gBAC9D,MAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC;gBAE3E,MAAM,gBAAgB,GAAe;oBACnC,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,CAAC;4BACX,EAAE,EAAE,WAAW,IAAI,CAAC,GAAG,EAAE,EAAE;4BAC3B,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE;gCACR,IAAI,EAAE,mBAAmB;gCACzB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,QAAQ,EAAE,CAAC;6BACxC;yBACF,CAAC;iBACH,CAAC;gBACF,OAAO,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAC7C,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC3D,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;gBAEzD,MAAM,gBAAgB,GAAe;oBACnC,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,CAAC;4BACX,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;4BAC1B,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE;gCACR,IAAI,EAAE,WAAW;gCACjB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,CAAC;6BACrC;yBACF,CAAC;iBACH,CAAC;gBACF,OAAO,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAC7C,CAAC;YAED,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;gBAChE,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;gBAE3E,MAAM,gBAAgB,GAAe;oBACnC,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,CAAC;4BACX,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;4BACxB,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE;gCACR,IAAI,EAAE,WAAW;gCACjB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE,CAAC;6BAC1C;yBACF,CAAC;iBACH,CAAC;gBACF,OAAO,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;YAC7C,CAAC;YAED,mBAAmB;YACnB,MAAM,gBAAgB,GAAe;gBACnC,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,sGAAsG;aAChH,CAAC;YACF,OAAO,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAC7C,CAAC;QAED,wBAAwB;QACxB,IAAI,WAAW,EAAE,IAAI,KAAK,MAAM,EAAE,CAAC;YACjC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACnD,IAAI,QAAQ,GAAG,EAAE,CAAC;YAElB,IAAI,UAAU,CAAC,WAAW,EAAE,CAAC;gBAC3B,QAAQ,GAAG,kBAAkB,UAAU,CAAC,QAAQ,OAAO,UAAU,CAAC,WAAW,SAAS,UAAU,CAAC,SAAS,cAAc,CAAC;YAC3H,CAAC;iBAAM,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;gBAC9B,QAAQ,GAAG,WAAW,UAAU,CAAC,OAAO,CAAC,MAAM,iBAAiB,UAAU,CAAC,KAAK,gCAAgC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;YAC5K,CAAC;iBAAM,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;gBAC3C,QAAQ,GAAG,iBAAiB,UAAU,CAAC,UAAU,OAAO,UAAU,CAAC,MAAM,GAAG,CAAC;YAC/E,CAAC;iBAAM,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBAC5B,QAAQ,GAAG,2BAA2B,UAAU,CAAC,KAAK,EAAE,CAAC;YAC3D,CAAC;iBAAM,CAAC;gBACN,QAAQ,GAAG,gCAAgC,WAAW,CAAC,OAAO,EAAE,CAAC;YACnE,CAAC;YAED,MAAM,gBAAgB,GAAe;gBACnC,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,QAAQ;aAClB,CAAC;YACF,OAAO,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,6BAA6B;AAC7B,MAAM,kBAAkB,GAAG,GAA8B,EAAE;IACzD,MAAM,SAAS,GAAG,mBAAmB,EAAE,CAAC;IACxC,MAAM,QAAQ,GAAG,IAAA,oBAAc,EAAqB,KAAK,CAAC,CAAC;IAE3D,OAAO,IAAI,YAAK,EAAsB;SACnC,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC;SAC3B,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;SAC1B,aAAa,CAAC,OAAO,CAAC;SACtB,kBAAkB,CAAC,OAAO,EAAE,qBAAe,EAAE;QAC5C,OAAO,EAAE,OAAO;QAChB,SAAS,EAAE,SAAS;KACrB,CAAC;SACD,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC/B,CAAC,CAAC;AA2CwB,gDAAkB;AAzC5C,2BAA2B;AAC3B,MAAM,eAAe,GAAG,KAAK,IAAI,EAAE;IACjC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IAErD,MAAM,KAAK,GAAG,kBAAkB,EAAE,CAAC;IACnC,MAAM,QAAQ,GAAG,IAAI,eAAQ,CAAC,KAAK,CAAC,CAAC;IAErC,MAAM,OAAO,GAAG;QACd,8BAA8B;QAC9B,yCAAyC;QACzC,uBAAuB;KACxB,CAAC;IAEF,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,OAAO,CAAC,CAAC;QAE3C,MAAM,YAAY,GAAuB;YACvC,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,EAAE;YACX,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;SAC7C,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,mBAAmB,CACnD,YAAY,EACZ,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;gBACd,MAAM,OAAO,GAAG,IAAA,oBAAc,EAAC,KAAK,CAAC,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,KAAK,OAAO,EAAE,IAAI,MAAM,OAAO,EAAE,OAAO,IAAI,cAAc,EAAE,CAAC,CAAC;YACxF,CAAC,CACF,CAAC;YAEF,MAAM,WAAW,GAAG,IAAA,oBAAc,EAAC,UAAU,CAAC,EAAE,OAAO,CAAC;YACxD,OAAO,CAAC,GAAG,CAAC,qBAAqB,WAAW,IAAI,CAAC,CAAC;QAEpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,YAAY,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAGO,0CAAe;AAExB,wCAAwC;AACxC,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,eAAe,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC"}