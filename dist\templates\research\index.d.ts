import { Graph, AgentState, NodeFunction } from '../../core';
import { ToolMap } from '../../lib';
/**
 * Configuration options for the Research Agent
 */
export interface ResearchAgentOptions {
    /** Research tools available */
    tools?: ToolMap;
    /** System message for research */
    systemMessage?: string;
    /** Custom research node function */
    researchNode?: NodeFunction<any>;
    /** Maximum number of research iterations */
    maxIterations?: number;
    /** Research depth (1-5, where 5 is most thorough) */
    researchDepth?: number;
    /** Whether to enable parallel research */
    enableParallelResearch?: boolean;
}
/**
 * Represents a research source
 */
export interface ResearchSource {
    /** Source URL or identifier */
    url: string;
    /** Source title */
    title: string;
    /** Source type */
    type: 'web' | 'academic' | 'news' | 'documentation' | 'other';
    /** Relevance score (1-5) */
    relevance: number;
    /** Content summary */
    summary?: string;
    /** Key findings */
    keyFindings?: string[];
    /** Date accessed */
    accessedAt: Date;
}
/**
 * Research findings and insights
 */
export interface ResearchFindings {
    /** Main topic researched */
    topic: string;
    /** Key insights discovered */
    insights: string[];
    /** Supporting evidence */
    evidence: ResearchSource[];
    /** Research gaps identified */
    gaps?: string[];
    /** Confidence level (1-5) */
    confidence: number;
    /** Recommendations for further research */
    recommendations?: string[];
}
/**
 * State interface for Research Agent
 */
export interface ResearchAgentState extends AgentState {
    /** Research topic */
    topic?: string;
    /** Research query/question */
    query?: string;
    /** Current research status */
    researchStatus?: 'initializing' | 'searching' | 'analyzing' | 'synthesizing' | 'completed' | 'failed';
    /** Sources discovered */
    sources?: ResearchSource[];
    /** Research findings */
    findings?: ResearchFindings;
    /** Research notes/scratchpad */
    researchNotes?: string[];
    /** Current iteration count */
    iterationCount?: number;
    /** Research completeness score (1-5) */
    completenessScore?: number;
}
/**
 * Create a Research Agent - specialized for autonomous web research
 *
 * This agent can autonomously browse the web, scrape information,
 * and compile comprehensive research briefs on specific topics.
 *
 * @param options Configuration options
 * @returns Configured Graph ready for execution
 */
export declare function createResearchAgent<TState extends ResearchAgentState = ResearchAgentState>(options?: ResearchAgentOptions): Graph<TState>;
/**
 * Create a simple research agent with minimal configuration
 */
export declare function createSimpleResearcher<TState extends ResearchAgentState = ResearchAgentState>(systemMessage?: string): Graph<TState>;
/**
 * Create a deep research agent for thorough investigation
 */
export declare function createDeepResearcher<TState extends ResearchAgentState = ResearchAgentState>(maxIterations?: number): Graph<TState>;
//# sourceMappingURL=index.d.ts.map