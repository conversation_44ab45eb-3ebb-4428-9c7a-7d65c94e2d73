/** @type {import('jest').Config} */
const config = {
  // Test environment
  preset: 'ts-jest/presets/default-esm',
  extensionsToTreatAsEsm: ['.ts'],
  testEnvironment: 'node',

  // Module resolution
  moduleNameMapping: {
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      useESM: true,
      tsconfig: {
        module: 'esnext',
        target: 'es2022',
        moduleResolution: 'node',
        allowSyntheticDefaultImports: true,
        esModuleInterop: true,
      }
    }],
  },

  // Test file patterns
  testMatch: [
    '<rootDir>/**/*.test.ts',
    '<rootDir>/**/*.spec.ts'
  ],
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/'
  ],

  // Setup and teardown
  setupFilesAfterEnv: ['<rootDir>/setup.ts'],
  globalSetup: '<rootDir>/global-setup.ts',
  globalTeardown: '<rootDir>/global-teardown.ts',

  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: '<rootDir>/coverage',
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json'
  ],
  collectCoverageFrom: [
    '<rootDir>/../src/**/*.ts',
    '!<rootDir>/../src/**/*.d.ts',
    '!<rootDir>/../src/**/*.test.ts',
    '!<rootDir>/../src/**/*.spec.ts',
    '!<rootDir>/../src/**/index.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 85,
      lines: 85,
      statements: 85
    }
  },

  // Performance and timeout settings
  testTimeout: 60000, // 60 seconds per test (increased for OpenRouter API calls)
  slowTestThreshold: 10000, // 10 seconds (increased for LLM responses)
  maxWorkers: '25%', // Use 25% of available CPU cores (reduced for API rate limiting)

  // Output and reporting
  verbose: true,
  silent: false,
  errorOnDeprecated: true,
  
  // Custom reporters for detailed output
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: '<rootDir>/coverage/html-report',
      filename: 'test-report.html',
      expand: true,
      hideIcon: false,
      pageTitle: 'AG3NTIC Framework Test Report'
    }],
    ['jest-junit', {
      outputDirectory: '<rootDir>/coverage',
      outputName: 'junit.xml',
      suiteName: 'AG3NTIC Framework Tests'
    }]
  ],

  // Memory and resource management
  logHeapUsage: true,
  detectOpenHandles: true,
  detectLeaks: true,
  forceExit: false,

  // Test organization
  displayName: {
    name: 'AG3NTIC Framework',
    color: 'blue'
  },

  // Custom test sequences
  projects: [
    {
      displayName: 'OpenRouter Demo - Kimi-K2 Integration',
      testMatch: ['<rootDir>/openrouter-demo.test.ts'],
      setupFilesAfterEnv: ['<rootDir>/setup.ts'],
      testTimeout: 90000, // Extended timeout for API calls
    },
    {
      displayName: 'Unit Tests - Core Components',
      testMatch: ['<rootDir>/agents/*.test.ts'],
      setupFilesAfterEnv: ['<rootDir>/setup.ts']
    },
    {
      displayName: 'Integration Tests - Multi-Agent Systems',
      testMatch: ['<rootDir>/multi-agent/*.test.ts'],
      setupFilesAfterEnv: ['<rootDir>/setup.ts']
    },
    {
      displayName: 'End-to-End Tests - Framework Integration',
      testMatch: ['<rootDir>/integration/*.test.ts'],
      setupFilesAfterEnv: ['<rootDir>/setup.ts']
    }
  ],

  // Global variables for tests
  globals: {
    'ts-jest': {
      useESM: true
    }
  }
};

module.exports = config;
