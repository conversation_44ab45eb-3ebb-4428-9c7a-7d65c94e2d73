import { Graph } from '../core/graph';
import { AgentState, NodeFunction } from '../core/types';
/**
 * Tool function type for executor agents
 */
export type Tool = (args: any) => any | Promise<any>;
/**
 * Create an executor agent that can use tools to complete tasks
 *
 * This is a fundamental pattern in AG3NTIC where an LLM agent can:
 * 1. Receive a user request
 * 2. Decide which tools to call
 * 3. Execute the tools
 * 4. Continue the conversation with the results
 *
 * @param llmNode - The LLM node function that generates responses and tool calls
 * @param tools - Map of tool names to tool functions
 * @returns A graph that implements the executor pattern
 */
export declare const createExecutorAgent: <TState extends AgentState>(llmNode: NodeFunction<TState>, tools: Record<string, Tool>) => Graph<TState>;
/**
 * Plan step interface for planner agents
 */
export interface PlanStep {
    id: string;
    description: string;
    dependencies?: string[];
    estimatedTime?: number;
    priority?: 'low' | 'medium' | 'high';
    status?: 'pending' | 'in_progress' | 'completed' | 'failed';
}
/**
 * Extended state for planner agents
 */
export interface PlannerAgentState extends AgentState {
    plan?: PlanStep[];
    currentStep?: number;
    planningStrategy?: 'sequential' | 'parallel' | 'adaptive';
}
/**
 * Create a planner agent that breaks down complex goals into actionable steps
 *
 * This agent specializes in:
 * 1. Analyzing complex goals or requests
 * 2. Breaking them down into logical, sequential steps
 * 3. Identifying dependencies between steps
 * 4. Creating a structured execution plan
 *
 * @param llmNode - The LLM node function that generates plans
 * @param options - Configuration options for the planner
 * @returns A graph that implements the planner pattern
 */
export declare const createPlannerAgent: <TState extends PlannerAgentState>(llmNode: NodeFunction<TState>, options?: {
    maxSteps?: number;
    strategy?: "sequential" | "parallel" | "adaptive";
    includeTimeEstimates?: boolean;
}) => Graph<TState>;
/**
 * Worker agent interface for orchestrator agents
 */
export interface WorkerAgent {
    id: string;
    name: string;
    description: string;
    capabilities: string[];
    specializations?: string[];
    graph: Graph<any>;
    isAvailable?: boolean;
}
/**
 * Step execution tracking for orchestrator agents
 */
export interface StepExecution {
    stepId: string;
    assignedWorker?: string;
    status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'failed';
    startTime?: Date;
    endTime?: Date;
    result?: any;
    error?: string;
}
/**
 * Extended state for orchestrator agents
 */
export interface OrchestratorAgentState extends PlannerAgentState {
    workers?: WorkerAgent[];
    stepExecutions?: StepExecution[];
    orchestrationStatus?: 'planning' | 'executing' | 'completed' | 'failed';
    currentStepIndex?: number;
}
/**
 * Create an orchestrator agent that coordinates multiple worker agents
 *
 * This agent specializes in:
 * 1. Taking a plan and coordinating its execution
 * 2. Assigning tasks to the most suitable worker agents
 * 3. Managing dependencies between tasks
 * 4. Monitoring progress and handling failures
 * 5. Synthesizing results from multiple workers
 *
 * @param llmNode - The LLM node function that makes orchestration decisions
 * @param workers - Array of available worker agents
 * @param options - Configuration options for the orchestrator
 * @returns A graph that implements the orchestrator pattern
 */
export declare const createOrchestratorAgent: <TState extends OrchestratorAgentState>(_llmNode: NodeFunction<TState>, workers: WorkerAgent[], options?: {
    maxConcurrentTasks?: number;
    retryFailedTasks?: boolean;
    timeoutPerTask?: number;
}) => Graph<TState>;
//# sourceMappingURL=templates.d.ts.map