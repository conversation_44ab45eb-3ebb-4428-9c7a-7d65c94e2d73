import { Graph } from '../core/graph';
import { AgentState, NodeFunction } from '../core/types';
import { ToolMap } from './tool-helpers';
type MCPClient = any;
/**
 * Ultra-fast agent templates optimized for maximum performance
 *
 * Optimizations applied:
 * - Pre-compiled graph structures
 * - Minimal object allocation
 * - Efficient tool execution
 * - Cached conditional functions
 * - Zero-cost abstractions
 */
/**
 * Optimized tool function type
 */
export type Tool = (args: any) => any | Promise<any>;
/**
 * Create ultra-fast executor agent (optimized pattern)
 *
 * Key optimizations:
 * - Uses optimized tool execution from tool-helpers
 * - Pre-compiled conditional function
 * - Minimal graph construction overhead
 */
export declare const createExecutorAgent: <TState extends AgentState>(llmNode: NodeFunction<TState>, tools: ToolMap) => Graph<TState>;
/**
 * Plan step interface for planner agents
 */
export interface PlanStep {
    id: string;
    description: string;
    dependencies?: string[];
    estimatedTime?: number;
    priority?: 'low' | 'medium' | 'high';
    status?: 'pending' | 'in_progress' | 'completed' | 'failed';
}
/**
 * Extended state for planner agents
 */
export interface PlannerAgentState extends AgentState {
    plan?: PlanStep[];
    currentStep?: number;
    planningStrategy?: 'sequential' | 'parallel' | 'adaptive';
}
/**
 * Create a planner agent that breaks down complex goals into actionable steps
 *
 * This agent specializes in:
 * 1. Analyzing complex goals or requests
 * 2. Breaking them down into logical, sequential steps
 * 3. Identifying dependencies between steps
 * 4. Creating a structured execution plan
 *
 * @param llmNode - The LLM node function that generates plans
 * @param options - Configuration options for the planner
 * @returns A graph that implements the planner pattern
 */
export declare const createPlannerAgent: <TState extends PlannerAgentState>(llmNode: NodeFunction<TState>, options?: {
    maxSteps?: number;
    strategy?: "sequential" | "parallel" | "adaptive";
    includeTimeEstimates?: boolean;
}) => Graph<TState>;
/**
 * Create an MCP-enabled executor agent that can use both local tools and remote MCP server tools
 *
 * @param llmNode - The LLM node function
 * @param localTools - Local tool functions
 * @param mcpClients - Array of connected MCP clients
 * @returns A graph that can execute both local and remote tools
 */
export declare const createMCPExecutorAgent: <TState extends AgentState>(llmNode: NodeFunction<TState>, localTools?: Record<string, Tool>, mcpClients?: MCPClient[]) => Graph<TState>;
/**
 * Create an MCP-enabled agent that automatically connects to specified MCP servers
 *
 * @param llmNode - The LLM node function
 * @param localTools - Local tool functions
 * @param mcpServers - Array of MCP server configurations
 * @returns Promise resolving to an agent with MCP capabilities
 */
export declare const createAutoMCPAgent: <TState extends AgentState>(llmNode: NodeFunction<TState>, localTools?: Record<string, Tool>, mcpServers?: Array<{
    name: string;
    url: string;
    transport?: "http" | "sse";
}>) => Promise<{
    graph: Graph<TState>;
    mcpClients: MCPClient[];
    cleanup: () => Promise<void>;
}>;
/**
 * Worker agent interface for orchestrator agents
 */
export interface WorkerAgent {
    id: string;
    name: string;
    description: string;
    capabilities: string[];
    specializations?: string[];
    graph: Graph<any>;
    isAvailable?: boolean;
}
/**
 * Step execution tracking for orchestrator agents
 */
export interface StepExecution {
    stepId: string;
    assignedWorker?: string;
    status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'failed';
    startTime?: Date;
    endTime?: Date;
    result?: any;
    error?: string;
}
/**
 * Extended state for orchestrator agents
 */
export interface OrchestratorAgentState extends PlannerAgentState {
    workers?: WorkerAgent[];
    stepExecutions?: StepExecution[];
    orchestrationStatus?: 'planning' | 'executing' | 'completed' | 'failed';
    currentStepIndex?: number;
}
/**
 * Create an orchestrator agent that coordinates multiple worker agents
 *
 * This agent specializes in:
 * 1. Taking a plan and coordinating its execution
 * 2. Assigning tasks to the most suitable worker agents
 * 3. Managing dependencies between tasks
 * 4. Monitoring progress and handling failures
 * 5. Synthesizing results from multiple workers
 *
 * @param llmNode - The LLM node function that makes orchestration decisions
 * @param workers - Array of available worker agents
 * @param options - Configuration options for the orchestrator
 * @returns A graph that implements the orchestrator pattern
 */
export declare const createOrchestratorAgent: <TState extends OrchestratorAgentState>(_llmNode: NodeFunction<TState>, workers: WorkerAgent[], options?: {
    maxConcurrentTasks?: number;
    retryFailedTasks?: boolean;
    timeoutPerTask?: number;
}) => Graph<TState>;
export {};
//# sourceMappingURL=templates.d.ts.map