// src/lib/agent-helpers.ts

import {
  AgentState,
  MCPMessage,
  MCPAssistantMessage,
  NodeFunction
} from "../core/types";

/**
 * Ultra-fast agent helper functions optimized for performance
 *
 * Optimizations:
 * - Direct array access (O(1) operations)
 * - Minimal object allocation
 * - Efficient loops over built-in methods
 * - Cached computations where possible
 */

/**
 * Get last message (O(1) operation)
 */
export const getLastMessage = <TState extends AgentState>(state: TState): MCPMessage | undefined =>
  state.messages[state.messages.length - 1];

/**
 * Add message efficiently (minimal allocation)
 */
export const addMessage = <TState extends AgentState>(
  state: TState,
  message: MCPMessage
): Partial<TState> => ({
  messages: [...state.messages, message]
} as Partial<TState>);

/**
 * Get messages by role (optimized loop)
 */
export const getMessagesByRole = <TState extends AgentState>(
  state: TState,
  role: MCPMessage['role']
): MCPMessage[] => {
  const result: MCPMessage[] = [];
  const messages = state.messages;

  for (let i = 0; i < messages.length; i++) {
    if (messages[i].role === role) {
      result.push(messages[i]);
    }
  }

  return result;
};

/**
 * Count messages by role (optimized counter)
 */
export const countMessagesByRole = <TState extends AgentState>(
  state: TState,
  role: MCPMessage['role']
): number => {
  let count = 0;
  const messages = state.messages;

  for (let i = 0; i < messages.length; i++) {
    if (messages[i].role === role) {
      count++;
    }
  }

  return count;
};

/**
 * Check assistant response (fast boolean check)
 */
export const hasAssistantResponse = <TState extends AgentState>(state: TState): boolean => {
  const lastMessage = getLastMessage(state);
  return lastMessage?.role === 'assistant' && !!lastMessage.content;
};

/**
 * Get conversation history (optimized string building)
 */
export const getConversationHistory = <TState extends AgentState>(
  state: TState,
  includeSystem = false
): string => {
  const messages = state.messages;
  if (messages.length === 0) return '';

  let result = '';
  let first = true;

  for (let i = 0; i < messages.length; i++) {
    const msg = messages[i];
    if (!includeSystem && msg.role === 'system') continue;

    if (!first) result += '\n';
    result += `${msg.role}: ${msg.content || '[tool_calls]'}`;
    first = false;
  }

  return result;
};

/**
 * Create static agent node (pre-compiled message)
 */
export const createStaticAgentNode = <TState extends AgentState>(
  response: string
): NodeFunction<TState> => {
  // Pre-compile the message for performance
  const precompiledMessage: MCPAssistantMessage = {
    role: 'assistant',
    content: response
  };

  return async (state: TState): Promise<Partial<TState>> =>
    addMessage(state, precompiledMessage);
};

/**
 * Create conditional agent node (optimized)
 */
export const createConditionalAgentNode = <TState extends AgentState>(
  responseFunction: (state: TState) => string | MCPAssistantMessage
): NodeFunction<TState> =>
  async (state: TState): Promise<Partial<TState>> => {
    const response = responseFunction(state);

    const message: MCPAssistantMessage = typeof response === 'string'
      ? { role: 'assistant', content: response }
      : response;

    return addMessage(state, message);
  };

/**
 * Additional optimized helpers
 */

/**
 * Get message count (O(1))
 */
export const getMessageCount = <TState extends AgentState>(state: TState): number =>
  state.messages.length;

/**
 * Check if state has messages (O(1))
 */
export const hasMessages = <TState extends AgentState>(state: TState): boolean =>
  state.messages.length > 0;

/**
 * Get first message (O(1))
 */
export const getFirstMessage = <TState extends AgentState>(state: TState): MCPMessage | undefined =>
  state.messages[0];

/**
 * Clear messages (minimal allocation)
 */
export const clearMessages = <TState extends AgentState>(): Partial<TState> => ({
  messages: []
} as unknown as Partial<TState>);
