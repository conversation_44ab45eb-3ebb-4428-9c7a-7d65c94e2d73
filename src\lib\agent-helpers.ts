// src/lib/agent-helpers.ts

import { 
  AgentState, 
  MCPMessage, 
  MCPAssistantMessage,
  NodeFunction
} from "../core/types";

/**
 * Get the last message from the state
 * @param state The current state
 * @returns The last message or undefined if no messages exist
 */
export function getLastMessage<TState extends AgentState>(state: TState): MCPMessage | undefined {
  return state.messages[state.messages.length - 1];
}

/**
 * Add a message to the state
 * @param state The current state
 * @param message The message to add
 * @returns A partial state update with the new message added
 */
export function addMessage<TState extends AgentState>(
  state: TState, 
  message: MCPMessage
): Partial<TState> {
  return {
    messages: [...state.messages, message]
  } as Partial<TState>;
}

/**
 * Get all messages of a specific role
 * @param state The current state
 * @param role The role to filter by
 * @returns Array of messages with the specified role
 */
export function getMessagesByRole<TState extends AgentState>(
  state: TState, 
  role: MCPMessage['role']
): MCPMessage[] {
  return state.messages.filter(message => message.role === role);
}

/**
 * Count messages by role
 * @param state The current state
 * @param role The role to count
 * @returns Number of messages with the specified role
 */
export function countMessagesByRole<TState extends AgentState>(
  state: TState, 
  role: MCPMessage['role']
): number {
  return getMessagesByRole(state, role).length;
}

/**
 * Check if the last message is from an assistant and has content
 * @param state The current state
 * @returns True if the last message is a completed assistant response
 */
export function hasAssistantResponse<TState extends AgentState>(state: TState): boolean {
  const lastMessage = getLastMessage(state);
  return lastMessage?.role === 'assistant' && !!lastMessage.content;
}

/**
 * Get the conversation history as a formatted string
 * @param state The current state
 * @param includeSystem Whether to include system messages
 * @returns Formatted conversation string
 */
export function getConversationHistory<TState extends AgentState>(
  state: TState,
  includeSystem: boolean = false
): string {
  return state.messages
    .filter(msg => includeSystem || msg.role !== 'system')
    .map(msg => `${msg.role}: ${msg.content || '[tool_calls]'}`)
    .join('\n');
}

/**
 * Create a simple agent node that returns a static response
 * This is useful for testing or simple workflows
 * @param response The response content to return
 * @returns A node function that adds the response message
 */
export function createStaticAgentNode<TState extends AgentState>(
  response: string
): NodeFunction<TState> {
  return async (state: TState): Promise<Partial<TState>> => {
    const newMessage: MCPAssistantMessage = {
      role: 'assistant',
      content: response
    };
    
    return addMessage(state, newMessage);
  };
}

/**
 * Create a conditional agent node that responds based on state
 * @param responseFunction Function that generates response based on state
 * @returns A node function that calls the response function
 */
export function createConditionalAgentNode<TState extends AgentState>(
  responseFunction: (state: TState) => string | MCPAssistantMessage
): NodeFunction<TState> {
  return async (state: TState): Promise<Partial<TState>> => {
    const response = responseFunction(state);
    
    const message: MCPAssistantMessage = typeof response === 'string' 
      ? { role: 'assistant', content: response }
      : response;
    
    return addMessage(state, message);
  };
}
