import { beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { Agent, Graph, Memory, Tool, EvaluationSystem } from '../src/index.js';
import { AgentState } from '../src/core/types.js';
import {
  OpenRouterClient,
  MockOpenRouterClient,
  createOpenRouterClient,
  openRouterTestConfig
} from './providers/openrouter.js';

/**
 * Test utilities and setup for AG3NTIC framework tests
 */

// OpenRouter client for real LLM interactions
export let openRouterClient: OpenRouterClient;
export let useRealLLM: boolean = false;

// Mock LLM responses for testing (fallback when OpenRouter unavailable)
export const mockLLMResponses = {
  planning: "I need to break this task into steps: 1) Analyze requirements 2) Create plan 3) Execute",
  execution: "Executing the task with the provided tools and parameters",
  analysis: "Based on the data analysis, I found the following patterns and insights",
  orchestration: "Routing this task to the appropriate specialized agent",
  critique: "The response quality is good but could be improved in the following areas"
};

// Test state factory
export function createTestState(overrides: Partial<AgentState> = {}): AgentState {
  return {
    messages: [
      { role: 'user', content: 'Test message' }
    ],
    metadata: {
      agentName: 'test-agent',
      role: 'executor',
      threadId: 'test-thread-123',
      ...overrides.metadata
    },
    ...overrides
  };
}

// Mock tool for testing
export function createMockTool(name: string, response: string = 'Mock tool response'): Tool {
  return new Tool(
    {
      name,
      description: `Mock tool for testing: ${name}`,
      parameters: {
        type: 'object',
        properties: {
          input: { type: 'string' }
        },
        required: ['input']
      }
    },
    async (params) => {
      return `${response}: ${params.input}`;
    }
  );
}

// Mock memory for testing
export function createMockMemory(): Memory {
  return new Memory({
    type: 'buffer',
    maxMessages: 10
  });
}

// Mock evaluation system
export function createMockEvaluationSystem(): EvaluationSystem {
  return new EvaluationSystem({
    enableSelfEvaluation: true,
    enablePeerReview: false,
    enableHumanReview: false,
    metrics: ['accuracy', 'relevance', 'completeness']
  });
}

// Test data generators
export const testData = {
  simpleQuery: "What is the weather today?",
  complexQuery: "Analyze the sales data from last quarter and create a comprehensive report with recommendations",
  planningQuery: "Create a project plan for building a new mobile app",
  analysisQuery: "Analyze this dataset and provide insights: [1,2,3,4,5,6,7,8,9,10]",
  orchestrationQuery: "I need help with both data analysis and creating a presentation"
};

// Performance measurement utilities
export class PerformanceMeasurer {
  private startTime: number = 0;
  private measurements: Array<{ name: string; duration: number }> = [];

  start(): void {
    this.startTime = performance.now();
  }

  measure(name: string): number {
    const duration = performance.now() - this.startTime;
    this.measurements.push({ name, duration });
    return duration;
  }

  getResults(): Array<{ name: string; duration: number }> {
    return [...this.measurements];
  }

  reset(): void {
    this.measurements = [];
    this.startTime = 0;
  }
}

// Test assertion helpers
export function assertAgentResult(result: any, expectedProperties: string[] = []) {
  expect(result).toBeDefined();
  expect(result.finalOutput).toBeDefined();
  expect(result.finalState).toBeDefined();
  expect(result.steps).toBeDefined();
  expect(result.metrics).toBeDefined();
  
  expectedProperties.forEach(prop => {
    expect(result).toHaveProperty(prop);
  });
}

export function assertPerformance(duration: number, maxExpectedMs: number) {
  expect(duration).toBeLessThan(maxExpectedMs);
}

export function assertMemoryUsage(beforeHeap: number, afterHeap: number, maxIncreaseMB: number) {
  const increaseMB = (afterHeap - beforeHeap) / 1024 / 1024;
  expect(increaseMB).toBeLessThan(maxIncreaseMB);
}

// Global test setup
let globalPerformanceMeasurer: PerformanceMeasurer;

beforeAll(async () => {
  globalPerformanceMeasurer = new PerformanceMeasurer();

  // Initialize OpenRouter client
  try {
    openRouterClient = createOpenRouterClient(openRouterTestConfig.apiKey);

    // Test connection with a simple request
    const testResponse = await openRouterClient.generateCompletion([
      { role: 'user', content: 'Hello, this is a connection test.' }
    ]);

    if (testResponse && testResponse.length > 0) {
      useRealLLM = true;
      console.log('✅ OpenRouter connection established - using Kimi-K2 model');
    } else {
      throw new Error('Empty response from OpenRouter');
    }
  } catch (error) {
    console.warn('⚠️ OpenRouter connection failed, using mock responses:', error.message);
    openRouterClient = new MockOpenRouterClient(openRouterTestConfig);
    useRealLLM = false;
  }

  console.log('🚀 Starting AG3NTIC Framework Test Suite');
  console.log(`🤖 LLM Provider: ${useRealLLM ? 'OpenRouter (Kimi-K2)' : 'Mock Responses'}`);
});

afterAll(async () => {
  console.log('✅ AG3NTIC Framework Test Suite Completed');
  const results = globalPerformanceMeasurer.getResults();
  if (results.length > 0) {
    console.log('📊 Performance Summary:');
    results.forEach(({ name, duration }) => {
      console.log(`  ${name}: ${duration.toFixed(2)}ms`);
    });
  }
});

beforeEach(() => {
  globalPerformanceMeasurer.start();
});

afterEach(() => {
  // Clean up any test artifacts
  globalPerformanceMeasurer.reset();
});

export { globalPerformanceMeasurer };

// Enhanced agent factory with OpenRouter integration
export function createEnhancedAgent(config: {
  name: string;
  instructions: string;
  role?: string;
  tools?: Tool[];
  memory?: Memory;
  maxIterations?: number;
  enableSelfReflection?: boolean;
}): Agent {
  // Create base agent
  const agent = new Agent({
    ...config,
    maxIterations: config.maxIterations || 5,
  });

  // Override the run method to use OpenRouter
  const originalRun = agent.run.bind(agent);

  agent.run = async function(input: string | any, context?: Record<string, any>) {
    try {
      // If using real LLM, generate intelligent responses
      if (useRealLLM && typeof input === 'string') {
        const state = typeof input === 'string'
          ? { messages: [{ role: 'user', content: input }], metadata: { agentName: config.name } }
          : input;

        // Check if tools should be used
        const toolNames = config.tools?.map(t => t.name) || [];
        if (toolNames.length > 0) {
          const toolDecision = await openRouterClient.shouldUseTool(
            state,
            toolNames,
            config.instructions
          );

          if (toolDecision.shouldUse && toolDecision.toolName && config.tools) {
            const tool = config.tools.find(t => t.name === toolDecision.toolName);
            if (tool) {
              try {
                const toolResult = await tool.execute(
                  toolDecision.parameters || {},
                  { state, agentName: config.name }
                );

                if (toolResult.success) {
                  return {
                    finalOutput: toolResult.output,
                    finalState: state,
                    finalAgent: agent,
                    steps: [
                      { step: 'thought', content: 'Analyzing task and selecting appropriate tool', timestamp: Date.now() },
                      { step: 'action', content: `Using tool: ${toolDecision.toolName}`, timestamp: Date.now() },
                      { step: 'observation', content: toolResult.output, timestamp: Date.now() }
                    ],
                    metrics: {
                      totalSteps: 3,
                      toolCalls: 1,
                      executionTime: 500,
                    }
                  };
                }
              } catch (error) {
                console.warn(`Tool execution failed: ${error.message}`);
              }
            }
          }
        }

        // Generate response using OpenRouter
        const response = await openRouterClient.generateAgentResponse(
          state,
          config.instructions,
          context ? JSON.stringify(context) : undefined
        );

        return {
          finalOutput: response,
          finalState: {
            ...state,
            messages: [...state.messages, { role: 'assistant', content: response }]
          },
          finalAgent: agent,
          steps: [
            { step: 'thought', content: 'Processing request and generating response', timestamp: Date.now() },
            { step: 'observation', content: response, timestamp: Date.now() }
          ],
          metrics: {
            totalSteps: 2,
            toolCalls: 0,
            executionTime: 800,
          }
        };
      }

      // Fallback to original implementation for mock responses
      return originalRun(input, context);

    } catch (error) {
      console.warn(`Enhanced agent execution failed, falling back to mock: ${error.message}`);
      return originalRun(input, context);
    }
  };

  return agent;
}

// Mock implementations for testing without external dependencies
export class MockLLMProvider {
  async generateResponse(prompt: string, context?: any): Promise<string> {
    // Simple mock responses based on prompt content
    if (prompt.includes('plan') || prompt.includes('planning')) {
      return mockLLMResponses.planning;
    }
    if (prompt.includes('execute') || prompt.includes('execution')) {
      return mockLLMResponses.execution;
    }
    if (prompt.includes('analyze') || prompt.includes('analysis')) {
      return mockLLMResponses.analysis;
    }
    if (prompt.includes('route') || prompt.includes('orchestrat')) {
      return mockLLMResponses.orchestration;
    }
    if (prompt.includes('critique') || prompt.includes('evaluate')) {
      return mockLLMResponses.critique;
    }

    return "Mock LLM response for testing purposes";
  }
}

// Test configuration
export const testConfig = {
  timeout: 30000, // 30 seconds max per test
  performance: {
    maxExecutionTime: 1000, // 1 second max for most operations
    maxMemoryIncrease: 50, // 50MB max memory increase
  },
  agents: {
    maxIterations: 5,
    defaultTimeout: 5000,
  }
};

// Export commonly used test utilities
export {
  Agent,
  Graph,
  Memory,
  Tool,
  EvaluationSystem
};
