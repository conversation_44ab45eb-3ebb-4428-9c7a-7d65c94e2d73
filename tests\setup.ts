import { beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { Agent, Graph, Memory, Tool, EvaluationSystem } from '../src/index.js';
import { AgentState } from '../src/core/types.js';

/**
 * Test utilities and setup for AG3NTIC framework tests
 */

// Mock LLM responses for testing
export const mockLLMResponses = {
  planning: "I need to break this task into steps: 1) Analyze requirements 2) Create plan 3) Execute",
  execution: "Executing the task with the provided tools and parameters",
  analysis: "Based on the data analysis, I found the following patterns and insights",
  orchestration: "Routing this task to the appropriate specialized agent",
  critique: "The response quality is good but could be improved in the following areas"
};

// Test state factory
export function createTestState(overrides: Partial<AgentState> = {}): AgentState {
  return {
    messages: [
      { role: 'user', content: 'Test message' }
    ],
    metadata: {
      agentName: 'test-agent',
      role: 'executor',
      threadId: 'test-thread-123',
      ...overrides.metadata
    },
    ...overrides
  };
}

// Mock tool for testing
export function createMockTool(name: string, response: string = 'Mock tool response'): Tool {
  return new Tool(
    {
      name,
      description: `Mock tool for testing: ${name}`,
      parameters: {
        type: 'object',
        properties: {
          input: { type: 'string' }
        },
        required: ['input']
      }
    },
    async (params) => {
      return `${response}: ${params.input}`;
    }
  );
}

// Mock memory for testing
export function createMockMemory(): Memory {
  return new Memory({
    type: 'buffer',
    maxMessages: 10
  });
}

// Mock evaluation system
export function createMockEvaluationSystem(): EvaluationSystem {
  return new EvaluationSystem({
    enableSelfEvaluation: true,
    enablePeerReview: false,
    enableHumanReview: false,
    metrics: ['accuracy', 'relevance', 'completeness']
  });
}

// Test data generators
export const testData = {
  simpleQuery: "What is the weather today?",
  complexQuery: "Analyze the sales data from last quarter and create a comprehensive report with recommendations",
  planningQuery: "Create a project plan for building a new mobile app",
  analysisQuery: "Analyze this dataset and provide insights: [1,2,3,4,5,6,7,8,9,10]",
  orchestrationQuery: "I need help with both data analysis and creating a presentation"
};

// Performance measurement utilities
export class PerformanceMeasurer {
  private startTime: number = 0;
  private measurements: Array<{ name: string; duration: number }> = [];

  start(): void {
    this.startTime = performance.now();
  }

  measure(name: string): number {
    const duration = performance.now() - this.startTime;
    this.measurements.push({ name, duration });
    return duration;
  }

  getResults(): Array<{ name: string; duration: number }> {
    return [...this.measurements];
  }

  reset(): void {
    this.measurements = [];
    this.startTime = 0;
  }
}

// Test assertion helpers
export function assertAgentResult(result: any, expectedProperties: string[] = []) {
  expect(result).toBeDefined();
  expect(result.finalOutput).toBeDefined();
  expect(result.finalState).toBeDefined();
  expect(result.steps).toBeDefined();
  expect(result.metrics).toBeDefined();
  
  expectedProperties.forEach(prop => {
    expect(result).toHaveProperty(prop);
  });
}

export function assertPerformance(duration: number, maxExpectedMs: number) {
  expect(duration).toBeLessThan(maxExpectedMs);
}

export function assertMemoryUsage(beforeHeap: number, afterHeap: number, maxIncreaseMB: number) {
  const increaseMB = (afterHeap - beforeHeap) / 1024 / 1024;
  expect(increaseMB).toBeLessThan(maxIncreaseMB);
}

// Global test setup
let globalPerformanceMeasurer: PerformanceMeasurer;

beforeAll(async () => {
  globalPerformanceMeasurer = new PerformanceMeasurer();
  console.log('🚀 Starting AG3NTIC Framework Test Suite');
});

afterAll(async () => {
  console.log('✅ AG3NTIC Framework Test Suite Completed');
  const results = globalPerformanceMeasurer.getResults();
  if (results.length > 0) {
    console.log('📊 Performance Summary:');
    results.forEach(({ name, duration }) => {
      console.log(`  ${name}: ${duration.toFixed(2)}ms`);
    });
  }
});

beforeEach(() => {
  globalPerformanceMeasurer.start();
});

afterEach(() => {
  // Clean up any test artifacts
  globalPerformanceMeasurer.reset();
});

export { globalPerformanceMeasurer };

// Mock implementations for testing without external dependencies
export class MockLLMProvider {
  async generateResponse(prompt: string, context?: any): Promise<string> {
    // Simple mock responses based on prompt content
    if (prompt.includes('plan') || prompt.includes('planning')) {
      return mockLLMResponses.planning;
    }
    if (prompt.includes('execute') || prompt.includes('execution')) {
      return mockLLMResponses.execution;
    }
    if (prompt.includes('analyze') || prompt.includes('analysis')) {
      return mockLLMResponses.analysis;
    }
    if (prompt.includes('route') || prompt.includes('orchestrat')) {
      return mockLLMResponses.orchestration;
    }
    if (prompt.includes('critique') || prompt.includes('evaluate')) {
      return mockLLMResponses.critique;
    }
    
    return "Mock LLM response for testing purposes";
  }
}

// Test configuration
export const testConfig = {
  timeout: 30000, // 30 seconds max per test
  performance: {
    maxExecutionTime: 1000, // 1 second max for most operations
    maxMemoryIncrease: 50, // 50MB max memory increase
  },
  agents: {
    maxIterations: 5,
    defaultTimeout: 5000,
  }
};

// Export commonly used test utilities
export {
  Agent,
  Graph,
  Memory,
  Tool,
  EvaluationSystem
};
