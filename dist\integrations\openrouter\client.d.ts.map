{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../../src/integrations/openrouter/client.ts"], "names": [], "mappings": "AAEA,OAAO,EACL,UAAU,EACV,UAAU,EACV,mBAAmB,EACnB,YAAY,EACb,MAAM,kBAAkB,CAAC;AAG1B;;GAEG;AACH,MAAM,WAAW,gBAAgB;IAC/B,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,UAAU,CAAC;IACjB,QAAQ,EAAE;QACR,IAAI,EAAE,MAAM,CAAC;QACb,WAAW,EAAE,MAAM,CAAC;QACpB,UAAU,EAAE;YACV,IAAI,EAAE,QAAQ,CAAC;YACf,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YAChC,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;SACrB,CAAC;KACH,CAAC;CACH;AAED;;GAEG;AACH,MAAM,WAAW,sBAAsB;IACrC,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,KAAK,CAAC,EAAE,cAAc,EAAE,CAAC;CAC1B;AAED;;GAEG;AACH,qBAAa,gBAAgB;IAC3B,OAAO,CAAC,MAAM,CAA6B;gBAE/B,MAAM,EAAE,gBAAgB;IAUpC;;OAEG;IACG,oBAAoB,CACxB,QAAQ,EAAE,UAAU,EAAE,EACtB,OAAO,GAAE,sBAA2B,GACnC,OAAO,CAAC,mBAAmB,CAAC;IA8E/B;;OAEG;IACH,eAAe,CAAC,MAAM,SAAS,UAAU,EACvC,OAAO,GAAE,sBAA2B,GACnC,YAAY,CAAC,MAAM,CAAC;IAyBvB;;OAEG;IACG,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC;IAmBxC;;OAEG;IACG,kBAAkB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;CAmB9C"}