// src/templates/index.ts

// Export all agent templates
export * from './executor';
export * from './planner';
export * from './orchestrator';
export * from './research';

// Re-export key types for convenience
export type { 
  ExecutorAgentOptions, 
  ExecutorAgentState 
} from './executor';

export type { 
  PlannerAgentOptions, 
  PlannerAgentState, 
  PlanStep 
} from './planner';

export type { 
  OrchestratorAgentOptions, 
  OrchestratorAgentState, 
  WorkerAgent, 
  StepExecution 
} from './orchestrator';

export type { 
  ResearchAgentOptions, 
  ResearchAgentState, 
  ResearchSource, 
  ResearchFindings 
} from './research';
