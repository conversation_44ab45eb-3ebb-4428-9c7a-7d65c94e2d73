#!/usr/bin/env node

/**
 * AG3NTIC Framework Test Runner - REAL API ONLY
 * 
 * This script runs the AG3NTIC test suite with REAL OpenRouter API calls.
 * NO MOCK RESPONSES - Tests will fail if API is unavailable.
 * 
 * Requirements:
 * - Active internet connection
 * - Valid OpenRouter API key
 * - Kimi-K2 model access
 */

const { execSync } = require('child_process');
const chalk = require('chalk');

console.log(chalk.blue.bold('\n🚀 AG3NTIC Framework Test Suite - REAL API ONLY'));
console.log(chalk.blue('================================================\n'));

console.log(chalk.yellow('📋 Test Configuration:'));
console.log(chalk.white('  • Model: moonshotai/kimi-k2'));
console.log(chalk.white('  • API: OpenRouter (Real API calls only)'));
console.log(chalk.white('  • Fallbacks: NONE - Tests fail if API unavailable'));
console.log(chalk.white('  • Mock Responses: DISABLED\n'));

console.log(chalk.red.bold('⚠️  IMPORTANT: Tests require active OpenRouter API connection!'));
console.log(chalk.red('   If API is unavailable, tests will fail immediately.\n'));

// Check if user wants to continue
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question(chalk.cyan('Continue with real API tests? (y/N): '), (answer) => {
  rl.close();
  
  if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
    console.log(chalk.yellow('\n❌ Tests cancelled by user.'));
    process.exit(0);
  }

  console.log(chalk.green('\n✅ Starting real API tests...\n'));

  try {
    // Run the tests
    const testCommand = process.argv.includes('--demo') 
      ? 'npm run test:openrouter'
      : 'npm test';
    
    console.log(chalk.blue(`Running: ${testCommand}\n`));
    
    execSync(testCommand, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log(chalk.green.bold('\n🎉 All tests completed successfully!'));
    console.log(chalk.green('✅ AG3NTIC Framework validated with real LLM intelligence'));
    
  } catch (error) {
    console.log(chalk.red.bold('\n❌ Tests failed!'));
    
    if (error.message.includes('OpenRouter')) {
      console.log(chalk.red('🔌 OpenRouter API connection issue detected.'));
      console.log(chalk.yellow('💡 Troubleshooting steps:'));
      console.log(chalk.white('   1. Check internet connection'));
      console.log(chalk.white('   2. Verify API key is correct'));
      console.log(chalk.white('   3. Ensure OpenRouter service is available'));
      console.log(chalk.white('   4. Check API rate limits'));
    } else {
      console.log(chalk.red('Error details:'), error.message);
    }
    
    process.exit(1);
  }
});

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log(chalk.yellow('\n\n❌ Tests cancelled by user.'));
  process.exit(0);
});
