"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.globalToolRegistry = exports.ToolRegistry = exports.Tool = void 0;
exports.tool = tool;
exports.createToolMap = createToolMap;
const zod_1 = require("zod");
/**
 * Ultra-high-performance Tool implementation
 *
 * Features:
 * - Zod-based parameter validation
 * - Approval workflows
 * - Rate limiting and timeouts
 * - Streaming execution support
 * - Error handling and retries
 * - Performance monitoring
 */
class Tool {
    constructor(config, executeFunction, approvalFunction) {
        this.rateLimitMap = new Map();
        this.config = {
            needsApproval: false,
            timeout: 30000, // 30 seconds
            retries: 3,
            rateLimit: { requests: 100, window: 60000 }, // 100 requests per minute
            ...config,
        };
        this.executeFunction = executeFunction;
        this.approvalFunction = approvalFunction;
    }
    /**
     * Execute tool with full validation and error handling
     */
    async execute(params, context) {
        const startTime = Date.now();
        try {
            // Rate limiting check
            if (!this.checkRateLimit(context.userId || context.agentName)) {
                return {
                    success: false,
                    output: '',
                    error: 'Rate limit exceeded',
                };
            }
            // Parameter validation
            const validatedParams = this.validateParameters(params);
            if (!validatedParams.success) {
                return {
                    success: false,
                    output: '',
                    error: `Parameter validation failed: ${validatedParams.error}`,
                };
            }
            // Approval check
            if (this.config.needsApproval && this.approvalFunction) {
                const approval = await this.approvalFunction(validatedParams.data, context);
                if (approval !== 'approved') {
                    return {
                        success: false,
                        output: '',
                        error: `Tool execution ${approval}`,
                    };
                }
            }
            // Execute with timeout and retries
            const result = await this.executeWithRetries(validatedParams.data, context);
            return {
                success: true,
                output: result,
                metadata: {
                    executionTime: Date.now() - startTime,
                    retries: 0, // Would track actual retries
                },
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: error instanceof Error ? error.message : 'Unknown error',
                metadata: {
                    executionTime: Date.now() - startTime,
                },
            };
        }
    }
    /**
     * Validate parameters using Zod schema
     */
    validateParameters(params) {
        try {
            if (this.config.parameters instanceof zod_1.z.ZodSchema) {
                const result = this.config.parameters.safeParse(params);
                if (result.success) {
                    return { success: true, data: result.data };
                }
                else {
                    return {
                        success: false,
                        error: result.error.errors.map(e => e.message).join(', '),
                    };
                }
            }
            else {
                // Basic validation for non-Zod schemas
                return { success: true, data: params };
            }
        }
        catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Validation error',
            };
        }
    }
    /**
     * Execute with timeout and retry logic
     */
    async executeWithRetries(params, context) {
        let lastError = null;
        for (let attempt = 0; attempt <= this.config.retries; attempt++) {
            try {
                const result = await Promise.race([
                    this.executeFunction(params, context),
                    this.createTimeoutPromise(),
                ]);
                return typeof result === 'string' ? result : String(result);
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                // Don't retry on the last attempt
                if (attempt === this.config.retries) {
                    break;
                }
                // Exponential backoff
                await this.delay(Math.pow(2, attempt) * 1000);
            }
        }
        throw lastError || new Error('Tool execution failed');
    }
    /**
     * Create timeout promise
     */
    createTimeoutPromise() {
        return new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error(`Tool execution timed out after ${this.config.timeout}ms`));
            }, this.config.timeout);
        });
    }
    /**
     * Check rate limiting
     */
    checkRateLimit(identifier) {
        const now = Date.now();
        const windowStart = now - this.config.rateLimit.window;
        // Get existing requests for this identifier
        const requests = this.rateLimitMap.get(identifier) || [];
        // Filter out old requests
        const recentRequests = requests.filter(time => time > windowStart);
        // Check if under limit
        if (recentRequests.length >= this.config.rateLimit.requests) {
            return false;
        }
        // Add current request
        recentRequests.push(now);
        this.rateLimitMap.set(identifier, recentRequests);
        return true;
    }
    /**
     * Delay utility for retries
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    // Getters
    get name() {
        return this.config.name;
    }
    get description() {
        return this.config.description;
    }
    get parameters() {
        return this.config.parameters;
    }
    get needsApproval() {
        return this.config.needsApproval;
    }
}
exports.Tool = Tool;
/**
 * Tool registry for dynamic loading and management
 */
class ToolRegistry {
    constructor() {
        this.tools = new Map();
        this.categories = new Map();
    }
    /**
     * Register a tool
     */
    register(tool, category) {
        this.tools.set(tool.name, tool);
        if (category) {
            if (!this.categories.has(category)) {
                this.categories.set(category, new Set());
            }
            this.categories.get(category).add(tool.name);
        }
    }
    /**
     * Get tool by name
     */
    get(name) {
        return this.tools.get(name);
    }
    /**
     * Get tools by category
     */
    getByCategory(category) {
        const toolNames = this.categories.get(category);
        if (!toolNames)
            return [];
        return Array.from(toolNames)
            .map(name => this.tools.get(name))
            .filter((tool) => tool !== undefined);
    }
    /**
     * List all tools
     */
    list() {
        return Array.from(this.tools.values());
    }
    /**
     * Remove tool
     */
    unregister(name) {
        const removed = this.tools.delete(name);
        // Remove from categories
        for (const [category, toolNames] of this.categories) {
            toolNames.delete(name);
            if (toolNames.size === 0) {
                this.categories.delete(category);
            }
        }
        return removed;
    }
}
exports.ToolRegistry = ToolRegistry;
/**
 * Utility function to create a tool with Zod validation
 */
function tool(config, executeFunction, approvalFunction) {
    return new Tool(config, executeFunction, approvalFunction);
}
/**
 * Create a tool map from an array of tools
 */
function createToolMap(tools) {
    const map = new Map();
    tools.forEach(tool => map.set(tool.name, tool));
    return map;
}
/**
 * Global tool registry instance
 */
exports.globalToolRegistry = new ToolRegistry();
//# sourceMappingURL=tools.js.map