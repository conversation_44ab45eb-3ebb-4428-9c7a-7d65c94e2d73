{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/templates/research/index.ts"], "names": [], "mappings": ";AAAA,kCAAkC;;AA4FlC,kDAiKC;AA+ID,wDAQC;AAKD,oDAQC;AA/ZD,qCAA6D;AAC7D,mCAAiG;AAgFjG;;;;;;;;GAQG;AACH,SAAgB,mBAAmB,CACjC,UAAgC,EAAE;IAElC,MAAM,EACJ,KAAK,GAAG,0BAA0B,EAAE,EACpC,YAAY,EACZ,aAAa,GAAG,EAAE,EAClB,aAAa,GAAG,CAAC,EACjB,sBAAsB,GAAG,KAAK,EAC/B,GAAG,OAAO,CAAC;IAEZ,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI;;;;;;;;;;qFAUoC,CAAC;IAEpF,OAAO,CAAC,GAAG,CAAC,yCAAyC,aAAa,SAAS,aAAa,0BAA0B,sBAAsB,EAAE,CAAC,CAAC;IAC5I,OAAO,CAAC,GAAG,CAAC,mBAAmB,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;IAEpE,6BAA6B;IAC7B,MAAM,QAAQ,GAAG,IAAA,oBAAc,EAAS,KAAK,CAAC,CAAC;IAE/C,+BAA+B;IAC/B,MAAM,mBAAmB,GAAyB,KAAK,EAAE,KAAK,EAAE,EAAE;QAChE,MAAM,WAAW,GAAG,IAAA,oBAAc,EAAC,KAAK,CAAC,CAAC;QAE1C,sBAAsB;QACtB,IAAI,WAAW,EAAE,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC1D,MAAM,KAAK,GAAG,WAAW,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YAEvD,MAAM,gBAAgB,GAAG;gBACvB,IAAI,EAAE,WAAoB;gBAC1B,OAAO,EAAE,IAAI;gBACb,UAAU,EAAE,CAAC;wBACX,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;wBAC1B,IAAI,EAAE,UAAmB;wBACzB,QAAQ,EAAE;4BACR,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;gCACxB,KAAK,EAAE,KAAK;gCACZ,UAAU,EAAE,CAAC;6BACd,CAAC;yBACH;qBACF,CAAC;aACH,CAAC;YAEF,OAAO;gBACL,GAAG,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC;gBACtC,KAAK;gBACL,cAAc,EAAE,WAAoB;gBACpC,cAAc,EAAE,CAAC;gBACjB,OAAO,EAAE,EAAE;gBACX,aAAa,EAAE,CAAC,yBAAyB,KAAK,EAAE,CAAC;aAClD,CAAC;QACJ,CAAC;QAED,yBAAyB;QACzB,IAAI,WAAW,EAAE,IAAI,KAAK,MAAM,IAAI,KAAK,CAAC,cAAc,KAAK,WAAW,EAAE,CAAC;YACzE,IAAI,CAAC;gBACH,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACtD,MAAM,OAAO,GAAqB,aAAa,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,MAAW,EAAE,KAAa,EAAE,EAAE,CAAC,CAAC;oBAC5F,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,uBAAuB,KAAK,EAAE;oBACjD,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,UAAU,KAAK,GAAG,CAAC,EAAE;oBAC5C,IAAI,EAAE,KAAc;oBACpB,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,uBAAuB;oBACvE,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,sBAAsB;oBACjD,UAAU,EAAE,IAAI,IAAI,EAAE;iBACvB,CAAC,CAAC,IAAI,EAAE,CAAC;gBAEV,iEAAiE;gBACjE,MAAM,gBAAgB,GAAG,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;gBACzD,MAAM,iBAAiB,GAAG,gBAAgB,GAAG,aAAa;oBACjC,OAAO,CAAC,MAAM,GAAG,aAAa,GAAG,CAAC,CAAC;gBAE5D,IAAI,iBAAiB,EAAE,CAAC;oBACtB,+CAA+C;oBAC/C,MAAM,SAAS,GAAG,iBAAiB,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;oBAEhE,MAAM,gBAAgB,GAAG;wBACvB,IAAI,EAAE,WAAoB;wBAC1B,OAAO,EAAE,IAAI;wBACb,UAAU,EAAE,CAAC;gCACX,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;gCAC1B,IAAI,EAAE,UAAmB;gCACzB,QAAQ,EAAE;oCACR,IAAI,EAAE,WAAW;oCACjB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;wCACxB,KAAK,EAAE,SAAS;wCAChB,UAAU,EAAE,CAAC;qCACd,CAAC;iCACH;6BACF,CAAC;qBACH,CAAC;oBAEF,OAAO;wBACL,GAAG,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC;wBACtC,OAAO,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC;wBAC/C,cAAc,EAAE,gBAAgB;wBAChC,aAAa,EAAE;4BACb,GAAG,CAAC,KAAK,CAAC,aAAa,IAAI,EAAE,CAAC;4BAC9B,aAAa,gBAAgB,WAAW,OAAO,CAAC,MAAM,8BAA8B,SAAS,EAAE;yBAChG;qBACF,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,+BAA+B;oBAC/B,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,CAAC;oBAC1D,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,EAAE,UAAU,CAAC,CAAC;oBAEnE,MAAM,gBAAgB,GAAG;wBACvB,IAAI,EAAE,WAAoB;wBAC1B,OAAO,EAAE,oBAAoB,CAAC,QAAQ,CAAC;qBACxC,CAAC;oBAEF,OAAO;wBACL,GAAG,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC;wBACtC,OAAO,EAAE,UAAU;wBACnB,QAAQ;wBACR,cAAc,EAAE,WAAoB;wBACpC,iBAAiB,EAAE,0BAA0B,CAAC,QAAQ,CAAC;wBACvD,aAAa,EAAE;4BACb,GAAG,CAAC,KAAK,CAAC,aAAa,IAAI,EAAE,CAAC;4BAC9B,2BAA2B,UAAU,CAAC,MAAM,mBAAmB;yBAChE;qBACF,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,gBAAgB,GAAG;oBACvB,IAAI,EAAE,WAAoB;oBAC1B,OAAO,EAAE,oBAAoB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;iBACtF,CAAC;gBAEF,OAAO;oBACL,GAAG,IAAA,gBAAU,EAAC,KAAK,EAAE,gBAAgB,CAAC;oBACtC,cAAc,EAAE,QAAiB;iBAClC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC;IAEF,wCAAwC;IACxC,MAAM,iBAAiB,GAAG,YAAY,IAAI,mBAAmB,CAAC;IAE9D,6BAA6B;IAC7B,OAAO,IAAI,YAAK,EAAU;SACvB,OAAO,CAAC,YAAY,EAAE,iBAAiB,CAAC;SACxC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;SAC1B,aAAa,CAAC,YAAY,CAAC;SAC3B,kBAAkB,CAAC,YAAY,EAAE,qBAAe,EAAE;QACjD,OAAO,EAAE,OAAO;QAChB,SAAS,EAAE,SAAS;KACrB,CAAC;SACD,OAAO,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AACpC,CAAC;AAED;;GAEG;AACH,SAAS,0BAA0B;IACjC,OAAO;QACL,SAAS,EAAE,KAAK,EAAE,IAA4C,EAAE,EAAE;YAChE,MAAM,EAAE,KAAK,EAAE,UAAU,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;YAEvC,sBAAsB;YACtB,MAAM,WAAW,GAAG;gBAClB;oBACE,KAAK,EAAE,GAAG,KAAK,cAAc;oBAC7B,GAAG,EAAE,iCAAiC,kBAAkB,CAAC,KAAK,CAAC,EAAE;oBACjE,OAAO,EAAE,mCAAmC,KAAK,oBAAoB;iBACtE;gBACD;oBACE,KAAK,EAAE,GAAG,KAAK,kBAAkB;oBACjC,GAAG,EAAE,uCAAuC,kBAAkB,CAAC,KAAK,CAAC,EAAE;oBACvE,OAAO,EAAE,2CAA2C,KAAK,KAAK;iBAC/D;gBACD;oBACE,KAAK,EAAE,GAAG,KAAK,mBAAmB;oBAClC,GAAG,EAAE,oCAAoC,kBAAkB,CAAC,KAAK,CAAC,EAAE;oBACpE,OAAO,EAAE,sCAAsC,KAAK,KAAK;iBAC1D;aACF,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YAEvB,OAAO,IAAI,CAAC,SAAS,CAAC;gBACpB,KAAK;gBACL,OAAO,EAAE,WAAW;gBACpB,YAAY,EAAE,WAAW,CAAC,MAAM;aACjC,CAAC,CAAC;QACL,CAAC;QAED,aAAa,EAAE,KAAK,EAAE,IAAqB,EAAE,EAAE;YAC7C,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAErB,uBAAuB;YACvB,OAAO,IAAI,CAAC,SAAS,CAAC;gBACpB,GAAG;gBACH,KAAK,EAAE,gBAAgB,GAAG,EAAE;gBAC5B,OAAO,EAAE,qCAAqC,GAAG,4EAA4E;gBAC7H,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC,CAAC,CAAC;QACL,CAAC;QAED,gBAAgB,EAAE,KAAK,EAAE,IAA6C,EAAE,EAAE;YACxE,MAAM,EAAE,OAAO,EAAE,SAAS,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC;YAE1C,qBAAqB;YACrB,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,GAAG,SAAS;gBACxC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,KAAK;gBACzC,CAAC,CAAC,OAAO,CAAC;YAEZ,OAAO,IAAI,CAAC,SAAS,CAAC;gBACpB,cAAc,EAAE,OAAO,CAAC,MAAM;gBAC9B,OAAO;gBACP,SAAS,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC;aACzD,CAAC,CAAC;QACL,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,KAAa,EAAE,OAAyB;IACjE,MAAM,OAAO,GAAG;QACd,GAAG,KAAK,sBAAsB;QAC9B,GAAG,KAAK,mBAAmB;QAC3B,GAAG,KAAK,kBAAkB;QAC1B,GAAG,KAAK,eAAe;QACvB,GAAG,KAAK,iBAAiB;KAC1B,CAAC;IAEF,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AAClD,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,KAAa,EAAE,OAAyB;IAClE,MAAM,QAAQ,GAAG;QACf,GAAG,KAAK,kCAAkC;QAC1C,kCAAkC,KAAK,EAAE;QACzC,0BAA0B,KAAK,wBAAwB;KACxD,CAAC;IAEF,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,4BAA4B;IAElE,MAAM,QAAQ,GAAqB;QACjC,KAAK;QACL,QAAQ;QACR,QAAQ;QACR,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACxD,eAAe,EAAE,CAAC,4BAA4B,EAAE,yCAAyC,CAAC;KAC3F,CAAC;IAEF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvB,QAAQ,CAAC,IAAI,GAAG,CAAC,0BAA0B,EAAE,uBAAuB,CAAC,CAAC;IACxE,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAAC,QAA0B;IACtD,OAAO,sBAAsB,QAAQ,CAAC,KAAK;;;EAG3C,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;EAG3D,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,IAAI,kBAAkB,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;;;EAGnH,QAAQ,CAAC,UAAU;;EAEnB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,uBAAuB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;;EAE7F,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,yBAAyB,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;AAC1H,CAAC;AAED;;GAEG;AACH,SAAS,0BAA0B,CAAC,QAA0B;IAC5D,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,8BAA8B;IAClF,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,8BAA8B;IAClF,KAAK,IAAI,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,+BAA+B;IAEjE,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CACpC,aAAsB;IAEtB,MAAM,OAAO,GAAyB,EAAE,CAAC;IACzC,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;IACxC,CAAC;IACD,OAAO,mBAAmB,CAAS,OAAO,CAAC,CAAC;AAC9C,CAAC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAClC,gBAAwB,EAAE;IAE1B,OAAO,mBAAmB,CAAS;QACjC,aAAa;QACb,aAAa,EAAE,CAAC;QAChB,aAAa,EAAE,8IAA8I;KAC9J,CAAC,CAAC;AACL,CAAC"}