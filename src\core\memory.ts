import { AgentState, MCPMessage } from './types.js';

/**
 * Memory types for different storage strategies
 */
export type MemoryType = 'buffer' | 'window' | 'summary' | 'vector' | 'graph';

/**
 * Memory configuration
 */
export interface MemoryConfig {
  type: MemoryType;
  maxTokens?: number;
  maxMessages?: number;
  windowSize?: number;
  vectorStore?: VectorStore;
  persistenceAdapter?: PersistenceAdapter;
  ttl?: number; // Time to live in milliseconds
}

/**
 * Vector store interface for semantic memory
 */
export interface VectorStore {
  add(documents: MemoryDocument[]): Promise<void>;
  search(query: string, k?: number, filter?: Record<string, any>): Promise<MemoryDocument[]>;
  delete(ids: string[]): Promise<void>;
}

/**
 * Memory document for vector storage
 */
export interface MemoryDocument {
  id: string;
  content: string;
  metadata: Record<string, any>;
  embedding?: number[];
  timestamp: number;
}

/**
 * Persistence adapter for long-term storage
 */
export interface PersistenceAdapter {
  save(threadId: string, state: AgentState): Promise<void>;
  load(threadId: string): Promise<AgentState | null>;
  delete(threadId: string): Promise<void>;
  list(): Promise<string[]>;
}

/**
 * Memory checkpoint for state snapshots
 */
export interface MemoryCheckpoint {
  threadId: string;
  timestamp: number;
  state: AgentState;
  metadata?: Record<string, any>;
}

/**
 * Ultra-high-performance Memory implementation
 * 
 * Features:
 * - Multiple memory strategies (buffer, window, summary, vector, graph)
 * - Conversation persistence with checkpointing
 * - Vector-based semantic retrieval
 * - Time-aware memory with TTL
 * - Performance-optimized operations
 * - Thread isolation for multi-agent scenarios
 */
export class Memory<TState extends AgentState = AgentState> {
  private readonly config: Required<MemoryConfig>;
  private readonly cache = new Map<string, MemoryCheckpoint>();
  private readonly messageCache = new Map<string, MCPMessage[]>();

  constructor(config: MemoryConfig = { type: 'buffer' }) {
    this.config = {
      maxTokens: 4000,
      maxMessages: 50,
      windowSize: 10,
      ttl: 24 * 60 * 60 * 1000, // 24 hours
      ...config,
    };
  }

  /**
   * Load memory state for a thread
   */
  async load(state: TState): Promise<TState> {
    const threadId = this.getThreadId(state);
    
    try {
      // Try cache first for performance
      const cached = this.cache.get(threadId);
      if (cached && this.isValidCheckpoint(cached)) {
        return this.mergeStates(state, cached.state as TState);
      }

      // Load from persistence if available
      if (this.config.persistenceAdapter) {
        const persisted = await this.config.persistenceAdapter.load(threadId);
        if (persisted) {
          const checkpoint: MemoryCheckpoint = {
            threadId,
            timestamp: Date.now(),
            state: persisted,
          };
          this.cache.set(threadId, checkpoint);
          return this.mergeStates(state, persisted as TState);
        }
      }

      // Load vector memories if available
      if (this.config.vectorStore && state.messages.length > 0) {
        const recentMessages = state.messages.slice(-3);
        const query = recentMessages.map(m => m.content).join(' ');
        const memories = await this.config.vectorStore.search(query, 5, {
          threadId,
        });

        if (memories.length > 0) {
          const memoryContent = memories.map(m => m.content).join('\n');
          return {
            ...state,
            metadata: {
              ...state.metadata,
              retrievedMemories: memories,
              memoryContext: memoryContent,
            },
          };
        }
      }

      return state;
    } catch (error) {
      console.warn('Failed to load memory:', error);
      return state;
    }
  }

  /**
   * Save memory state for a thread
   */
  async save(state: TState): Promise<void> {
    const threadId = this.getThreadId(state);
    
    try {
      // Apply memory strategy
      const processedState = await this.applyMemoryStrategy(state);

      // Create checkpoint
      const checkpoint: MemoryCheckpoint = {
        threadId,
        timestamp: Date.now(),
        state: processedState,
      };

      // Cache for fast access
      this.cache.set(threadId, checkpoint);

      // Persist if adapter available
      if (this.config.persistenceAdapter) {
        await this.config.persistenceAdapter.save(threadId, processedState);
      }

      // Save to vector store for semantic retrieval
      if (this.config.vectorStore && processedState.messages.length > 0) {
        await this.saveToVectorStore(threadId, processedState);
      }

    } catch (error) {
      console.warn('Failed to save memory:', error);
    }
  }

  /**
   * Apply memory strategy to manage conversation length
   */
  private async applyMemoryStrategy(state: TState): Promise<TState> {
    switch (this.config.type) {
      case 'buffer':
        return this.applyBufferStrategy(state);
      case 'window':
        return this.applyWindowStrategy(state);
      case 'summary':
        return this.applySummaryStrategy(state);
      case 'vector':
        return this.applyVectorStrategy(state);
      case 'graph':
        return this.applyGraphStrategy(state);
      default:
        return state;
    }
  }

  /**
   * Buffer strategy: Keep all messages up to token limit
   */
  private applyBufferStrategy(state: TState): TState {
    if (state.messages.length <= this.config.maxMessages) {
      return state;
    }

    // Keep system message and recent messages
    const systemMessages = state.messages.filter(m => m.role === 'system');
    const otherMessages = state.messages.filter(m => m.role !== 'system');
    const recentMessages = otherMessages.slice(-this.config.maxMessages + systemMessages.length);

    return {
      ...state,
      messages: [...systemMessages, ...recentMessages],
    };
  }

  /**
   * Window strategy: Keep only recent messages
   */
  private applyWindowStrategy(state: TState): TState {
    const windowSize = this.config.windowSize || 10;
    const systemMessages = state.messages.filter(m => m.role === 'system');
    const recentMessages = state.messages.slice(-windowSize);

    return {
      ...state,
      messages: [...systemMessages, ...recentMessages],
    };
  }

  /**
   * Summary strategy: Summarize old messages
   */
  private async applySummaryStrategy(state: TState): Promise<TState> {
    // This would integrate with an LLM to create summaries
    // For now, return buffer strategy
    return this.applyBufferStrategy(state);
  }

  /**
   * Vector strategy: Store in vector database
   */
  private async applyVectorStrategy(state: TState): Promise<TState> {
    // Keep recent messages, store older ones in vector DB
    const recentMessages = state.messages.slice(-this.config.windowSize);
    return {
      ...state,
      messages: recentMessages,
    };
  }

  /**
   * Graph strategy: Build knowledge graph
   */
  private async applyGraphStrategy(state: TState): Promise<TState> {
    // This would build entity relationships
    // For now, return buffer strategy
    return this.applyBufferStrategy(state);
  }

  /**
   * Save messages to vector store for semantic retrieval
   */
  private async saveToVectorStore(threadId: string, state: TState): Promise<void> {
    if (!this.config.vectorStore) return;

    const documents: MemoryDocument[] = state.messages
      .filter(m => m.content && m.content.length > 10)
      .map((message, index) => ({
        id: `${threadId}_${Date.now()}_${index}`,
        content: message.content,
        metadata: {
          threadId,
          role: message.role,
          timestamp: Date.now(),
          agentName: state.metadata?.agentName,
        },
        timestamp: Date.now(),
      }));

    if (documents.length > 0) {
      await this.config.vectorStore.add(documents);
    }
  }

  /**
   * Get thread ID from state
   */
  private getThreadId(state: TState): string {
    return state.metadata?.threadId || 
           state.metadata?.sessionId || 
           state.metadata?.agentName || 
           'default';
  }

  /**
   * Check if checkpoint is still valid (not expired)
   */
  private isValidCheckpoint(checkpoint: MemoryCheckpoint): boolean {
    const now = Date.now();
    return (now - checkpoint.timestamp) < this.config.ttl;
  }

  /**
   * Merge two states, prioritizing new state
   */
  private mergeStates(newState: TState, oldState: TState): TState {
    return {
      ...oldState,
      ...newState,
      messages: [...(oldState.messages || []), ...(newState.messages || [])],
      metadata: {
        ...oldState.metadata,
        ...newState.metadata,
      },
    };
  }

  /**
   * Clear memory for a thread
   */
  async clear(threadId: string): Promise<void> {
    this.cache.delete(threadId);
    this.messageCache.delete(threadId);
    
    if (this.config.persistenceAdapter) {
      await this.config.persistenceAdapter.delete(threadId);
    }
  }

  /**
   * Get memory statistics
   */
  getStats(): {
    cacheSize: number;
    messageCacheSize: number;
    type: MemoryType;
    config: MemoryConfig;
  } {
    return {
      cacheSize: this.cache.size,
      messageCacheSize: this.messageCache.size,
      type: this.config.type,
      config: this.config,
    };
  }
}

/**
 * In-memory persistence adapter for development
 */
export class InMemoryPersistenceAdapter implements PersistenceAdapter {
  private readonly storage = new Map<string, AgentState>();

  async save(threadId: string, state: AgentState): Promise<void> {
    this.storage.set(threadId, JSON.parse(JSON.stringify(state)));
  }

  async load(threadId: string): Promise<AgentState | null> {
    const state = this.storage.get(threadId);
    return state ? JSON.parse(JSON.stringify(state)) : null;
  }

  async delete(threadId: string): Promise<void> {
    this.storage.delete(threadId);
  }

  async list(): Promise<string[]> {
    return Array.from(this.storage.keys());
  }
}

/**
 * Create memory with specific configuration
 */
export function createMemory<TState extends AgentState = AgentState>(
  config: MemoryConfig
): Memory<TState> {
  return new Memory<TState>(config);
}
