"use strict";
// src/core/helpers.ts
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLastMessage = getLastMessage;
exports.addMessage = addMessage;
exports.getToolCalls = getToolCalls;
exports.hasToolCalls = hasToolCalls;
exports.createToolNode = createToolNode;
exports.createAgentNode = createAgentNode;
exports.shouldCallTools = shouldCallTools;
exports.getMessagesByRole = getMessagesByRole;
exports.countMessagesByRole = countMessagesByRole;
/**
 * Get the last message from the state
 * @param state The current state
 * @returns The last message or undefined if no messages exist
 */
function getLastMessage(state) {
    return state.messages[state.messages.length - 1];
}
/**
 * Add a message to the state
 * @param state The current state
 * @param message The message to add
 * @returns A partial state update with the new message added
 */
function addMessage(state, message) {
    return {
        messages: [...state.messages, message]
    };
}
/**
 * Get tool calls from the last assistant message
 * @param state The current state
 * @returns Array of tool calls or empty array if none exist
 */
function getToolCalls(state) {
    const lastMessage = getLastMessage(state);
    if (lastMessage?.role === 'assistant' && lastMessage.tool_calls) {
        return lastMessage.tool_calls;
    }
    return [];
}
/**
 * Check if the last message contains tool calls
 * @param state The current state
 * @returns True if the last message has tool calls
 */
function hasToolCalls(state) {
    return getToolCalls(state).length > 0;
}
/**
 * Create a tool node that can execute a map of tools
 * @param tools Map of tool names to their executable functions
 * @returns A node function that can execute tools based on the last assistant message
 */
function createToolNode(tools) {
    return async (state) => {
        const lastMessage = getLastMessage(state);
        if (!lastMessage || lastMessage.role !== 'assistant' || !lastMessage.tool_calls) {
            throw new Error('Tool node called but last message is not an assistant message with tool calls');
        }
        const toolResults = [];
        for (const toolCall of lastMessage.tool_calls) {
            const { id, function: { name, arguments: argsString } } = toolCall;
            try {
                // Parse arguments
                const args = JSON.parse(argsString);
                // Find the tool function
                const toolFunction = tools[name];
                if (!toolFunction) {
                    throw new Error(`Tool "${name}" not found in available tools: ${Object.keys(tools).join(', ')}`);
                }
                // Execute the tool
                const result = await toolFunction(args);
                // Create tool result message
                toolResults.push({
                    role: 'tool',
                    tool_call_id: id,
                    content: typeof result === 'string' ? result : JSON.stringify(result)
                });
            }
            catch (error) {
                // Create error result message
                toolResults.push({
                    role: 'tool',
                    tool_call_id: id,
                    content: `Error executing tool "${name}": ${error instanceof Error ? error.message : String(error)}`
                });
            }
        }
        return {
            messages: [...state.messages, ...toolResults]
        };
    };
}
/**
 * Create an agent node that can call an LLM with tools
 * This is a simplified version - in a real implementation, this would integrate with specific LLM clients
 * @param _llmClient The LLM client (e.g., OpenAI instance) - currently unused in mock implementation
 * @param options Configuration options for the agent
 * @returns A node function that calls the LLM
 */
function createAgentNode(_llmClient, // This would be typed based on the specific LLM client
options = {}) {
    return async (state) => {
        const { tools = [], systemMessage } = options;
        // Prepare messages for the LLM
        const messages = [...state.messages];
        // Add system message if provided
        if (systemMessage && (messages.length === 0 || messages[0].role !== 'system')) {
            messages.unshift({
                role: 'system',
                content: systemMessage
            });
        }
        try {
            // This is a placeholder for the actual LLM call
            // In a real implementation, this would call the specific LLM client
            // For example, with OpenAI:
            // const response = await llmClient.chat.completions.create({
            //   model,
            //   messages,
            //   tools: tools.length > 0 ? tools.map(formatToolForOpenAI) : undefined,
            //   temperature
            // });
            // For now, we'll create a mock response
            const mockResponse = tools.length > 0 ? {
                role: 'assistant',
                content: null,
                tool_calls: [{
                        id: 'mock_call_1',
                        type: 'function',
                        function: {
                            name: tools[0].name,
                            arguments: JSON.stringify({})
                        }
                    }]
            } : {
                role: 'assistant',
                content: 'This is a mock response. In a real implementation, this would be the LLM response.'
            };
            return {
                messages: [...state.messages, mockResponse]
            };
        }
        catch (error) {
            throw new Error(`Error calling LLM: ${error instanceof Error ? error.message : String(error)}`);
        }
    };
}
/**
 * Simple routing function for agent -> tool -> agent loops
 * @param state The current state
 * @returns 'tools' if the last message has tool calls, '__end__' otherwise
 */
function shouldCallTools(state) {
    return hasToolCalls(state) ? 'tools' : '__end__';
}
/**
 * Get all messages of a specific role
 * @param state The current state
 * @param role The role to filter by
 * @returns Array of messages with the specified role
 */
function getMessagesByRole(state, role) {
    return state.messages.filter(message => message.role === role);
}
/**
 * Count messages by role
 * @param state The current state
 * @param role The role to count
 * @returns Number of messages with the specified role
 */
function countMessagesByRole(state, role) {
    return getMessagesByRole(state, role).length;
}
//# sourceMappingURL=helpers.js.map